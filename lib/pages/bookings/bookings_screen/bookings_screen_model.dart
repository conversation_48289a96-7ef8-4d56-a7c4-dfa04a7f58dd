import 'package:flutter/material.dart';
import '/flutter_flow/flutter_flow_util.dart';
import 'bookings_screen_widget.dart' show BookingsScreenWidget;

class BookingsScreenModel extends FlutterFlowModel<BookingsScreenWidget> {
  ///  State fields for stateful widgets in this page.

  // State field(s) for TextField widget.
  FocusNode? textFieldFocusNode;
  TextEditingController? textController;
  String? Function(BuildContext, String?)? textControllerValidator;

  // State field(s) for DatePicker widget.
  DateTime? selectedDate;
  bool isDateLoading = false;

  @override
  void initState(BuildContext context) {
    textFieldFocusNode = FocusNode();
    textController = TextEditingController();
  }

  @override
  void dispose() {
    textFieldFocusNode?.dispose();
    textController?.dispose();
  }
}
