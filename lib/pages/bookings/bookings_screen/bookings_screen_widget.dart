import 'package:easy_debounce/easy_debounce.dart';
import 'package:flutter/material.dart';
import 'package:unimetals_admin/auth/firebase_auth/auth_util.dart';
import 'package:unimetals_admin/backend/backend.dart';
import 'package:unimetals_admin/backend/schema/bookings_record.dart';
import 'package:unimetals_admin/backend/schema/enums/enums.dart';
import 'package:unimetals_admin/backend/schema/enums/menu_items.dart';
import 'package:unimetals_admin/pages/components/side_menu/side_menu_widget.dart';

import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import 'bookings_screen_model.dart';

export 'bookings_screen_model.dart';

class BookingsScreenWidget extends StatefulWidget {
  const BookingsScreenWidget({super.key});

  static String routeName = 'bookings_screen';
  static String routePath = '/bookingsScreen';

  @override
  State<BookingsScreenWidget> createState() => _BookingsScreenWidgetState();
}

class _BookingsScreenWidgetState extends State<BookingsScreenWidget> {
  late BookingsScreenModel _model;
  final scaffoldKey = GlobalKey<ScaffoldState>();
  List<BookingsRecord> searchResults = [];
  bool isSearching = false;
  final bool _isDateLoading = false;

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => BookingsScreenModel());
  }

  @override
  void dispose() {
    _model.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => FocusScope.of(context).unfocus(),
      child: Scaffold(
        key: scaffoldKey,
        backgroundColor: FlutterFlowTheme.of(context).primaryBackground,
        body: SafeArea(
          top: true,
          child: Row(
            mainAxisSize: MainAxisSize.max,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SideMenuWidget(selectedItem: MenuItem.bookings),
              Expanded(
                child: Align(
                  alignment: const AlignmentDirectional(0.0, -1.0),
                  child: Container(
                    width: double.infinity,
                    constraints: const BoxConstraints(
                      maxWidth: 1370.0,
                    ),
                    decoration: const BoxDecoration(),
                    child: Column(
                      mainAxisSize: MainAxisSize.max,
                      children: [
                        Padding(
                          padding: const EdgeInsetsDirectional.fromSTEB(16.0, 26.0, 16.0, 15.0),
                          child: Row(
                            mainAxisSize: MainAxisSize.max,
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Expanded(
                                child: Column(
                                  mainAxisSize: MainAxisSize.max,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Row(
                                      mainAxisSize: MainAxisSize.max,
                                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                      children: [
                                        Text(
                                          'Booking Management',
                                          style: FlutterFlowTheme.of(context).bodyMedium.override(
                                                fontFamily: 'Open Sans',
                                                color: FlutterFlowTheme.of(context).blackColor,
                                                fontSize: 26.0,
                                                letterSpacing: 0.0,
                                                fontWeight: FontWeight.w600,
                                              ),
                                        ),
                                      ],
                                    ),
                                    Padding(
                                      padding: const EdgeInsetsDirectional.fromSTEB(16.0, 16.0, 16.0, 0.0),
                                      child: Row(
                                        children: [
                                          // Search TextField
                                          Expanded(
                                            child: Container(
                                              height: 45.0,
                                              decoration: const BoxDecoration(),
                                              child: TextFormField(
                                                controller: _model.textController,
                                                focusNode: _model.textFieldFocusNode,
                                                onChanged: (_) => EasyDebounce.debounce(
                                                  '_model.textController',
                                                  const Duration(milliseconds: 2000),
                                                  () async {
                                                   setState(() => isSearching = true);

                                                    try {
                                                      // Get all bookings based on role
                                                      final query = FirebaseFirestore.instance
                                                          .collection('bookings')
                                                          .orderBy('createdAt', descending: true);

                                                      // Add role-based filtering
                                                      final QuerySnapshot bookingsSnapshot=await query
                                                          
                                                            .get();

                                                      // if (currentUserDocument?.role == Role.admin) {
                                                        // bookingsSnapshot = await query
                                                        //     .where('siteAdminId',
                                                        //         isEqualTo: currentUserDocument?.uid)
                                                        //     .get();
                                                      // } else {
                                                      //   bookingsSnapshot = await FirebaseFirestore.instance
                                                      //       .collection('bookings')
                                                      //       .where('siteAdminId',
                                                      //           isEqualTo: '')
                                                      //       .get();
                                                      // }

                                                      print(
                                                          'Number of bookings found: ${bookingsSnapshot.docs.length}');

                                                      final allBookings = bookingsSnapshot.docs
                                                          .map((doc) => BookingsRecord.fromFirestore(doc))
                                                          .toList();

                                                      // Create a list to store search matches
                                                      final List<BookingsRecord> matches = [];

                                                      // For each booking, fetch related user and site data
                                                      for (final booking in allBookings) {
                                                        final user = await UsersRecord.getDocumentOnce(
                                                          FirebaseFirestore.instance
                                                              .doc('users/${booking.userId}'),
                                                        );

                                                        final site = await SitesRecord.getDocumentOnce(
                                                          FirebaseFirestore.instance
                                                              .doc('sites/${booking.siteId}'),
                                                        );

                                                        // Check if any field matches the search query
                                                        final searchQuery =
                                                            _model.textController.text.toLowerCase();
                                                        if (user.displayName
                                                                .toLowerCase()
                                                                .contains(searchQuery) ||
                                                            user.email.toLowerCase().contains(searchQuery) ||
                                                            site.siteName
                                                                .toLowerCase()
                                                                .contains(searchQuery) ||
                                                            site.siteLocation
                                                                .toLowerCase()
                                                                .contains(searchQuery)) {
                                                          matches.add(booking);
                                                        }
                                                      }

                                                      if (mounted) {
                                                        setState(() {
                                                          searchResults = matches;
                                                          isSearching = false;
                                                        });
                                                      }
                                                    } catch (e) {
                                                      print('Error during search: $e');
                                                      if (mounted) {
                                                        setState(() {
                                                          searchResults = [];
                                                          isSearching = false;
                                                        });
                                                      }
                                                    }
                                                  },
                                                ),
                                                decoration: InputDecoration(
                                                  isDense: false,
                                                  filled: true,
                                                  fillColor: Colors.white,
                                                  prefixIcon: Icon(
                                                    Icons.search_rounded,
                                                    color: FlutterFlowTheme.of(context).secondaryText,
                                                    size: 20.0,
                                                  ),
                                                  suffixIcon: _model.textController.text.isNotEmpty
                                                      ? IconButton(
                                                          icon: Icon(
                                                            Icons.clear,
                                                            color: FlutterFlowTheme.of(context).secondaryText,
                                                            size: 20.0,
                                                          ),
                                                          onPressed: () {
                                                            _model.textController?.clear();
                                                            setState(() {
                                                              searchResults = [];
                                                              isSearching = false;
                                                            });
                                                          },
                                                        )
                                                      : null,
                                                  labelStyle:
                                                      FlutterFlowTheme.of(context).bodyMedium.override(
                                                            fontFamily: 'Jost',
                                                            color: FlutterFlowTheme.of(context).blackColor,
                                                            fontSize: 16.0,
                                                            letterSpacing: 0.0,
                                                            useGoogleFonts: false,
                                                          ),
                                                  hintText:
                                                      'Search by customer, email, site name, location...',
                                                  hintStyle:
                                                      FlutterFlowTheme.of(context).labelMedium.override(
                                                            fontFamily: 'Open Sans',
                                                            color: FlutterFlowTheme.of(context).secondaryText,
                                                            fontSize: 14.0,
                                                            letterSpacing: 0.0,
                                                          ),
                                                  contentPadding: const EdgeInsetsDirectional.fromSTEB(
                                                      16.0, 0.0, 16.0, 0.0),
                                                  enabledBorder: OutlineInputBorder(
                                                    borderSide: BorderSide(
                                                      color: FlutterFlowTheme.of(context).alternate,
                                                      width: 1.0,
                                                    ),
                                                    borderRadius: BorderRadius.circular(10.0),
                                                  ),
                                                  focusedBorder: OutlineInputBorder(
                                                    borderSide: BorderSide(
                                                      color: FlutterFlowTheme.of(context).primary,
                                                      width: 1.0,
                                                    ),
                                                    borderRadius: BorderRadius.circular(10.0),
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ),
                                          // Date Filter
                                          Padding(
                                            padding:
                                                const EdgeInsetsDirectional.fromSTEB(16.0, 0.0, 0.0, 0.0),
                                            child: Material(
                                              color: Colors.transparent,
                                              borderRadius: BorderRadius.circular(10.0),
                                              child: InkWell(
                                                borderRadius: BorderRadius.circular(10.0),
                                                onTap: () async {
                                                  final picked = await showDatePicker(
                                                    context: context,
                                                    initialDate: _model.selectedDate ?? DateTime.now(),
                                                    firstDate: DateTime(2000),
                                                    lastDate: DateTime(2101),
                                                    builder: (context, child) {
                                                      return Theme(
                                                        data: Theme.of(context).copyWith(
                                                          colorScheme: ColorScheme.light(
                                                            primary: FlutterFlowTheme.of(context).primary,
                                                            onPrimary: Colors.white,
                                                          ),
                                                        ),
                                                        child: child!,
                                                      );
                                                    },
                                                  );
                                                  if (picked != null && picked != _model.selectedDate) {
                                                    setState(() {
                                                      _model.isDateLoading = true;
                                                      _model.selectedDate = picked;
                                                    });

                                                    // Force a rebuild after a short delay
                                                    await Future.delayed(const Duration(milliseconds: 2000));

                                                    if (mounted) {
                                                      setState(() {
                                                        _model.isDateLoading = false;
                                                      });
                                                    }
                                                  }
                                                },
                                                child: Container(
                                                  height: 45.0,
                                                  decoration: BoxDecoration(
                                                    color: Colors.white,
                                                    borderRadius: BorderRadius.circular(10.0),
                                                    border: Border.all(
                                                      color: FlutterFlowTheme.of(context).alternate,
                                                      width: 1.0,
                                                    ),
                                                  ),
                                                  child: Padding(
                                                    padding: const EdgeInsetsDirectional.fromSTEB(
                                                        15.0, 0.0, 15.0, 0.0),
                                                    child: Row(
                                                      mainAxisSize: MainAxisSize.min,
                                                      children: [
                                                        Icon(
                                                          Icons.calendar_today_rounded,
                                                          color: FlutterFlowTheme.of(context).secondaryText,
                                                          size: 20.0,
                                                        ),
                                                        Padding(
                                                          padding: const EdgeInsetsDirectional.fromSTEB(
                                                              10.0, 0.0, 0.0, 0.0),
                                                          child: Text(
                                                            _model.selectedDate != null
                                                                ? DateFormat('MMM d, yyyy')
                                                                    .format(_model.selectedDate!)
                                                                : 'Filter by date',
                                                            style: FlutterFlowTheme.of(context)
                                                                .bodyMedium
                                                                .override(
                                                                  fontFamily: 'Open Sans',
                                                                  color: FlutterFlowTheme.of(context)
                                                                      .secondaryText,
                                                                  fontSize: 14.0,
                                                                  letterSpacing: 0.0,
                                                                ),
                                                          ),
                                                        ),
                                                        if (_model.selectedDate != null)
                                                          Padding(
                                                            padding: const EdgeInsetsDirectional.fromSTEB(
                                                                10.0, 0.0, 0.0, 0.0),
                                                            child: MouseRegion(
                                                              cursor: SystemMouseCursors.click,
                                                              child: GestureDetector(
                                                                onTap: () {
                                                                  setState(() {
                                                                    _model.selectedDate = null;
                                                                  });
                                                                },
                                                                child: Icon(
                                                                  Icons.close,
                                                                  color: FlutterFlowTheme.of(context)
                                                                      .secondaryText,
                                                                  size: 20.0,
                                                                ),
                                                              ),
                                                            ),
                                                          ),
                                                      ],
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                        Expanded(
                          child: Padding(
                            padding: const EdgeInsetsDirectional.fromSTEB(16.0, 16.0, 16.0, 0.0),
                            child: Container(
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(8.0),
                                border: Border.all(
                                  color: FlutterFlowTheme.of(context).alternate,
                                  width: 1.0,
                                ),
                              ),
                              child: Column(
                                children: [
                                  // Table Header - Always shown regardless of data state
                                  Container(
                                    decoration: BoxDecoration(
                                      color: FlutterFlowTheme.of(context).primaryBackground,
                                      borderRadius: const BorderRadius.only(
                                        topLeft: Radius.circular(8.0),
                                        topRight: Radius.circular(8.0),
                                      ),
                                    ),
                                    child: Padding(
                                      padding: const EdgeInsetsDirectional.fromSTEB(16.0, 12.0, 16.0, 12.0),
                                      child: Row(
                                        mainAxisSize: MainAxisSize.max,
                                        children: [
                                          Expanded(
                                            flex: 2,
                                            child: Text(
                                              'Customer',
                                              style: FlutterFlowTheme.of(context).labelSmall.override(
                                                    fontFamily: 'Open Sans',
                                                    letterSpacing: 0.0,
                                                    fontWeight: FontWeight.w600,
                                                  ),
                                            ),
                                          ),
                                          Expanded(
                                            flex: 2,
                                            child: Padding(
                                              padding: const EdgeInsets.only(left: 25.0),
                                              child: Text(
                                                'Site',
                                                style: FlutterFlowTheme.of(context).labelSmall.override(
                                                      fontFamily: 'Open Sans',
                                                      letterSpacing: 0.0,
                                                      fontWeight: FontWeight.w600,
                                                    ),
                                              ),
                                            ),
                                          ),
                                          Expanded(
                                            flex: 2,
                                            child: Padding(
                                              padding: const EdgeInsets.only(left: 10.0),
                                              child: Text(
                                                'Date & Time',
                                                style: FlutterFlowTheme.of(context).labelSmall.override(
                                                      fontFamily: 'Open Sans',
                                                      letterSpacing: 0.0,
                                                      fontWeight: FontWeight.w600,
                                                    ),
                                              ),
                                            ),
                                          ),
                                          Expanded(
                                            flex: 1,
                                            child: Padding(
                                              padding: const EdgeInsets.only(left: 25.0),
                                              child: Text(
                                                'Contact',
                                                style: FlutterFlowTheme.of(context).labelSmall.override(
                                                      fontFamily: 'Open Sans',
                                                      letterSpacing: 0.0,
                                                      fontWeight: FontWeight.w600,
                                                    ),
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                  // Content Area with StreamBuilder
                                  Expanded(
                                    child: StreamBuilder<List<BookingsRecord>>(
                                        stream: _model.textController.text.isEmpty
                                            ? queryBookingsRecord(
                                                queryBuilder: (query) {
                                                  var baseQuery =
                                                      query.orderBy('bookingDateTime', descending: false);

                                                  if (currentUserDocument?.role == Role.admin) {
                                                    baseQuery = query.where('siteAdminId',
                                                        isEqualTo: currentUserDocument?.uid);
                                                  }

                                                  if (_model.selectedDate != null) {
                                                    final startOfDay = DateTime(
                                                      _model.selectedDate!.year,
                                                      _model.selectedDate!.month,
                                                      _model.selectedDate!.day,
                                                    );
                                                    final endOfDay = startOfDay.add(const Duration(days: 1));

                                                    baseQuery = baseQuery.where('bookingDateTime',
                                                        isGreaterThanOrEqualTo:
                                                            Timestamp.fromDate(startOfDay),
                                                        isLessThan: Timestamp.fromDate(endOfDay));
                                                  }

                                                  return baseQuery;
                                                },
                                              )
                                            : Stream.value(searchResults),
                                        builder: (context, snapshot) {
                                          // Show loading indicator while searching or date filtering
                                          if (
                                            
                                             isSearching || 
                                            _model.isDateLoading) {
                                            return Center(
                                              child: Column(
                                                mainAxisSize: MainAxisSize.min,
                                                children: [
                                                  const CircularProgressIndicator(),
                                                  const SizedBox(height: 16),
                                                  Text(
                                                   isSearching ? 'Searching...' :
                                                     'Loading bookings...',
                                                    style: FlutterFlowTheme.of(context).bodyMedium,
                                                  ),
                                                ],
                                              ),
                                            );
                                          }

                                          // Show loading indicator while waiting for data
                                          if (snapshot.connectionState == ConnectionState.waiting) {
                                            return const Center(
                                              child: CircularProgressIndicator(),
                                            );
                                          }

                                          // Show error message if there's an error
                                          if (snapshot.hasError) {
                                            print(snapshot.error);
                                            return Center(
                                              child: Text(
                                                'Error: ${snapshot.error}',
                                                style: FlutterFlowTheme.of(context).bodyMedium.override(
                                                      fontFamily: 'Readex Pro',
                                                      color: FlutterFlowTheme.of(context).error,
                                                    ),
                                              ),
                                            );
                                          }

                                          final bookings = snapshot.data ?? [];

                                          // Show "No bookings found" if the list is empty
                                          if (bookings.isEmpty) {
                                            return Center(
                                              child: Text(
                                                'No bookings found',
                                                style: FlutterFlowTheme.of(context).bodyMedium,
                                              ),
                                            );
                                          }

                                          // Separate bookings into upcoming and previous
                                          final now = DateTime.now();
                                          final upcomingBookings = bookings
                                              .where(
                                                  (booking) => booking.bookingDateTime.toDate().isAfter(now))
                                              .toList();
                                          final previousBookings = bookings
                                              .where(
                                                  (booking) => booking.bookingDateTime.toDate().isBefore(now))
                                              .toList();

                                          return SingleChildScrollView(
                                            child: Column(
                                              crossAxisAlignment: CrossAxisAlignment.start,
                                              children: [
                                                // Upcoming Bookings Section
                                                if (upcomingBookings.isNotEmpty) ...[
                                                  Padding(
                                                    padding: const EdgeInsetsDirectional.fromSTEB(
                                                        16.0, 24.0, 16.0, 12.0),
                                                    child: Row(
                                                      children: [
                                                        Container(
                                                          decoration: BoxDecoration(
                                                            color: FlutterFlowTheme.of(context)
                                                                .primary
                                                                .withOpacity(0.1),
                                                            borderRadius: BorderRadius.circular(4.0),
                                                          ),
                                                          padding: const EdgeInsetsDirectional.fromSTEB(
                                                              12.0, 6.0, 12.0, 6.0),
                                                          child: Text(
                                                            'Upcoming Bookings',
                                                            style: FlutterFlowTheme.of(context)
                                                                .titleMedium
                                                                .override(
                                                                  fontFamily: 'Open Sans',
                                                                  color: FlutterFlowTheme.of(context).primary,
                                                                  fontWeight: FontWeight.w600,
                                                                  fontSize: 14,
                                                                ),
                                                          ),
                                                        ),
                                                        Expanded(
                                                          child: Padding(
                                                            padding: const EdgeInsetsDirectional.fromSTEB(
                                                                12.0, 0.0, 0.0, 0.0),
                                                            child: Container(
                                                              height: 1.0,
                                                              color: FlutterFlowTheme.of(context).alternate,
                                                            ),
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                  ListView.builder(
                                                    shrinkWrap: true,
                                                    physics: const NeverScrollableScrollPhysics(),
                                                    itemCount: upcomingBookings.length,
                                                    itemBuilder: (context, index) =>
                                                        buildBookingRow(context, upcomingBookings[index]),
                                                  ),
                                                ],

                                                // Previous Bookings Section
                                                if (previousBookings.isNotEmpty) ...[
                                                  Padding(
                                                    padding: const EdgeInsetsDirectional.fromSTEB(
                                                        16.0, 24.0, 16.0, 12.0),
                                                    child: Row(
                                                      children: [
                                                        Container(
                                                          decoration: BoxDecoration(
                                                            color: FlutterFlowTheme.of(context)
                                                                .secondaryBackground,
                                                            borderRadius: BorderRadius.circular(4.0),
                                                            border: Border.all(
                                                              color: FlutterFlowTheme.of(context).alternate,
                                                              width: 1.0,
                                                            ),
                                                          ),
                                                          padding: const EdgeInsetsDirectional.fromSTEB(
                                                              12.0, 6.0, 12.0, 6.0),
                                                          child: Text(
                                                            'Previous Bookings',
                                                            style: FlutterFlowTheme.of(context)
                                                                .titleMedium
                                                                .override(
                                                                  fontFamily: 'Open Sans',
                                                                  color: FlutterFlowTheme.of(context)
                                                                      .secondaryText,
                                                                  fontWeight: FontWeight.w600,
                                                                  fontSize: 14,
                                                                ),
                                                          ),
                                                        ),
                                                        Expanded(
                                                          child: Padding(
                                                            padding: const EdgeInsetsDirectional.fromSTEB(
                                                                12.0, 0.0, 0.0, 0.0),
                                                            child: Container(
                                                              height: 1.0,
                                                              color: FlutterFlowTheme.of(context).alternate,
                                                            ),
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                  ListView.builder(
                                                    shrinkWrap: true,
                                                    physics: const NeverScrollableScrollPhysics(),
                                                    itemCount: previousBookings.length,
                                                    itemBuilder: (context, index) =>
                                                        buildBookingRow(context, previousBookings[index]),
                                                  ),
                                                ],
                                              ],
                                            ),
                                          );
                                        }),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget buildBookingRow(BuildContext context, BookingsRecord booking) {
    final isUpcoming = booking.bookingDateTime.toDate().isAfter(DateTime.now());

    return FutureBuilder<UsersRecord>(
      future: UsersRecord.getDocumentOnce(
        FirebaseFirestore.instance.doc('users/${booking.userId}'),
      ),
      builder: (context, userSnapshot) {
        if (!userSnapshot.hasData) {
          return const SizedBox();
        }

        final user = userSnapshot.data!;

        return Padding(
          padding: const EdgeInsetsDirectional.fromSTEB(16.0, 8.0, 16.0, 8.0),
          child: Container(
            decoration: BoxDecoration(
              color: isUpcoming
                  ? FlutterFlowTheme.of(context).primary.withOpacity(0.02)
                  : FlutterFlowTheme.of(context).secondaryBackground,
              borderRadius: BorderRadius.circular(8.0),
              border: Border.all(
                color: isUpcoming
                    ? FlutterFlowTheme.of(context).primary.withOpacity(0.1)
                    : FlutterFlowTheme.of(context).alternate,
                width: 1.0,
              ),
              boxShadow: [
                BoxShadow(
                  color: FlutterFlowTheme.of(context).alternate.withOpacity(0.3),
                  offset: const Offset(0, 1),
                  blurRadius: 3,
                ),
              ],
            ),
            child: Padding(
              padding: const EdgeInsetsDirectional.fromSTEB(16.0, 12.0, 16.0, 12.0),
              child: Row(
                mainAxisSize: MainAxisSize.max,
                children: [
                  // Status indicator
                  Container(
                    width: 4,
                    height: 60,
                    decoration: BoxDecoration(
                      color: isUpcoming
                          ? FlutterFlowTheme.of(context).primary
                          : FlutterFlowTheme.of(context).secondaryText.withOpacity(0.3),
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                  const SizedBox(width: 12),
                  // Customer
                  Expanded(
                    flex: 2,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          user.displayName,
                          style: FlutterFlowTheme.of(context).bodyMedium.override(
                                fontFamily: 'Open Sans',
                                fontWeight: FontWeight.w600,
                                color: isUpcoming
                                    ? FlutterFlowTheme.of(context).primary
                                    : FlutterFlowTheme.of(context).secondaryText,
                              ),
                        ),
                        const SizedBox(height: 4),
                        Container(
                          padding: const EdgeInsetsDirectional.fromSTEB(6, 2, 6, 2),
                          decoration: BoxDecoration(
                            color: isUpcoming
                                ? FlutterFlowTheme.of(context).primary.withOpacity(0.08)
                                : FlutterFlowTheme.of(context).secondaryBackground,
                            borderRadius: BorderRadius.circular(4),
                            border: Border.all(
                              color: isUpcoming
                                  ? FlutterFlowTheme.of(context).primary.withOpacity(0.2)
                                  : FlutterFlowTheme.of(context).alternate,
                              width: 1,
                            ),
                          ),
                          child: Text(
                            isUpcoming ? 'Upcoming' : 'Previous',
                            style: FlutterFlowTheme.of(context).bodySmall.override(
                                  fontFamily: 'Open Sans',
                                  color: isUpcoming
                                      ? FlutterFlowTheme.of(context).primary
                                      : FlutterFlowTheme.of(context).secondaryText,
                                  fontSize: 11,
                                ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  // Site
                  Expanded(
                    flex: 2,
                    child: FutureBuilder<SitesRecord>(
                      future: SitesRecord.getDocumentOnce(
                        FirebaseFirestore.instance.doc('sites/${booking.siteId}'),
                      ),
                      builder: (context, siteSnapshot) {
                        if (!siteSnapshot.hasData) {
                          return const SizedBox();
                        }

                        final site = siteSnapshot.data!;

                        return Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            if (site.siteName.isNotEmpty)
                              Text(
                                site.siteName,
                                style: FlutterFlowTheme.of(context).bodyMedium.override(
                                      fontFamily: 'Open Sans',
                                      fontWeight: FontWeight.w600,
                                    ),
                              ),
                            const SizedBox(height: 4),
                            Row(
                              children: [
                                Icon(
                                  Icons.location_on_outlined,
                                  color: FlutterFlowTheme.of(context).secondaryText,
                                  size: 14,
                                ),
                                const SizedBox(width: 4),
                                Flexible(
                                  child: Text(
                                    site.siteLocation,
                                    style: FlutterFlowTheme.of(context).bodySmall.override(
                                          fontFamily: 'Open Sans',
                                          color: FlutterFlowTheme.of(context).secondaryText,
                                        ),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        );
                      },
                    ),
                  ),
                  // Date & Time
                  Expanded(
                    flex: 2,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(
                              Icons.calendar_today_outlined,
                              color: FlutterFlowTheme.of(context).primary,
                              size: 14,
                            ),
                            const SizedBox(width: 6),
                            Text(
                              DateFormat('MMM d, yyyy').format(booking.bookingDateTime.toDate()),
                              style: FlutterFlowTheme.of(context).bodyMedium.override(
                                    fontFamily: 'Open Sans',
                                    fontWeight: FontWeight.w600,
                                  ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 4),
                        Row(
                          children: [
                            Icon(
                              Icons.access_time,
                              color: FlutterFlowTheme.of(context).secondaryText,
                              size: 14,
                            ),
                            const SizedBox(width: 6),
                            Text(
                              DateFormat('h:mm a').format(booking.bookingDateTime.toDate()),
                              style: FlutterFlowTheme.of(context).bodySmall.override(
                                    fontFamily: 'Open Sans',
                                    color: FlutterFlowTheme.of(context).secondaryText,
                                  ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  // Contact
                  Expanded(
                    flex: 1,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(
                              Icons.email_outlined,
                              color: FlutterFlowTheme.of(context).secondaryText,
                              size: 14,
                            ),
                            const SizedBox(width: 6),
                            Flexible(
                              child: Text(
                                user.email,
                                style: FlutterFlowTheme.of(context).bodySmall.override(
                                      fontFamily: 'Open Sans',
                                      color: FlutterFlowTheme.of(context).secondaryText,
                                    ),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 4),
                        Row(
                          children: [
                            Icon(
                              Icons.phone_outlined,
                              color: FlutterFlowTheme.of(context).secondaryText,
                              size: 14,
                            ),
                            const SizedBox(width: 6),
                            Text(
                              user.phoneNumber,
                              style: FlutterFlowTheme.of(context).bodySmall.override(
                                    fontFamily: 'Open Sans',
                                    color: FlutterFlowTheme.of(context).secondaryText,
                                  ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
