import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:unimetals_admin/backend/schema/enums/enums.dart';

import '../../auth/firebase_auth/auth_util.dart';

const kUserPushNotificationsCollectionName = 'ff_user_push_notifications';

void triggerNotification({
  required String? notificationTitle,
  required String? notificationText,
  String? notificationImageUrl,
  DateTime? scheduledTime,
  String? notificationSound,
  required List<DocumentReference> userRefs,
  required String initialPageName,
  required Map<String, dynamic> parameterData,
}) {
  if ((notificationTitle ?? '').isEmpty || (notificationText ?? '').isEmpty) {
    return;
  }

  // Create a batch
  final batch = FirebaseFirestore.instance.batch();

  // Create documents references
  final pushNotificationRef =
      FirebaseFirestore.instance.collection(kUserPushNotificationsCollectionName).doc();
  final notificationDoc = FirebaseFirestore.instance.collection('notifications').doc();

  // Prepare push notification data
  final pushNotificationData = {
    'notification_title': notificationTitle,
    'notification_text': notificationText,
    if (notificationImageUrl != null) 'notification_image_url': notificationImageUrl,
    if (scheduledTime != null) 'scheduled_time': scheduledTime,
    if (notificationSound != null) 'notification_sound': notificationSound,
    'user_refs': userRefs.map((u) => u.path).join(','),
    'initial_page_name': initialPageName,
    'parameter_data': '{}',
    'sender': currentUserReference,
    'timestamp': DateTime.now(),
  };

  // Prepare app notification data
  final appPushNotificationData = {
    'notificationId': notificationDoc.id,
    'notificationTitle': notificationTitle,
    'notificationText': notificationText,
    'sendTo': userRefs.map((u) => u.id).toList(),
    'sendBy': Role.superAdmin.serialize(),
    'readByUsersCount': 0,
    'readBy': [],
    'createdAt': DateTime.now(),
  };

  // Add operations to batch
  batch.set(pushNotificationRef, pushNotificationData);
  batch.set(notificationDoc, appPushNotificationData);

  // Commit the batch
  batch.commit().then((_) {
    print('Notifications saved successfully');
  }).catchError((error) {
    print('Error saving notifications: $error');
  });
}
