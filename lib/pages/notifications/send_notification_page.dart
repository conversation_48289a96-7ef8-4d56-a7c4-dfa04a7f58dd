import 'package:flutter/material.dart';
import 'package:unimetals_admin/backend/schema/enums/enums.dart';
import 'package:unimetals_admin/backend/schema/enums/menu_items.dart';
import 'package:unimetals_admin/flutter_flow/flutter_flow_theme.dart';
import 'package:unimetals_admin/pages/components/side_menu/side_menu_widget.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:unimetals_admin/pages/notifications/push_notifications_util.dart';

class SendNotificationPage extends StatefulWidget {
  const SendNotificationPage({super.key});

  static String routeName = 'send_notification_screen';
  static String routePath = '/sendNotificationScreen';

  @override
  _SendNotificationPageState createState() => _SendNotificationPageState();
}

class _SendNotificationPageState extends State<SendNotificationPage> {
  final scaffoldKey = GlobalKey<ScaffoldState>();
  final titleController = TextEditingController();
  final descriptionController = TextEditingController();

  @override
  void dispose() {
    titleController.dispose();
    descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => FocusScope.of(context).unfocus(),
      child: Scaffold(
        key: scaffoldKey,
        backgroundColor: FlutterFlowTheme.of(context).primaryBackground,
        body: SafeArea(
          top: true,
          child: Row(
            mainAxisSize: MainAxisSize.max,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SideMenuWidget(selectedItem: MenuItem.notifications),
              Expanded(
                child: Align(
                  alignment: const AlignmentDirectional(0.0, -1.0),
                  child: Container(
                    width: double.infinity,
                    constraints: const BoxConstraints(
                      maxWidth: 1370.0,
                    ),
                    decoration: const BoxDecoration(),
                    child: Column(
                      mainAxisSize: MainAxisSize.max,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                          padding: const EdgeInsetsDirectional.fromSTEB(16.0, 26.0, 16.0, 15.0),
                          child: Row(
                            children: [
                              Text(
                                'Notification',
                                style: FlutterFlowTheme.of(context).bodyMedium.override(
                                      fontFamily: 'Open Sans',
                                      color: FlutterFlowTheme.of(context).blackColor,
                                      fontSize: 26.0,
                                      letterSpacing: 0.0,
                                      fontWeight: FontWeight.w600,
                                    ),
                              ),
                            ],
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsetsDirectional.fromSTEB(25.0, 20.0, 25.0, 16.0),
                          child: Container(
                            width: double.infinity,
                            decoration: BoxDecoration(
                              color: FlutterFlowTheme.of(context).secondaryBackground,
                              borderRadius: BorderRadius.circular(12.0),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withOpacity(0.05),
                                  blurRadius: 10,
                                  offset: const Offset(0, 2),
                                ),
                              ],
                            ),
                            child: Padding(
                              padding: const EdgeInsetsDirectional.fromSTEB(32.0, 24.0, 32.0, 24.0),
                              child: Column(
                                mainAxisSize: MainAxisSize.min,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'Notification Title',
                                    style: FlutterFlowTheme.of(context).bodyMedium.override(
                                          fontFamily: 'Open Sans',
                                          color: FlutterFlowTheme.of(context).primaryText,
                                          fontSize: 14.0,
                                          fontWeight: FontWeight.w600,
                                        ),
                                  ),
                                  const SizedBox(height: 8.0),
                                  TextFormField(
                                    controller: titleController,
                                    decoration: InputDecoration(
                                      hintText: 'Enter notification title',
                                      hintStyle: FlutterFlowTheme.of(context).bodyMedium.override(
                                            fontFamily: 'Open Sans',
                                            color: FlutterFlowTheme.of(context).secondaryText,
                                            fontSize: 14.0,
                                          ),
                                      enabledBorder: OutlineInputBorder(
                                        borderSide: BorderSide(
                                          color: FlutterFlowTheme.of(context).lightGrey,
                                          width: 1.0,
                                        ),
                                        borderRadius: BorderRadius.circular(8.0),
                                      ),
                                      focusedBorder: OutlineInputBorder(
                                        borderSide: BorderSide(
                                          color: FlutterFlowTheme.of(context).primary,
                                          width: 1.0,
                                        ),
                                        borderRadius: BorderRadius.circular(8.0),
                                      ),
                                      errorBorder: OutlineInputBorder(
                                        borderSide: BorderSide(
                                          color: FlutterFlowTheme.of(context).error,
                                          width: 1.0,
                                        ),
                                        borderRadius: BorderRadius.circular(8.0),
                                      ),
                                      focusedErrorBorder: OutlineInputBorder(
                                        borderSide: BorderSide(
                                          color: FlutterFlowTheme.of(context).error,
                                          width: 1.0,
                                        ),
                                        borderRadius: BorderRadius.circular(8.0),
                                      ),
                                      filled: true,
                                      fillColor: FlutterFlowTheme.of(context).secondaryBackground,
                                      contentPadding:
                                          const EdgeInsetsDirectional.fromSTEB(16.0, 16.0, 16.0, 16.0),
                                    ),
                                    style: FlutterFlowTheme.of(context).bodyMedium.override(
                                          fontFamily: 'Open Sans',
                                          fontSize: 14.0,
                                          fontWeight: FontWeight.w500,
                                        ),
                                  ),
                                  const SizedBox(height: 24.0),
                                  Text(
                                    'Notification Description',
                                    style: FlutterFlowTheme.of(context).bodyMedium.override(
                                          fontFamily: 'Open Sans',
                                          color: FlutterFlowTheme.of(context).primaryText,
                                          fontSize: 14.0,
                                          fontWeight: FontWeight.w600,
                                        ),
                                  ),
                                  const SizedBox(height: 8.0),
                                  TextFormField(
                                    controller: descriptionController,
                                    decoration: InputDecoration(
                                      hintText: 'Enter notification description',
                                      hintStyle: FlutterFlowTheme.of(context).bodyMedium.override(
                                            fontFamily: 'Open Sans',
                                            color: FlutterFlowTheme.of(context).secondaryText,
                                            fontSize: 14.0,
                                          ),
                                      enabledBorder: OutlineInputBorder(
                                        borderSide: BorderSide(
                                          color: FlutterFlowTheme.of(context).lightGrey,
                                          width: 1.0,
                                        ),
                                        borderRadius: BorderRadius.circular(8.0),
                                      ),
                                      focusedBorder: OutlineInputBorder(
                                        borderSide: BorderSide(
                                          color: FlutterFlowTheme.of(context).primary,
                                          width: 1.0,
                                        ),
                                        borderRadius: BorderRadius.circular(8.0),
                                      ),
                                      errorBorder: OutlineInputBorder(
                                        borderSide: BorderSide(
                                          color: FlutterFlowTheme.of(context).error,
                                          width: 1.0,
                                        ),
                                        borderRadius: BorderRadius.circular(8.0),
                                      ),
                                      focusedErrorBorder: OutlineInputBorder(
                                        borderSide: BorderSide(
                                          color: FlutterFlowTheme.of(context).error,
                                          width: 1.0,
                                        ),
                                        borderRadius: BorderRadius.circular(8.0),
                                      ),
                                      filled: true,
                                      fillColor: FlutterFlowTheme.of(context).secondaryBackground,
                                      contentPadding:
                                          const EdgeInsetsDirectional.fromSTEB(16.0, 16.0, 16.0, 16.0),
                                    ),
                                    style: FlutterFlowTheme.of(context).bodyMedium.override(
                                          fontFamily: 'Open Sans',
                                          fontSize: 14.0,
                                          fontWeight: FontWeight.w500,
                                        ),
                                    maxLines: 4,
                                    minLines: 4,
                                  ),
                                  const SizedBox(height: 32.0), // Increased spacing before buttons
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.end,
                                    children: [
                                      SizedBox(
                                        height: 48.0,
                                        child: TextButton(
                                          onPressed: () {
                                            titleController.clear();
                                            descriptionController.clear();
                                          },
                                          style: TextButton.styleFrom(
                                            padding:
                                                const EdgeInsetsDirectional.fromSTEB(32.0, 0.0, 32.0, 0.0),
                                            shape: RoundedRectangleBorder(
                                              borderRadius: BorderRadius.circular(8.0),
                                              side: BorderSide(
                                                color: FlutterFlowTheme.of(context).alternate,
                                                width: 2.0,
                                              ),
                                            ),
                                          ),
                                          child: Text(
                                            'Clear',
                                            style: FlutterFlowTheme.of(context).bodyMedium.override(
                                                  fontFamily: 'Open Sans',
                                                  color: FlutterFlowTheme.of(context).primaryText,
                                                  fontSize: 14.0,
                                                  fontWeight: FontWeight.w600,
                                                ),
                                          ),
                                        ),
                                      ),
                                      const SizedBox(width: 16.0),
                                      Container(
                                        height: 48.0,
                                        child: ElevatedButton(
                                          onPressed: () async {
                                            if (titleController.text.isEmpty ||
                                                descriptionController.text.isEmpty) {
                                              ScaffoldMessenger.of(context).showSnackBar(
                                                SnackBar(
                                                  content: Text('Please fill in both title and description'),
                                                  backgroundColor: FlutterFlowTheme.of(context).error,
                                                ),
                                              );
                                              return;
                                            }

                                            try {
                                              triggerNotification(
                                                notificationTitle: titleController.text,
                                                notificationText: descriptionController.text,
                                                notificationSound: 'default',
                                                userRefs: await FirebaseFirestore.instance
                                                    .collection('users')
                                                    .where('role', isEqualTo: Role.user.serialize())
                                                    .get()
                                                    .then((snapshot) =>
                                                        snapshot.docs.map((doc) => doc.reference).toList()),
                                                initialPageName: 'notification_screen',
                                                parameterData: {},
                                              );

                                              // Clear fields after successful save
                                              setState(() {
                                                titleController.clear();
                                                descriptionController.clear();
                                              });

                                              ScaffoldMessenger.of(context).showSnackBar(
                                                SnackBar(
                                                  content: Text('Notification sent successfully'),
                                                  backgroundColor: FlutterFlowTheme.of(context).success,
                                                ),
                                              );
                                            } catch (e) {
                                              ScaffoldMessenger.of(context).showSnackBar(
                                                SnackBar(
                                                  content: Text('Error sending notification: $e'),
                                                  backgroundColor: FlutterFlowTheme.of(context).error,
                                                ),
                                              );
                                            }
                                          },
                                          style: ElevatedButton.styleFrom(
                                            padding:
                                                const EdgeInsetsDirectional.fromSTEB(32.0, 0.0, 32.0, 0.0),
                                            backgroundColor: FlutterFlowTheme.of(context).primary,
                                            foregroundColor: Colors.white,
                                            elevation: 0,
                                            shape: RoundedRectangleBorder(
                                              borderRadius: BorderRadius.circular(8.0),
                                            ),
                                          ),
                                          child: Text(
                                            'Send Notification',
                                            style: FlutterFlowTheme.of(context).titleSmall.override(
                                                  fontFamily: 'Open Sans',
                                                  color: Colors.white,
                                                  fontSize: 14.0,
                                                  fontWeight: FontWeight.w600,
                                                ),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
