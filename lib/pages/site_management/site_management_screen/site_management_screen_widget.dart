import 'package:easy_debounce/easy_debounce.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:text_search/text_search.dart';
import 'package:unimetals_admin/auth/firebase_auth/auth_util.dart';
import 'package:unimetals_admin/backend/schema/enums/enums.dart';
import 'package:unimetals_admin/backend/schema/enums/menu_items.dart';
import 'package:unimetals_admin/pages/components/side_menu/side_menu_widget.dart';
import 'package:unimetals_admin/pages/site_management/add_new_site_dialog/add_new_site_dialog_widget.dart';

import '/actions/actions.dart' as action_blocks;
import '/backend/backend.dart';
import '/components/opening_hrs_component_widget.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/flutter_flow/flutter_flow_widgets.dart';
import '/index.dart';
import '/pages/components/empty_list_component/empty_list_component_widget.dart';
import '../delete_site_dialog/delete_site_dialog_widget.dart';
import 'site_management_screen_model.dart';
import '/custom_code/actions/index.dart' as actions;

export 'site_management_screen_model.dart';

class SiteManagementScreenWidget extends StatefulWidget {
  const SiteManagementScreenWidget({super.key});

  static String routeName = 'site_management_screen';
  static String routePath = '/siteManagementScreen';

  @override
  State<SiteManagementScreenWidget> createState() => _SiteManagementScreenWidgetState();
}

class _SiteManagementScreenWidgetState extends State<SiteManagementScreenWidget> {
  late SiteManagementScreenModel _model;

  final scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => SiteManagementScreenModel());

    _model.textController ??= TextEditingController();
    _model.textFieldFocusNode ??= FocusNode();

    WidgetsBinding.instance.addPostFrameCallback((_) => safeSetState(() {}));
  }

  @override
  void dispose() {
    _model.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).unfocus();
        FocusManager.instance.primaryFocus?.unfocus();
      },
      child: Scaffold(
        key: scaffoldKey,
        backgroundColor: FlutterFlowTheme.of(context).primaryBackground,
        body: SafeArea(
          top: true,
          child: Row(
            mainAxisSize: MainAxisSize.max,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SideMenuWidget(selectedItem: MenuItem.siteManagement),
              Expanded(
                child: Align(
                  alignment: const AlignmentDirectional(0.0, -1.0),
                  child: Container(
                    width: double.infinity,
                    constraints: const BoxConstraints(
                      maxWidth: 1370.0,
                    ),
                    decoration: const BoxDecoration(),
                    child: Column(
                      mainAxisSize: MainAxisSize.max,
                      children: [
                        Padding(
                          padding: const EdgeInsetsDirectional.fromSTEB(16.0, 26.0, 16.0, 15.0),
                          child: Row(
                            mainAxisSize: MainAxisSize.max,
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Expanded(
                                child: Column(
                                  mainAxisSize: MainAxisSize.max,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Row(
                                      mainAxisSize: MainAxisSize.max,
                                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                      children: [
                                        Text(
                                          'Site Management',
                                          style: FlutterFlowTheme.of(context).bodyMedium.override(
                                                fontFamily: 'Open Sans',
                                                color: FlutterFlowTheme.of(context).blackColor,
                                                fontSize: 26.0,
                                                letterSpacing: 0.0,
                                                fontWeight: FontWeight.w600,
                                              ),
                                        ),
                                        Builder(builder: (context) {
                                          if (currentUserDocument?.role == Role.superAdmin) {
                                            return FFButtonWidget(
                                              onPressed: () async {
                                                await showDialog(
                                                  context: context,
                                                  builder: (dialogContext) {
                                                    return Dialog(
                                                      elevation: 0,
                                                      insetPadding: EdgeInsets.zero,
                                                      backgroundColor: Colors.transparent,
                                                      alignment: const AlignmentDirectional(0.0, 0.0)
                                                          .resolve(Directionality.of(context)),
                                                      child: GestureDetector(
                                                        onTap: () {
                                                          FocusScope.of(dialogContext).unfocus();
                                                          FocusManager.instance.primaryFocus?.unfocus();
                                                        },
                                                        child: const AddNewSiteDialogWidget(),
                                                      ),
                                                    );
                                                  },
                                                );
                                              },
                                              text: 'Add New Site',
                                              options: FFButtonOptions(
                                                height: 40.0,
                                                padding: const EdgeInsetsDirectional.fromSTEB(
                                                    16.0, 0.0, 16.0, 0.0),
                                                iconPadding:
                                                    const EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 0.0, 0.0),
                                                color: FlutterFlowTheme.of(context).secondary,
                                                textStyle: FlutterFlowTheme.of(context).titleSmall.override(
                                                      fontFamily: 'Open Sans',
                                                      color: Colors.white,
                                                      letterSpacing: 0.0,
                                                      fontWeight: FontWeight.bold,
                                                    ),
                                                elevation: 0.0,
                                                borderRadius: BorderRadius.circular(10.0),
                                              ),
                                            );
                                          } else {
                                            return const SizedBox.shrink();
                                          }
                                        }),
                                      ],
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                        Expanded(
                          child: Padding(
                            padding: const EdgeInsetsDirectional.fromSTEB(16.0, 16.0, 15.0, 0.0),
                            child: Container(
                              width: double.infinity,
                              constraints: const BoxConstraints(
                                maxWidth: 1370.0,
                              ),
                              decoration: BoxDecoration(
                                color: FlutterFlowTheme.of(context).secondaryBackground,
                                boxShadow: const [
                                  BoxShadow(
                                    blurRadius: 3.0,
                                    color: Color(0x33000000),
                                    offset: Offset(
                                      0.0,
                                      1.0,
                                    ),
                                  )
                                ],
                                borderRadius: BorderRadius.circular(8.0),
                                border: Border.all(
                                  color: FlutterFlowTheme.of(context).alternate,
                                  width: 1.0,
                                ),
                              ),
                              child: Padding(
                                padding: const EdgeInsets.all(16.0),
                                child: Column(
                                  mainAxisSize: MainAxisSize.max,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Row(
                                      mainAxisSize: MainAxisSize.max,
                                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                      children: [
                                        Expanded(
                                          child: Column(
                                            mainAxisSize: MainAxisSize.max,
                                            crossAxisAlignment: CrossAxisAlignment.start,
                                            children: [
                                              Padding(
                                                padding:
                                                    const EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 12.0, 0.0),
                                                child: Text(
                                                  'Sites',
                                                  style: FlutterFlowTheme.of(context).headlineMedium.override(
                                                        fontFamily: 'Open Sans',
                                                        fontSize: 22.0,
                                                        letterSpacing: 0.0,
                                                      ),
                                                ),
                                              ),
                                              Padding(
                                                padding:
                                                    const EdgeInsetsDirectional.fromSTEB(0.0, 4.0, 12.0, 0.0),
                                                child: Text(
                                                  'Here is the list of all added sites',
                                                  style: FlutterFlowTheme.of(context).labelMedium.override(
                                                        fontFamily: 'Jost',
                                                        letterSpacing: 0.0,
                                                        useGoogleFonts: false,
                                                      ),
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                        Container(
                                          height: 45.0,
                                          decoration: const BoxDecoration(),
                                          child: SizedBox(
                                            width: 250.0,
                                            child: TextFormField(
                                              controller: _model.textController,
                                              focusNode: _model.textFieldFocusNode,
                                              onChanged: (_) => EasyDebounce.debounce(
                                                '_model.textController',
                                                const Duration(milliseconds: 2000),
                                                () async {
                                                  await querySitesRecordOnce()
                                                      .then(
                                                        (records) => _model.simpleSearchResults = TextSearch(
                                                          records
                                                              .map(
                                                                (record) => TextSearchItem.fromTerms(
                                                                    record, [record.siteLocation]),
                                                              )
                                                              .toList(),
                                                        )
                                                            .search(_model.textController.text)
                                                            .map((r) => r.object)
                                                            .toList(),
                                                      )
                                                      .onError((_, __) => _model.simpleSearchResults = [])
                                                      .whenComplete(() => safeSetState(() {}));
                                                },
                                              ),
                                              autofocus: false,
                                              obscureText: false,
                                              decoration: InputDecoration(
                                                isDense: false,
                                                labelStyle: FlutterFlowTheme.of(context).bodyMedium.override(
                                                      fontFamily: 'Jost',
                                                      color: FlutterFlowTheme.of(context).blackColor,
                                                      fontSize: 16.0,
                                                      letterSpacing: 0.0,
                                                      useGoogleFonts: false,
                                                    ),
                                                alignLabelWithHint: false,
                                                hintText: 'Search by location',
                                                hintStyle: FlutterFlowTheme.of(context).labelMedium.override(
                                                      fontFamily: 'Open Sans',
                                                      color: FlutterFlowTheme.of(context).secondaryText,
                                                      fontSize: 12.0,
                                                      letterSpacing: 0.0,
                                                    ),
                                                enabledBorder: OutlineInputBorder(
                                                  borderSide: const BorderSide(
                                                    color: Color(0x00000000),
                                                    width: 1.0,
                                                  ),
                                                  borderRadius: BorderRadius.circular(10.0),
                                                ),
                                                focusedBorder: OutlineInputBorder(
                                                  borderSide: const BorderSide(
                                                    color: Color(0x00000000),
                                                    width: 1.0,
                                                  ),
                                                  borderRadius: BorderRadius.circular(10.0),
                                                ),
                                                errorBorder: OutlineInputBorder(
                                                  borderSide: BorderSide(
                                                    color: FlutterFlowTheme.of(context).error,
                                                    width: 1.0,
                                                  ),
                                                  borderRadius: BorderRadius.circular(10.0),
                                                ),
                                                focusedErrorBorder: OutlineInputBorder(
                                                  borderSide: BorderSide(
                                                    color: FlutterFlowTheme.of(context).error,
                                                    width: 1.0,
                                                  ),
                                                  borderRadius: BorderRadius.circular(10.0),
                                                ),
                                                filled: true,
                                                fillColor: FlutterFlowTheme.of(context).primaryBackground,
                                                contentPadding: const EdgeInsetsDirectional.fromSTEB(
                                                    15.0, 20.0, 15.0, 30.0),
                                                suffixIcon: Icon(
                                                  Icons.search_rounded,
                                                  color: FlutterFlowTheme.of(context).secondaryText,
                                                ),
                                              ),
                                              style: FlutterFlowTheme.of(context).bodyMedium.override(
                                                    fontFamily: 'Jost',
                                                    color: FlutterFlowTheme.of(context).blackColor,
                                                    fontSize: 14.0,
                                                    letterSpacing: 0.0,
                                                    useGoogleFonts: false,
                                                  ),
                                              cursorColor: FlutterFlowTheme.of(context).primaryText,
                                              validator: _model.textControllerValidator.asValidator(context),
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                    Padding(
                                      padding: const EdgeInsetsDirectional.fromSTEB(0.0, 16.0, 0.0, 0.0),
                                      child: Container(
                                        width: double.infinity,
                                        height: 40.0,
                                        decoration: BoxDecoration(
                                          color: FlutterFlowTheme.of(context).primaryBackground,
                                          borderRadius: const BorderRadius.only(
                                            bottomLeft: Radius.circular(0.0),
                                            bottomRight: Radius.circular(0.0),
                                            topLeft: Radius.circular(8.0),
                                            topRight: Radius.circular(8.0),
                                          ),
                                        ),
                                        child: Padding(
                                          padding: const EdgeInsetsDirectional.fromSTEB(16.0, 0.0, 0.0, 0.0),
                                          child: Row(
                                            mainAxisSize: MainAxisSize.max,
                                            children: [
                                              Expanded(
                                                flex: 1,
                                                child: Text(
                                                  'Id#',
                                                  style: FlutterFlowTheme.of(context).labelSmall.override(
                                                        fontFamily: 'Open Sans',
                                                        letterSpacing: 0.0,
                                                        fontWeight: FontWeight.w600,
                                                      ),
                                                ),
                                              ),
                                              Expanded(
                                                flex: 1,
                                                child: Text(
                                                  'Location',
                                                  style: FlutterFlowTheme.of(context).labelSmall.override(
                                                        fontFamily: 'Open Sans',
                                                        letterSpacing: 0.0,
                                                        fontWeight: FontWeight.w600,
                                                      ),
                                                ),
                                              ),
                                              Expanded(
                                                flex: 1,
                                                child: Text(
                                                  'Name',
                                                  style: FlutterFlowTheme.of(context).labelSmall.override(
                                                        fontFamily: 'Open Sans',
                                                        letterSpacing: 0.0,
                                                        fontWeight: FontWeight.w600,
                                                      ),
                                                ),
                                              ),
                                              Expanded(
                                                flex: 2,
                                                child: Text(
                                                  'Opening Hours',
                                                  style: FlutterFlowTheme.of(context).labelSmall.override(
                                                        fontFamily: 'Open Sans',
                                                        letterSpacing: 0.0,
                                                        fontWeight: FontWeight.w600,
                                                      ),
                                                ),
                                              ),
                                              currentUserDocument?.role == Role.superAdmin
                                                  ? Padding(
                                                      padding: const EdgeInsetsDirectional.fromSTEB(
                                                          25.0, 0.0, 25.0, 0.0),
                                                      child: Text(
                                                        'Actions',
                                                        textAlign: TextAlign.center,
                                                        style:
                                                            FlutterFlowTheme.of(context).labelSmall.override(
                                                                  fontFamily: 'Open Sans',
                                                                  letterSpacing: 0.0,
                                                                  fontWeight: FontWeight.w600,
                                                                ),
                                                      ),
                                                    )
                                                  : const SizedBox.shrink(),
                                            ],
                                          ),
                                        ),
                                      ),
                                    ),
                                    Expanded(
                                      child: Builder(
                                        builder: (context) {
                                          if (_model.textController.text == '') {
                                            return StreamBuilder<List<SitesRecord>>(
                                              stream: querySitesRecord(
                                                queryBuilder: (sitesRecord) {
                                                  // If user is admin, filter by their ID
                                                  var query = sitesRecord;
                                                  if (currentUserDocument?.role == Role.admin) {
                                                    query = query.where('siteAdminId', isEqualTo: currentUserDocument?.uid);
                                                  }
                                                  
                                                  // Return the query with ordering
                                                  return query;
                                                },
                                              ),
                                              builder: (context, snapshot) {
                                                // Customize what your widget looks like when it's loading.
                                                if (!snapshot.hasData) {
                                                  return Center(
                                                    child: SizedBox(
                                                      width: 40.0,
                                                      height: 40.0,
                                                      child: CircularProgressIndicator(
                                                        valueColor: AlwaysStoppedAnimation<Color>(
                                                          FlutterFlowTheme.of(context).primary,
                                                        ),
                                                      ),
                                                    ),
                                                  );
                                                }
                                                
                                                // Sort the sites list: sites with names first (alphabetically), then sites without names
                                                List<SitesRecord> listViewSitesRecordList = snapshot.data!;
                                                listViewSitesRecordList.sort((a, b) {
                                                  // If both have names or both don't have names, sort alphabetically
                                                  if ((a.siteName.isEmpty && b.siteName.isEmpty) || 
                                                      (a.siteName.isNotEmpty && b.siteName.isNotEmpty)) {
                                                    return a.siteName.toLowerCase().compareTo(b.siteName.toLowerCase());
                                                  }
                                                  // If only a has a name, a comes first
                                                  if (a.siteName.isNotEmpty && b.siteName.isEmpty) {
                                                    return -1;
                                                  }
                                                  // If only b has a name, b comes first
                                                  return 1;
                                                });
                                                
                                                if (listViewSitesRecordList.isEmpty) {
                                                  return const EmptyListComponentWidget(
                                                    text: 'No Sites',
                                                  );
                                                }

                                                return ListView.builder(
                                                  padding: EdgeInsets.zero,
                                                  primary: false,
                                                  shrinkWrap: true,
                                                  scrollDirection: Axis.vertical,
                                                  itemCount: listViewSitesRecordList.length,
                                                  itemBuilder: (context, listViewIndex) {
                                                    final listViewSitesRecord =
                                                        listViewSitesRecordList[listViewIndex];
                                                    return Padding(
                                                      padding: const EdgeInsetsDirectional.fromSTEB(
                                                          0.0, 0.0, 0.0, 1.0),
                                                      child: InkWell(
                                                        splashColor: Colors.transparent,
                                                        focusColor: Colors.transparent,
                                                        hoverColor: Colors.transparent,
                                                        highlightColor: Colors.transparent,
                                                        onTap: () async {
                                                          Get.toNamed(
                                                            SiteDetailScreenWidget.routePath,
                                                            parameters: {
                                                              'siteRef': listViewSitesRecord.reference.id,
                                                            },
                                                          );
                                                        },
                                                        child: Container(
                                                          width: 100.0,
                                                          decoration: BoxDecoration(
                                                            color: FlutterFlowTheme.of(context)
                                                                .secondaryBackground,
                                                            boxShadow: [
                                                              BoxShadow(
                                                                blurRadius: 0.0,
                                                                color: FlutterFlowTheme.of(context)
                                                                    .primaryBackground,
                                                                offset: const Offset(
                                                                  0.0,
                                                                  1.0,
                                                                ),
                                                              )
                                                            ],
                                                          ),
                                                          child: Padding(
                                                            padding: const EdgeInsetsDirectional.fromSTEB(
                                                                16.0, 8.0, 0.0, 8.0),
                                                            child: Row(
                                                              mainAxisSize: MainAxisSize.max,
                                                              children: [
                                                                Expanded(
                                                                  flex: 1,
                                                                  child: Padding(
                                                                    padding:
                                                                        const EdgeInsetsDirectional.fromSTEB(
                                                                            0.0, 0.0, 4.0, 0.0),
                                                                    child: Text(
                                                                      listViewSitesRecord.siteId,
                                                                      style: FlutterFlowTheme.of(context)
                                                                          .bodyMedium
                                                                          .override(
                                                                            fontFamily: 'Nunito',
                                                                            letterSpacing: 0.0,
                                                                          ),
                                                                    ),
                                                                  ),
                                                                ),
                                                                Expanded(
                                                                  flex: 1,
                                                                  child: Padding(
                                                                    padding:
                                                                        const EdgeInsetsDirectional.fromSTEB(
                                                                            0.0, 0.0, 8.0, 0.0),
                                                                    child: Text(
                                                                      listViewSitesRecord.siteLocation,
                                                                      maxLines: 2,
                                                                      style: FlutterFlowTheme.of(context)
                                                                          .titleLarge
                                                                          .override(
                                                                            fontFamily: 'Open Sans',
                                                                            fontSize: 14.0,
                                                                            letterSpacing: 0.0,
                                                                          ),
                                                                    ),
                                                                  ),
                                                                ),
                                                                Expanded(
                                                                  flex: 1,
                                                                  child: Padding(
                                                                    padding:
                                                                        const EdgeInsetsDirectional.fromSTEB(
                                                                            0.0, 0.0, 4.0, 0.0),
                                                                    child: Text(
                                                                      listViewSitesRecord.siteName != ''
                                                                          ? listViewSitesRecord.siteName
                                                                          : 'Name not added',
                                                                      maxLines: 2,
                                                                      style: FlutterFlowTheme.of(context)
                                                                          .titleLarge
                                                                          .override(
                                                                            fontFamily: 'Open Sans',
                                                                            fontSize: 14.0,
                                                                            letterSpacing: 0.0,
                                                                          ),
                                                                    ),
                                                                  ),
                                                                ),
                                                                Expanded(
                                                                  flex: 2,
                                                                  child: Container(
                                                                    height: 60.0,
                                                                    decoration: const BoxDecoration(),
                                                                    child: ListView(
                                                                      padding: EdgeInsets.zero,
                                                                      shrinkWrap: true,
                                                                      scrollDirection: Axis.horizontal,
                                                                      children: [
                                                                        for (var day in [
                                                                          'Monday',
                                                                          'Tuesday',
                                                                          'Wednesday',
                                                                          'Thursday',
                                                                          'Friday',
                                                                          'Saturday',
                                                                          'Sunday'
                                                                        ])
                                                                          Padding(
                                                                            padding:
                                                                                const EdgeInsetsDirectional
                                                                                    .fromSTEB(0, 0, 5, 0),
                                                                            child: OpeningHrsComponentWidget(
                                                                              siteRef: listViewSitesRecord
                                                                                  .reference,
                                                                              dayName: day,
                                                                            ),
                                                                          ),
                                                                      ],
                                                                    ),
                                                                  ),
                                                                ),
                                                                Padding(
                                                                  padding:
                                                                      const EdgeInsetsDirectional.fromSTEB(
                                                                          10.0, 0.0, 0.0, 0.0),
                                                                  child: Row(
                                                                    mainAxisSize: MainAxisSize.max,
                                                                    mainAxisAlignment:
                                                                        MainAxisAlignment.center,
                                                                    children: [
                                                                      currentUserDocument?.role ==
                                                                              Role.superAdmin
                                                                          ? FFButtonWidget(
                                                                              onPressed: () async {
                                                                                final bool? confirmed =
                                                                                    await showDialog<bool>(
                                                                                  context: context,
                                                                                  builder: (context) =>
                                                                                      AlertDialog(
                                                                                    backgroundColor:
                                                                                        Colors.transparent,
                                                                                    contentPadding:
                                                                                        EdgeInsets.zero,
                                                                                    content:
                                                                                        DeleteSiteDialogWidget(
                                                                                      siteRef:
                                                                                          listViewSitesRecord
                                                                                              .reference,
                                                                                    ),
                                                                                  ),
                                                                                );

                                                                                if (confirmed ?? false) {
                                                                                  // Show loading indicator
                                                                                  ScaffoldMessenger.of(
                                                                                          context)
                                                                                      .showSnackBar(
                                                                                    const SnackBar(
                                                                                      content: Row(
                                                                                        children: [
                                                                                          CircularProgressIndicator(
                                                                                            valueColor:
                                                                                                AlwaysStoppedAnimation<
                                                                                                        Color>(
                                                                                                    Colors
                                                                                                        .white),
                                                                                          ),
                                                                                          SizedBox(width: 16),
                                                                                          Text(
                                                                                              'Deleting site and related data...'),
                                                                                        ],
                                                                                      ),
                                                                                      duration: Duration(
                                                                                          seconds: 60),
                                                                                    ),
                                                                                  );

                                                                                  try {
                                                                                    // Delete all bookings associated with this site
                                                                                    final bookingsQuery =
                                                                                        await FirebaseFirestore
                                                                                            .instance
                                                                                            .collection(
                                                                                                'bookings')
                                                                                            .where('siteId',
                                                                                                isEqualTo:
                                                                                                    listViewSitesRecord
                                                                                                        .reference
                                                                                                        .id)
                                                                                            .get();

                                                                                    for (final doc
                                                                                        in bookingsQuery
                                                                                            .docs) {
                                                                                      await doc.reference
                                                                                          .delete();
                                                                                    }

                                                                                    // Delete all site-customer relationships for this site
                                                                                    final siteCustomerQuery =
                                                                                        await FirebaseFirestore
                                                                                            .instance
                                                                                            .collection(
                                                                                                'site_customer')
                                                                                            .where('siteId',
                                                                                                isEqualTo:
                                                                                                    listViewSitesRecord
                                                                                                        .reference
                                                                                                        .id)
                                                                                            .get();

                                                                                    for (final doc
                                                                                        in siteCustomerQuery
                                                                                            .docs) {
                                                                                      await doc.reference
                                                                                          .delete();
                                                                                    }

                                                                                    // Delete all pricing data associated with this site
                                                                                    final pricesQuery =
                                                                                        await FirebaseFirestore
                                                                                            .instance
                                                                                            .collection(
                                                                                                'prices')
                                                                                            .where('siteId',
                                                                                                isEqualTo:
                                                                                                    listViewSitesRecord
                                                                                                        .reference
                                                                                                        .id)
                                                                                            .get();

                                                                                    for (final doc
                                                                                        in pricesQuery.docs) {
                                                                                      await doc.reference
                                                                                          .delete();
                                                                                    }

                                                                                    // Delete the site admin user if exists
                                                                                    // if (listViewSitesRecord.siteAdminId.isNotEmpty) {
                                                                                    //   // Get the admin user document
                                                                                    //   final adminUserDoc = await FirebaseFirestore.instance
                                                                                    //       .collection('users')
                                                                                    //       .doc(listViewSitesRecord.siteAdminId)
                                                                                    //       .get();

                                                                                    //   if (adminUserDoc.exists) {
                                                                                    //     final userData = adminUserDoc.data() as Map<String, dynamic>;
                                                                                    //     // Only delete if the user is an admin (not a superadmin)
                                                                                    //     if (userData['role'] == 'admin' && userData['encryptedPassword'] != null) {
                                                                                    //       final deleteResult = await actions.deleteAdminUser(
                                                                                    //         listViewSitesRecord.siteAdminId,
                                                                                    //         userData['email'] as String,
                                                                                    //         userData['encryptedPassword'] as String,
                                                                                    //       );

                                                                                    //       if (deleteResult != 'Success') {
                                                                                    //         print('Warning: Failed to delete admin user: $deleteResult');
                                                                                    //       }
                                                                                    //     }
                                                                                    //   }
                                                                                    // }

                                                                                    // Delete the site schedule
                                                                                    await FirebaseFirestore
                                                                                        .instance
                                                                                        .collection(
                                                                                            'site_schedules')
                                                                                        .doc(
                                                                                            listViewSitesRecord
                                                                                                .scheduleId)
                                                                                        .delete();

                                                                                    // Delete the site
                                                                                    await listViewSitesRecord
                                                                                        .reference
                                                                                        .delete();

                                                                                    // Dismiss the loading snackbar
                                                                                    ScaffoldMessenger.of(
                                                                                            context)
                                                                                        .hideCurrentSnackBar();

                                                                                    await action_blocks
                                                                                        .snackbarAction(
                                                                                      context,
                                                                                      snackbarMessage:
                                                                                          'Site and related data deleted successfully',
                                                                                    );
                                                                                  } catch (e) {
                                                                                    // Dismiss the loading snackbar
                                                                                    ScaffoldMessenger.of(
                                                                                            context)
                                                                                        .hideCurrentSnackBar();

                                                                                    await action_blocks
                                                                                        .snackbarAction(
                                                                                      context,
                                                                                      snackbarMessage:
                                                                                          'Error deleting site: $e',
                                                                                    );
                                                                                  }
                                                                                }
                                                                              },
                                                                              text: 'Remove',
                                                                              options: FFButtonOptions(
                                                                                height: 35.0,
                                                                                padding:
                                                                                    const EdgeInsetsDirectional
                                                                                        .fromSTEB(
                                                                                        16.0, 0.0, 16.0, 0.0),
                                                                                iconPadding:
                                                                                    const EdgeInsetsDirectional
                                                                                        .fromSTEB(
                                                                                        0.0, 0.0, 0.0, 0.0),
                                                                                color: FlutterFlowTheme.of(
                                                                                        context)
                                                                                    .customRed,
                                                                                textStyle: FlutterFlowTheme
                                                                                        .of(context)
                                                                                    .titleSmall
                                                                                    .override(
                                                                                      fontFamily: 'Open Sans',
                                                                                      color: Colors.white,
                                                                                      fontSize: 14.0,
                                                                                      letterSpacing: 0.0,
                                                                                    ),
                                                                                elevation: 0.0,
                                                                                borderRadius:
                                                                                    BorderRadius.circular(
                                                                                        8.0),
                                                                              ),
                                                                            )
                                                                          : const SizedBox.shrink()
                                                                    ],
                                                                  ),
                                                                ),
                                                              ],
                                                            ),
                                                          ),
                                                        ),
                                                      ),
                                                    );
                                                  },
                                                );
                                              },
                                            );
                                          } else {
                                            return Builder(
                                              builder: (context) {
                                                // Apply role-based filtering using where condition
                                                List<SitesRecord> searchSites = _model.simpleSearchResults
                                                    .where((site) => currentUserDocument?.role == Role.admin
                                                        ? site.siteAdminId == currentUserDocument?.uid
                                                        : true)
                                                    .toList();
                                                
                                                // Sort the search results: sites with names first (alphabetically), then sites without names
                                                searchSites.sort((a, b) {
                                                  // If both have names or both don't have names, sort alphabetically
                                                  if ((a.siteName.isEmpty && b.siteName.isEmpty) || 
                                                      (a.siteName.isNotEmpty && b.siteName.isNotEmpty)) {
                                                    return a.siteName.toLowerCase().compareTo(b.siteName.toLowerCase());
                                                  }
                                                  // If only a has a name, a comes first
                                                  if (a.siteName.isNotEmpty && b.siteName.isEmpty) {
                                                    return -1;
                                                  }
                                                  // If only b has a name, b comes first
                                                  return 1;
                                                });

                                                if (searchSites.isEmpty) {
                                                  return const EmptyListComponentWidget(
                                                    text: 'No Sites',
                                                  );
                                                }

                                                return ListView.builder(
                                                  padding: EdgeInsets.zero,
                                                  primary: false,
                                                  shrinkWrap: true,
                                                  scrollDirection: Axis.vertical,
                                                  itemCount: searchSites.length,
                                                  itemBuilder: (context, searchSitesIndex) {
                                                    final searchSitesItem = searchSites[searchSitesIndex];
                                                    return Padding(
                                                      padding: const EdgeInsetsDirectional.fromSTEB(
                                                          0.0, 0.0, 0.0, 1.0),
                                                      child: InkWell(
                                                        splashColor: Colors.transparent,
                                                        focusColor: Colors.transparent,
                                                        hoverColor: Colors.transparent,
                                                        highlightColor: Colors.transparent,
                                                        onTap: () async {
                                                          Get.toNamed(
                                                            SiteDetailScreenWidget.routePath,
                                                            parameters: {
                                                              'siteRef': searchSitesItem.reference.id,
                                                            },
                                                          );
                                                        },
                                                        child: Container(
                                                          width: 100.0,
                                                          decoration: BoxDecoration(
                                                            color: FlutterFlowTheme.of(context)
                                                                .secondaryBackground,
                                                            boxShadow: [
                                                              BoxShadow(
                                                                blurRadius: 0.0,
                                                                color: FlutterFlowTheme.of(context)
                                                                    .primaryBackground,
                                                                offset: const Offset(
                                                                  0.0,
                                                                  1.0,
                                                                ),
                                                              )
                                                            ],
                                                          ),
                                                          child: Padding(
                                                            padding: const EdgeInsetsDirectional.fromSTEB(
                                                                16.0, 8.0, 0.0, 8.0),
                                                            child: Row(
                                                              mainAxisSize: MainAxisSize.max,
                                                              children: [
                                                                Expanded(
                                                                  flex: 1,
                                                                  child: Padding(
                                                                    padding:
                                                                        const EdgeInsetsDirectional.fromSTEB(
                                                                            0.0, 0.0, 4.0, 0.0),
                                                                    child: Text(
                                                                      searchSitesItem.siteId,
                                                                      style: FlutterFlowTheme.of(context)
                                                                          .bodyMedium
                                                                          .override(
                                                                            fontFamily: 'Nunito',
                                                                            letterSpacing: 0.0,
                                                                          ),
                                                                    ),
                                                                  ),
                                                                ),
                                                                Expanded(
                                                                  flex: 1,
                                                                  child: Padding(
                                                                    padding:
                                                                        const EdgeInsetsDirectional.fromSTEB(
                                                                            0.0, 0.0, 4.0, 0.0),
                                                                    child: Text(
                                                                      searchSitesItem.siteLocation,
                                                                      maxLines: 2,
                                                                      style: FlutterFlowTheme.of(context)
                                                                          .titleLarge
                                                                          .override(
                                                                            fontFamily: 'Open Sans',
                                                                            fontSize: 14.0,
                                                                            letterSpacing: 0.0,
                                                                          ),
                                                                    ),
                                                                  ),
                                                                ),
                                                                Expanded(
                                                                  flex: 1,
                                                                  child: Padding(
                                                                    padding:
                                                                        const EdgeInsetsDirectional.fromSTEB(
                                                                            0.0, 0.0, 4.0, 0.0),
                                                                    child: Text(
                                                                      searchSitesItem.description != ''
                                                                          ? searchSitesItem.description
                                                                          : 'Description not added',
                                                                      maxLines: 2,
                                                                      style: FlutterFlowTheme.of(context)
                                                                          .titleLarge
                                                                          .override(
                                                                            fontFamily: 'Open Sans',
                                                                            fontSize: 14.0,
                                                                            letterSpacing: 0.0,
                                                                          ),
                                                                    ),
                                                                  ),
                                                                ),
                                                                Expanded(
                                                                  flex: 2,
                                                                  child: Container(
                                                                    height: 60.0,
                                                                    decoration: const BoxDecoration(),
                                                                    child: Center(
                                                                      child: ListView(
                                                                        padding: EdgeInsets.zero,
                                                                        shrinkWrap: true,
                                                                        scrollDirection: Axis.horizontal,
                                                                        children: [
                                                                          for (var day in [
                                                                            'Monday',
                                                                            'Tuesday',
                                                                            'Wednesday',
                                                                            'Thursday',
                                                                            'Friday',
                                                                            'Saturday',
                                                                            'Sunday'
                                                                          ])
                                                                            Padding(
                                                                              padding:
                                                                                  const EdgeInsetsDirectional
                                                                                      .fromSTEB(0, 0, 5, 0),
                                                                              child:
                                                                                  OpeningHrsComponentWidget(
                                                                                siteRef:
                                                                                    searchSitesItem.reference,
                                                                                dayName: day,
                                                                              ),
                                                                            ),
                                                                        ],
                                                                      ),
                                                                    ),
                                                                  ),
                                                                ),
                                                                Padding(
                                                                  padding:
                                                                      const EdgeInsetsDirectional.fromSTEB(
                                                                          10.0, 0.0, 0.0, 0.0),
                                                                  child: Row(
                                                                    mainAxisSize: MainAxisSize.max,
                                                                    mainAxisAlignment:
                                                                        MainAxisAlignment.center,
                                                                    children: [
                                                                      FFButtonWidget(
                                                                        onPressed: () async {
                                                                          final bool? confirmed =
                                                                              await showDialog<bool>(
                                                                            context: context,
                                                                            builder: (context) => AlertDialog(
                                                                              backgroundColor:
                                                                                  Colors.transparent,
                                                                              contentPadding: EdgeInsets.zero,
                                                                              content: DeleteSiteDialogWidget(
                                                                                siteRef:
                                                                                    searchSitesItem.reference,
                                                                              ),
                                                                            ),
                                                                          );

                                                                          if (confirmed ?? false) {
                                                                            // Show loading indicator
                                                                            ScaffoldMessenger.of(context)
                                                                                .showSnackBar(
                                                                              const SnackBar(
                                                                                content: Row(
                                                                                  children: [
                                                                                    CircularProgressIndicator(
                                                                                      valueColor:
                                                                                          AlwaysStoppedAnimation<
                                                                                                  Color>(
                                                                                              Colors.white),
                                                                                    ),
                                                                                    SizedBox(width: 16),
                                                                                    Text(
                                                                                        'Deleting site and related data...'),
                                                                                  ],
                                                                                ),
                                                                                duration:
                                                                                    Duration(seconds: 60),
                                                                              ),
                                                                            );

                                                                            try {
                                                                              // Delete all bookings associated with this site
                                                                              final bookingsQuery =
                                                                                  await FirebaseFirestore
                                                                                      .instance
                                                                                      .collection('bookings')
                                                                                      .where('siteId',
                                                                                          isEqualTo:
                                                                                              searchSitesItem
                                                                                                  .reference
                                                                                                  .id)
                                                                                      .get();

                                                                              for (final doc
                                                                                  in bookingsQuery.docs) {
                                                                                await doc.reference.delete();
                                                                              }

                                                                              // Delete all site-customer relationships for this site
                                                                              final siteCustomerQuery =
                                                                                  await FirebaseFirestore
                                                                                      .instance
                                                                                      .collection(
                                                                                          'site_customer')
                                                                                      .where('siteId',
                                                                                          isEqualTo:
                                                                                              searchSitesItem
                                                                                                  .reference
                                                                                                  .id)
                                                                                      .get();

                                                                              for (final doc
                                                                                  in siteCustomerQuery.docs) {
                                                                                await doc.reference.delete();
                                                                              }

                                                                              // Delete all pricing data associated with this site
                                                                              final pricesQuery =
                                                                                  await FirebaseFirestore
                                                                                      .instance
                                                                                      .collection('prices')
                                                                                      .where('siteId',
                                                                                          isEqualTo:
                                                                                              searchSitesItem
                                                                                                  .reference
                                                                                                  .id)
                                                                                      .get();

                                                                              for (final doc
                                                                                  in pricesQuery.docs) {
                                                                                await doc.reference.delete();
                                                                              }

                                                                              // Delete the site admin user if exists
                                                                              if (searchSitesItem
                                                                                  .siteAdminId.isNotEmpty) {
                                                                                // Get the admin user document
                                                                                final adminUserDoc =
                                                                                    await FirebaseFirestore
                                                                                        .instance
                                                                                        .collection('users')
                                                                                        .doc(searchSitesItem
                                                                                            .siteAdminId)
                                                                                        .get();

                                                                                if (adminUserDoc.exists) {
                                                                                  final userData =
                                                                                      adminUserDoc.data()
                                                                                          as Map<String,
                                                                                              dynamic>;
                                                                                  // Only delete if the user is an admin (not a superadmin)
                                                                                  if (userData['role'] ==
                                                                                          'admin' &&
                                                                                      userData[
                                                                                              'encryptedPassword'] !=
                                                                                          null) {
                                                                                    final deleteResult =
                                                                                        await actions
                                                                                            .deleteAdminUser(
                                                                                      searchSitesItem
                                                                                          .siteAdminId,
                                                                                      userData['email']
                                                                                          as String,
                                                                                      userData[
                                                                                              'encryptedPassword']
                                                                                          as String,
                                                                                    );

                                                                                    if (deleteResult !=
                                                                                        'Success') {
                                                                                      print(
                                                                                          'Warning: Failed to delete admin user: $deleteResult');
                                                                                    }
                                                                                  }
                                                                                }
                                                                              }

                                                                              // Delete the site schedule
                                                                              await FirebaseFirestore.instance
                                                                                  .collection(
                                                                                      'site_schedules')
                                                                                  .doc(searchSitesItem
                                                                                      .scheduleId)
                                                                                  .delete();

                                                                              // Delete the site
                                                                              await searchSitesItem.reference
                                                                                  .delete();

                                                                              // Dismiss the loading snackbar
                                                                              ScaffoldMessenger.of(context)
                                                                                  .hideCurrentSnackBar();

                                                                              await action_blocks
                                                                                  .snackbarAction(
                                                                                context,
                                                                                snackbarMessage:
                                                                                    'Site, admin user, schedule, bookings, pricing, and customer relationships deleted successfully',
                                                                              );
                                                                            } catch (e) {
                                                                              // Dismiss the loading snackbar
                                                                              ScaffoldMessenger.of(context)
                                                                                  .hideCurrentSnackBar();

                                                                              await action_blocks
                                                                                  .snackbarAction(
                                                                                context,
                                                                                snackbarMessage:
                                                                                    'Error deleting site: $e',
                                                                              );
                                                                            }
                                                                          }
                                                                        },
                                                                        text: 'Remove',
                                                                        options: FFButtonOptions(
                                                                          height: 35.0,
                                                                          padding: const EdgeInsetsDirectional
                                                                              .fromSTEB(16.0, 0.0, 16.0, 0.0),
                                                                          iconPadding:
                                                                              const EdgeInsetsDirectional
                                                                                  .fromSTEB(
                                                                                  0.0, 0.0, 0.0, 0.0),
                                                                          color: FlutterFlowTheme.of(context)
                                                                              .customRed,
                                                                          textStyle:
                                                                              FlutterFlowTheme.of(context)
                                                                                  .titleSmall
                                                                                  .override(
                                                                                    fontFamily: 'Open Sans',
                                                                                    color: Colors.white,
                                                                                    fontSize: 14.0,
                                                                                    letterSpacing: 0.0,
                                                                                  ),
                                                                          elevation: 0.0,
                                                                          borderRadius:
                                                                              BorderRadius.circular(8.0),
                                                                        ),
                                                                      ),
                                                                    ],
                                                                  ),
                                                                ),
                                                              ],
                                                            ),
                                                          ),
                                                        ),
                                                      ),
                                                    );
                                                  },
                                                );
                                              },
                                            );
                                          }
                                        },
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ),
                      ].addToEnd(const SizedBox(height: 24.0)),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
