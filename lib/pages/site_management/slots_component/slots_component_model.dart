import 'package:flutter/material.dart';
import 'package:unimetals_admin/backend/schema/site_schedules_record.dart';

import '/flutter_flow/flutter_flow_util.dart';
import 'slots_component_widget.dart' show SlotsComponentWidget;

class SlotsComponentModel extends FlutterFlowModel<SlotsComponentWidget> {
  List<TimeSlot> timeSlots = [];
  bool isLoading = false;
  String? scheduleId;

  @override
  void initState(BuildContext context) {}

  @override
  void dispose() {}

  void addNewSlot(TimeSlot slot) {
    timeSlots.add(slot);
  }

  void updateSlot(int index, TimeSlot slot) {
    if (index >= 0 && index < timeSlots.length) {
      timeSlots[index] = slot;
    }
  }

  void removeSlot(int index) {
    if (index >= 0 && index < timeSlots.length) {
      timeSlots.removeAt(index);
    }
  }
}
