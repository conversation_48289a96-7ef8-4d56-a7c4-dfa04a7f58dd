import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:unimetals_admin/backend/schema/site_schedules_record.dart';
import 'package:unimetals_admin/backend/schema/sites_record.dart';
import 'package:unimetals_admin/pages/site_management/site_detail_screen/site_detail_screen_model.dart';
import 'package:unimetals_admin/utils/schedule_utils.dart';

import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/flutter_flow/flutter_flow_widgets.dart';
import 'slots_component_model.dart';

export 'slots_component_model.dart';

class SlotsComponentWidget extends StatefulWidget {
  const SlotsComponentWidget({
    super.key,
    required this.siteRef,
    required this.dayName,
    this.openTime,
    this.closeTime,
  });

  final String siteRef;
  final String dayName;
  final String? openTime;
  final String? closeTime;

  @override
  State<SlotsComponentWidget> createState() => _SlotsComponentWidgetState();
}

class _SlotsComponentWidgetState extends State<SlotsComponentWidget> {
  late SlotsComponentModel _model;
  SiteDetailScreenModel? siteDetailModel;

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => SlotsComponentModel());
    siteDetailModel = Get.find<SiteDetailScreenModel>();
    _loadInitialData();
  }

  @override
  void didUpdateWidget(SlotsComponentWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.dayName != widget.dayName || oldWidget.siteRef != widget.siteRef) {
      _loadInitialData();
    }
  }

  RxBool isLoading = false.obs;

  Future<void> _loadInitialData() async {
    isLoading.value = true;
    try {
      // Clear existing time slots
      _model.timeSlots.clear();

      // Get site data
      final siteDoc = await FirebaseFirestore.instance.collection('sites').doc(widget.siteRef).get();

      if (!siteDoc.exists) return;

      final site = SitesRecord.fromSnapshot(siteDoc);
      _model.scheduleId = site.scheduleId;

      // If openTime and closeTime are provided, generate time slots based on them
      if (widget.openTime != null && widget.closeTime != null) {
        // Parse the provided times
        final openTimeParts = widget.openTime!.split(':');
        final closeTimeParts = widget.closeTime!.split(':');
        
        final openTime = TimeOfDay(
          hour: int.parse(openTimeParts[0]),
          minute: int.parse(openTimeParts[1]),
        );
        
        final closeTime = TimeOfDay(
          hour: int.parse(closeTimeParts[0]),
          minute: int.parse(closeTimeParts[1]),
        );
        
        // Generate time slots based on the provided times
        final generatedSlots = generateTimeSlots(openTime, closeTime);
        
        if (mounted) {
          setState(() {
            _model.timeSlots = generatedSlots.map((slotMap) => 
              TimeSlot(
                time: slotMap['time'] as String,
                isDisabledByAdmin: slotMap['isDisabledByAdmin'] as bool,
              )
            ).toList();
          });
        }
      } else {
        // Get schedule data from database (fallback to original behavior)
        final scheduleDoc =
            await FirebaseFirestore.instance.collection('site_schedules').doc(_model.scheduleId).get();

        if (!scheduleDoc.exists) return;

        final schedule = SiteSchedulesRecord.fromSnapshot(scheduleDoc);
        final daySchedule = schedule.weeklySchedule[widget.dayName.toLowerCase()];

        if (mounted) {
          setState(() {
            _model.timeSlots = daySchedule?.timeSlots ?? [];
          });
        }
      }
    } catch (e) {
      print('Error loading initial data: $e');
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> _saveChanges() async {
    try {
      if (_model.scheduleId == null) return;

      final scheduleRef = FirebaseFirestore.instance.collection('site_schedules').doc(_model.scheduleId);

      // Get current schedule
      final scheduleDoc = await scheduleRef.get();
      if (!scheduleDoc.exists) return;

      final schedule = SiteSchedulesRecord.fromSnapshot(scheduleDoc);
      final dayKey = widget.dayName.toLowerCase();

      // Calculate open and close times from time slots
      if (_model.timeSlots.isNotEmpty) {
        // Get first and last time slots
        final firstSlot = _model.timeSlots.first;
        final lastSlot = _model.timeSlots.last;

        // Parse times
        final firstTimeParts = firstSlot.time.split(':');
        final lastTimeParts = lastSlot.time.split(':');

        // Calculate actual opening time (30 minutes before first slot)
        int openHour = int.parse(firstTimeParts[0]);
        int openMinute = int.parse(firstTimeParts[1]);

        // Subtract 30 minutes from the first slot time
        if (openMinute < 30) {
          openHour -= 1;
          openMinute = 30 + openMinute;
        } else {
          openMinute -= 30;
        }

        final openTime = TimeOfDay(hour: openHour, minute: openMinute);
        final closeTime = TimeOfDay(hour: int.parse(lastTimeParts[0]), minute: int.parse(lastTimeParts[1]));

        // Get existing weekly schedule and convert to Map
        final weeklySchedule = Map<String, dynamic>.from(schedule.weeklySchedule.map(
          (key, value) => MapEntry(key, value.toMap()),
        ));

        // Get current day's isOpen status
        final currentDaySchedule = schedule.weeklySchedule[dayKey];
        final isOpen = currentDaySchedule?.isOpen ?? true;

        // Update only the current day's schedule
        weeklySchedule[dayKey] = {
          'isOpen': isOpen,
          'openTime':
              '${openTime.hour.toString().padLeft(2, '0')}:${openTime.minute.toString().padLeft(2, '0')}',
          'closeTime':
              '${closeTime.hour.toString().padLeft(2, '0')}:${closeTime.minute.toString().padLeft(2, '0')}',
          'timeSlots': _model.timeSlots.map((slot) => slot.toMap()).toList(),
        };

        print('Updating schedule for $dayKey: ${weeklySchedule[dayKey]}');

        // Update schedule
        await scheduleRef.update({
          'weeklySchedule': weeklySchedule,
          'lastUpdated': FieldValue.serverTimestamp(),
        });

        // Update site's weekly hours
        final siteRef = FirebaseFirestore.instance.collection('sites').doc(widget.siteRef);
        final siteDoc = await siteRef.get();

        if (siteDoc.exists) {
          final site = SitesRecord.fromSnapshot(siteDoc);
          final weeklyHours = Map<String, dynamic>.from(site.weeklyHours ?? {});

          final newHours = {
            'openTime':
                '${openTime.hour.toString().padLeft(2, '0')}:${openTime.minute.toString().padLeft(2, '0')}',
            'closeTime':
                '${closeTime.hour.toString().padLeft(2, '0')}:${closeTime.minute.toString().padLeft(2, '0')}',
            'isOpen': isOpen,
          };

          weeklyHours[dayKey] = newHours;

          await siteRef.update({
            'weeklyHours': weeklyHours,
          });

          // Update the local weeklyHours in SiteDetailScreenModel
          if (siteDetailModel != null) {
            siteDetailModel!.weeklyHours[dayKey] = newHours;
            siteDetailModel!.weeklyHours.refresh(); // Trigger UI update
          }
        }

        if (mounted) {
          Navigator.pop(context);
        }
      }
    } catch (e) {
      print('Error saving changes: $e');
      print('Stack trace: ${StackTrace.current}');
    }
  }

  void _addNewSlot() {
    final lastSlot = _model.timeSlots.isEmpty
        ? const TimeSlot(time: '09:00', isDisabledByAdmin: false)
        : _model.timeSlots.last;

    // Parse last slot time and add 30 minutes
    final parts = lastSlot.time.split(':');
    int hours = int.parse(parts[0]);
    int minutes = int.parse(parts[1]);

    minutes += 30;
    if (minutes >= 60) {
      hours += 1;
      minutes -= 60;
    }

    final newTime = '${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}';

    setState(() {
      _model.timeSlots.add(TimeSlot(
        time: newTime,
        isDisabledByAdmin: false, // New slots are available by default
      ));
    });
  }

  String formatTime(String time24) {
    try {
      final parts = time24.split(':');
      int hours = int.parse(parts[0]);
      final minutes = parts[1];
      final period = hours >= 12 ? 'PM' : 'AM';

      hours = hours > 12 ? hours - 12 : hours;
      hours = hours == 0 ? 12 : hours;

      return '$hours:$minutes $period';
    } catch (e) {
      return time24;
    }
  }

  // Add this method to toggle slot availability
  void _toggleSlotAvailability(int index) {
    setState(() {
      final slot = _model.timeSlots[index];
      _model.timeSlots[index] = TimeSlot(
        time: slot.time,
        isDisabledByAdmin: !slot.isDisabledByAdmin, // Toggle the disabled status
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: const AlignmentDirectional(0.0, 0.0),
      child: Container(
        width: 600.0,
        height: 600.0,
        decoration: const BoxDecoration(
          color: Colors.transparent,
        ),
        child: Padding(
          padding: const EdgeInsetsDirectional.fromSTEB(16.0, 16.0, 16.0, 16.0),
          child: Container(
            width: double.infinity,
            decoration: BoxDecoration(
              color: FlutterFlowTheme.of(context).secondaryBackground,
              boxShadow: const [
                BoxShadow(
                  blurRadius: 4.0,
                  color: Color(0x33000000),
                  offset: Offset(0.0, 2.0),
                  spreadRadius: 0.0,
                )
              ],
              borderRadius: BorderRadius.circular(16.0),
            ),
            child: Padding(
              padding: const EdgeInsetsDirectional.fromSTEB(16.0, 16.0, 16.0, 16.0),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Padding(
                    padding: const EdgeInsetsDirectional.fromSTEB(8.0, 0.0, 8.0, 0.0),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          '${widget.dayName.capitalize} Time Slots',
                          textAlign: TextAlign.center,
                          style: FlutterFlowTheme.of(context).headlineSmall.override(
                                fontFamily: 'Open Sans',
                                letterSpacing: 0.0,
                              ),
                        ),
                        FFButtonWidget(
                          onPressed: _addNewSlot,
                          text: 'Add Slot',
                          options: FFButtonOptions(
                            height: 40.0,
                            padding: const EdgeInsetsDirectional.fromSTEB(16.0, 0.0, 16.0, 0.0),
                            iconPadding: const EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 0.0, 0.0),
                            color: FlutterFlowTheme.of(context).secondary,
                            textStyle: FlutterFlowTheme.of(context).titleSmall.override(
                                  fontFamily: 'Open Sans',
                                  color: Colors.white,
                                  letterSpacing: 0.0,
                                  fontWeight: FontWeight.bold,
                                ),
                            elevation: 0.0,
                            borderRadius: BorderRadius.circular(10.0),
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 40),
                  Expanded(
                    child: isLoading.value
                        ? Center(
                            child: SizedBox(
                              width: 40,
                              height: 40,
                              child: CircularProgressIndicator(
                                strokeWidth: 4,
                                color: FlutterFlowTheme.of(context).primary,
                              ),
                            ),
                          )
                        : Padding(
                            padding: const EdgeInsetsDirectional.fromSTEB(8.0, 0.0, 8.0, 0.0),
                            child: Wrap(
                              spacing: 8.0,
                              runSpacing: 8.0,
                              alignment: WrapAlignment.start,
                              children: [
                                for (int index = 0; index < _model.timeSlots.length; index++)
                                  GestureDetector(
                                    onTap: () => _toggleSlotAvailability(index),
                                    child: Container(
                                      width: 105.0,
                                      height: 40.0,
                                      decoration: BoxDecoration(
                                        color: _model.timeSlots[index].isDisabledByAdmin
                                            ? FlutterFlowTheme.of(context)
                                                .error
                                                .withOpacity(0.1) // Red background when disabled
                                            : FlutterFlowTheme.of(context)
                                                .primaryBackground, // Normal background when available
                                        borderRadius: BorderRadius.circular(8.0),
                                        border: Border.all(
                                          color: _model.timeSlots[index].isDisabledByAdmin
                                              ? FlutterFlowTheme.of(context).error // Red border when disabled
                                              : FlutterFlowTheme.of(context)
                                                  .alternate, // Normal border when available
                                          width: 1.0,
                                        ),
                                      ),
                                      child: Center(
                                        child: Text(
                                          formatTime(_model.timeSlots[index].time),
                                          style: FlutterFlowTheme.of(context).bodyMedium.override(
                                                fontFamily: 'Open Sans',
                                                color: _model.timeSlots[index].isDisabledByAdmin
                                                    ? FlutterFlowTheme.of(context)
                                                        .error // Red text when disabled
                                                    : FlutterFlowTheme.of(context)
                                                        .primaryText, // Normal text when available
                                                fontSize: 14.0,
                                                letterSpacing: 0.0,
                                              ),
                                        ),
                                      ),
                                    ),
                                  ),
                              ],
                            ),
                          ),
                  ),
                  Row(
                    mainAxisSize: MainAxisSize.max,
                    children: [
                      Expanded(
                        child: Padding(
                          padding: const EdgeInsetsDirectional.fromSTEB(8.0, 0.0, 8.0, 0.0),
                          child: FFButtonWidget(
                            onPressed: () => Navigator.pop(context),
                            text: 'Cancel',
                            options: FFButtonOptions(
                              width: 200.0,
                              height: 50.0,
                              padding: const EdgeInsets.all(8.0),
                              iconPadding: const EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 0.0, 0.0),
                              color: const Color(0x00036570),
                              textStyle: FlutterFlowTheme.of(context).titleSmall.override(
                                    fontFamily: 'Open Sans',
                                    color: FlutterFlowTheme.of(context).primary,
                                    letterSpacing: 0.0,
                                  ),
                              elevation: 0.0,
                              borderSide: BorderSide(
                                color: FlutterFlowTheme.of(context).primary,
                                width: 1.0,
                              ),
                              borderRadius: BorderRadius.circular(8.0),
                            ),
                          ),
                        ),
                      ),
                      Expanded(
                        child: Padding(
                          padding: const EdgeInsetsDirectional.fromSTEB(8.0, 0.0, 8.0, 0.0),
                          child: FFButtonWidget(
                            onPressed: _saveChanges,
                            text: 'Save',
                            options: FFButtonOptions(
                              width: 200.0,
                              height: 50.0,
                              padding: const EdgeInsets.all(8.0),
                              iconPadding: const EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 0.0, 0.0),
                              color: FlutterFlowTheme.of(context).primary,
                              textStyle: FlutterFlowTheme.of(context).titleSmall.override(
                                    fontFamily: 'Open Sans',
                                    color: FlutterFlowTheme.of(context).info,
                                    letterSpacing: 0.0,
                                  ),
                              elevation: 0.0,
                              borderSide: const BorderSide(
                                color: Colors.transparent,
                                width: 1.0,
                              ),
                              borderRadius: BorderRadius.circular(8.0),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ].divide(const SizedBox(height: 16.0)),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
