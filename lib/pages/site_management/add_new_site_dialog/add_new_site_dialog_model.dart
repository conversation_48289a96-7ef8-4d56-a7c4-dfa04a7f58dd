import '/flutter_flow/flutter_flow_util.dart';
import 'add_new_site_dialog_widget.dart' show AddNewSiteDialogWidget;
import 'package:flutter/material.dart';

class AddNewSiteDialogModel extends FlutterFlowModel<AddNewSiteDialogWidget> {
  ///  State fields for stateful widgets in this component.
  final unfocusNode = FocusNode();
  // State field(s) for SiteName widget.
  FocusNode? siteNameFocusNode;
  TextEditingController? siteNameController;
  String? Function(BuildContext, String?)? siteNameControllerValidator;
  
  // State field(s) for Description widget.
  FocusNode? descriptionFocusNode;
  TextEditingController? descriptionController;
  String? Function(BuildContext, String?)? descriptionControllerValidator;

  // State field(s) for PlacePicker widget.
  FFPlace placePickerValue = FFPlace();

  Map<String, dynamic>? selectedLocation;

  /// Initialization and disposal methods.

  @override
  void initState(BuildContext context) {}

  @override
  void dispose() {
    unfocusNode.dispose();
    siteNameFocusNode?.dispose();
    siteNameController?.dispose();

    descriptionFocusNode?.dispose();
    descriptionController?.dispose();
  }
}
