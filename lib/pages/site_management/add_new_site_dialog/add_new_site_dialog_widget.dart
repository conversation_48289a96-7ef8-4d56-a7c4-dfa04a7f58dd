import 'package:flutter/material.dart';
import 'package:unimetals_admin/utils/schedule_utils.dart';

import '/actions/actions.dart' as action_blocks;
import '/flutter_flow/flutter_flow_place_picker.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/flutter_flow/flutter_flow_widgets.dart';
import '../../../backend/backend.dart';
import 'add_new_site_dialog_model.dart';

export 'add_new_site_dialog_model.dart';

class AddNewSiteDialogWidget extends StatefulWidget {
  const AddNewSiteDialogWidget({super.key});

  @override
  State<AddNewSiteDialogWidget> createState() => _AddNewSiteDialogWidgetState();
}

class _AddNewSiteDialogWidgetState extends State<AddNewSiteDialogWidget> {
  late AddNewSiteDialogModel _model;

  static const _locations = [
    {
      'name': '<PERSON><PERSON>',
      'lat': 33.594758,
      'lng': 73.049553,
      'zipCode': '46000',
    },
    {
      'name': 'Blue Area Islamabad',
      'lat': 33.7294,
      'lng': 73.0931,
      'zipCode': '44000',
    },
    {
      'name': 'Ghakhar Plaza Rawalpindi',
      'lat': 33.6007,
      'lng': 73.0679,
      'zipCode': '46000',
    },
    {
      'name': 'F-8 Markaz Islamabad',
      'lat': 33.7147,
      'lng': 73.0398,
      'zipCode': '44000',
    },
    {
      'name': 'G-9 Markaz Islamabad',
      'lat': 33.6960,
      'lng': 73.0369,
      'zipCode': '44000',
    },
    {
      'name': 'Commercial Market Rawalpindi',
      'lat': 33.6239,
      'lng': 73.0756,
      'zipCode': '46000',
    },
    {
      'name': 'F-7 Markaz Islamabad',
      'lat': 33.7251,
      'lng': 73.0567,
      'zipCode': '44000',
    },
    {
      'name': 'Raja Bazaar Rawalpindi',
      'lat': 33.6007,
      'lng': 73.0679,
      'zipCode': '46000',
    },
    {
      'name': 'I-8 Markaz Islamabad',
      'lat': 33.6614,
      'lng': 73.0766,
      'zipCode': '44000',
    },
    {
      'name': 'Bahria Town Phase 7',
      'lat': 33.5651,
      'lng': 73.1268,
      'zipCode': '46000',
    },
    {
      'name': 'DHA Phase 2 Islamabad',
      'lat': 33.4846,
      'lng': 73.1641,
      'zipCode': '44000',
    },
    {
      'name': 'PWD Commercial',
      'lat': 33.6239,
      'lng': 73.0756,
      'zipCode': '46000',
    },
  ];

  @override
  void setState(VoidCallback callback) {
    super.setState(callback);
    _model.onUpdate();
  }

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => AddNewSiteDialogModel());

    _model.siteNameController ??= TextEditingController();
    _model.siteNameFocusNode ??= FocusNode();

    _model.descriptionController ??= TextEditingController();
    _model.descriptionFocusNode ??= FocusNode();

    WidgetsBinding.instance.addPostFrameCallback((_) => safeSetState(() {}));
  }

  @override
  void dispose() {
    _model.maybeDispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: const AlignmentDirectional(0.0, 0.0),
      child: Container(
        width: 400.0,
        decoration: BoxDecoration(
          color: FlutterFlowTheme.of(context).secondaryBackground,
          boxShadow: const [
            BoxShadow(
              blurRadius: 4.0,
              color: Color(0x33000000),
              offset: Offset(0.0, 2.0),
              spreadRadius: 0.0,
            )
          ],
          borderRadius: BorderRadius.circular(16.0),
        ),
        child: Padding(
          padding: const EdgeInsetsDirectional.fromSTEB(16.0, 16.0, 16.0, 16.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Add New Site',
                style: FlutterFlowTheme.of(context).bodyMedium.override(
                      fontFamily: 'Open Sans',
                      color: FlutterFlowTheme.of(context).blackColor,
                      fontSize: 26.0,
                      letterSpacing: 0.0,
                      fontWeight: FontWeight.w600,
                    ),
              ),
              Padding(
                padding: const EdgeInsetsDirectional.fromSTEB(16.0, 0.0, 16.0, 0.0),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                      padding: const EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 0.0, 4.0),
                      child: Text(
                        'Site Name',
                        style: FlutterFlowTheme.of(context).bodyMedium,
                      ),
                    ),
                    TextFormField(
                      controller: _model.siteNameController,
                      focusNode: _model.siteNameFocusNode,
                      decoration: InputDecoration(
                        isDense: true,
                        hintText: 'Enter site name',
                        enabledBorder: OutlineInputBorder(
                          borderSide: BorderSide(
                            color: FlutterFlowTheme.of(context).alternate,
                            width: 1.0,
                          ),
                          borderRadius: BorderRadius.circular(8.0),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderSide: BorderSide(
                            color: FlutterFlowTheme.of(context).primary,
                            width: 1.0,
                          ),
                          borderRadius: BorderRadius.circular(8.0),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              Padding(
                padding: const EdgeInsetsDirectional.fromSTEB(16.0, 0.0, 16.0, 0.0),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                      padding: const EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 0.0, 4.0),
                      child: Text(
                        'Description',
                        style: FlutterFlowTheme.of(context).bodyMedium,
                      ),
                    ),
                    TextFormField(
                      controller: _model.descriptionController,
                      focusNode: _model.descriptionFocusNode,
                      maxLines: 3,
                      decoration: InputDecoration(
                        isDense: true,
                        hintText: 'Enter site description',
                        enabledBorder: OutlineInputBorder(
                          borderSide: BorderSide(
                            color: FlutterFlowTheme.of(context).alternate,
                            width: 1.0,
                          ),
                          borderRadius: BorderRadius.circular(8.0),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderSide: BorderSide(
                            color: FlutterFlowTheme.of(context).primary,
                            width: 1.0,
                          ),
                          borderRadius: BorderRadius.circular(8.0),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              Padding(
                padding: const EdgeInsetsDirectional.fromSTEB(16.0, 0.0, 16.0, 0.0),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                      padding: const EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 0.0, 4.0),
                      child: Text(
                        'Location',
                        style: FlutterFlowTheme.of(context).bodyMedium,
                      ),
                    ),
                    // DropdownButtonFormField<String>(
                    //   value: _model.selectedLocation?['name'],
                    //   onChanged: (val) => setState(() {
                    //     _model.selectedLocation = _locations.firstWhere(
                    //       (location) => location['name'] == val,
                    //     );
                    //   }),
                    //   items: _locations
                    //       .map((location) => DropdownMenuItem(
                    //             value: location['name'] as String,
                    //             child: Text(location['name'] as String),
                    //           ))
                    //       .toList(),
                    //   decoration: InputDecoration(
                    //     isDense: true,
                    //     hintText: 'Select Location',
                    //     enabledBorder: OutlineInputBorder(
                    //       borderSide: BorderSide(
                    //         color: FlutterFlowTheme.of(context).alternate,
                    //         width: 1.0,
                    //       ),
                    //       borderRadius: BorderRadius.circular(8.0),
                    //     ),
                    //     focusedBorder: OutlineInputBorder(
                    //       borderSide: BorderSide(
                    //         color: FlutterFlowTheme.of(context).primary,
                    //         width: 1.0,
                    //       ),
                    //       borderRadius: BorderRadius.circular(8.0),
                    //     ),
                    //     filled: true,
                    //     fillColor: FlutterFlowTheme.of(context).secondaryBackground,
                    //   ),
                    // ),

                    FlutterFlowPlacePicker(
                      iOSGoogleMapsApiKey: '',
                      androidGoogleMapsApiKey: '',
                      webGoogleMapsApiKey: FFAppConstants.googleMapsKeyWeb,
                      proxyBaseUrl: FFAppConstants.googleMapPlacePickerProxyUrl,
                      onSelect: (place) async {
                        setState(() {
                          _model.placePickerValue = place;
                          print('Zip code -- $place');
                          _model.selectedLocation = {
                            'name': place.name ?? '',
                            'lat': place.latLng.latitude,
                            'lng': place.latLng.longitude,
                            'zipCode': place.zipCode ?? ''
                          };
                        });
                        print('Selected place: ${place.name} at ${place.latLng}');
                      },

                      defaultText: 'Select Location',
                      icon: Icon(
                        Icons.place,
                        color: FlutterFlowTheme.of(context).primary,
                        size: 20.0,
                      ),
                      buttonOptions: FFButtonOptions(
                        width: double.infinity,
                        height: 48.0,
                        color: FlutterFlowTheme.of(context).secondaryBackground,
                        textStyle: FlutterFlowTheme.of(context).titleSmall.override(
                              fontFamily: 'Open Sans',
                              color: FlutterFlowTheme.of(context).primaryText,
                            ),
                        elevation: 0.0,
                        borderSide: BorderSide(
                          color: FlutterFlowTheme.of(context).alternate,
                          width: 1.0,
                        ),
                        borderRadius: BorderRadius.circular(8.0),
                      ),
                    ),
                  ],
                ),
              ),
              Padding(
                padding: const EdgeInsetsDirectional.fromSTEB(16.0, 0.0, 16.0, 0.0),
                child: Row(
                  mainAxisSize: MainAxisSize.max,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    FFButtonWidget(
                      onPressed: () async {
                        Navigator.pop(context);
                      },
                      text: 'Cancel',
                      options: FFButtonOptions(
                        width: 120.0,
                        height: 48.0,
                        padding: const EdgeInsets.all(8.0),
                        iconPadding: const EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 0.0, 0.0),
                        color: FlutterFlowTheme.of(context).secondaryBackground,
                        textStyle: FlutterFlowTheme.of(context).bodyLarge,
                        elevation: 0.0,
                        borderSide: BorderSide(
                          color: FlutterFlowTheme.of(context).alternate,
                          width: 1.0,
                        ),
                        borderRadius: BorderRadius.circular(8.0),
                      ),
                    ),
                    FFButtonWidget(
                      onPressed: () async {
                        if (_model.siteNameController.text.isEmpty ||
                            _model.descriptionController.text.isEmpty ||
                            _model.selectedLocation == null) {
                          await action_blocks.snackbarAction(
                            context,
                            snackbarMessage: 'Please fill all fields',
                          );
                          return;
                        }
                        try {
                          final sitesRef = FirebaseFirestore.instance.collection('sites');
                          final siteDocRef = sitesRef.doc();
                          final siteId = siteDocRef.id;

                          const defaultOpenTime = TimeOfDay(hour: 6, minute: 0); // 6:00 AM
                          const defaultCloseTime = TimeOfDay(hour: 17, minute: 0); // 5:00 PM

                          // First create the schedule
                          final scheduleRef = FirebaseFirestore.instance.collection('site_schedules').doc();
                          final scheduleId = scheduleRef.id;

                          await scheduleRef.set({
                            'siteScheduleId': scheduleId,
                            'siteId': siteId,
                            'weeklySchedule': createDefaultWeeklySchedule(
                              defaultOpenTime,
                              defaultCloseTime,
                            ),
                          });

                          // Format TimeOfDay to string
                          final openTimeString =
                              '${defaultOpenTime.hour.toString().padLeft(2, '0')}:${defaultOpenTime.minute.toString().padLeft(2, '0')}';
                          final closeTimeString =
                              '${defaultCloseTime.hour.toString().padLeft(2, '0')}:${defaultCloseTime.minute.toString().padLeft(2, '0')}';

                          // Create weekly hours map
                          final Map<String, dynamic> weeklyHours = {
                            'monday': {
                              'openTime': openTimeString,
                              'closeTime': closeTimeString,
                              'isOpen': true
                            },
                            'tuesday': {
                              'openTime': openTimeString,
                              'closeTime': closeTimeString,
                              'isOpen': true
                            },
                            'wednesday': {
                              'openTime': openTimeString,
                              'closeTime': closeTimeString,
                              'isOpen': true
                            },
                            'thursday': {
                              'openTime': openTimeString,
                              'closeTime': closeTimeString,
                              'isOpen': true
                            },
                            'friday': {
                              'openTime': openTimeString,
                              'closeTime': closeTimeString,
                              'isOpen': true
                            },
                            'saturday': {
                              'openTime': openTimeString,
                              'closeTime': closeTimeString,
                              'isOpen': true
                            },
                            'sunday': {
                              'openTime': openTimeString,
                              'closeTime': closeTimeString,
                              'isOpen': false
                            },
                          };

                          // Then create the site with the schedule reference and weekly hours
                          await siteDocRef.set({
                            'siteName': _model.siteNameController.text,
                            'description': _model.descriptionController.text,
                            'siteLocation': _model.selectedLocation!['name'],
                            'siteLatLng': GeoPoint(
                              _model.selectedLocation!['lat'] as double,
                              _model.selectedLocation!['lng'] as double,
                            ),
                            'siteId': siteId,
                            'siteAdminId': '',
                            'scheduleId': scheduleId,
                            'weeklyHours': weeklyHours,
                            'createdAt': getCurrentTimestamp,
                            'zipCode':
                                _model.selectedLocation!['zipCode'], // Added zip code from selected location
                          });

                          Navigator.pop(context);
                          return;
                        } catch (e) {
                          print('Error -- $e');
                        }
                        Navigator.pop(context);
                      },
                      text: 'Confirm',
                      options: FFButtonOptions(
                        width: 120.0,
                        height: 48.0,
                        padding: const EdgeInsets.all(8.0),
                        iconPadding: const EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 0.0, 0.0),
                        color: FlutterFlowTheme.of(context).primary,
                        textStyle: FlutterFlowTheme.of(context).titleSmall.override(
                              fontFamily: 'Open Sans',
                              color: Colors.white,
                              letterSpacing: 0.0,
                            ),
                        elevation: 0.0,
                        borderSide: const BorderSide(
                          color: Colors.transparent,
                          width: 1.0,
                        ),
                        borderRadius: BorderRadius.circular(8.0),
                      ),
                    ),
                  ],
                ),
              ),
            ].divide(const SizedBox(height: 16.0)),
          ),
        ),
      ),
    );
  }
}
