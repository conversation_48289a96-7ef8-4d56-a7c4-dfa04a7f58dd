import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:get/get.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/flutter_flow/flutter_flow_place_picker.dart';
import 'site_detail_screen_widget.dart' show SiteDetailScreenWidget;

class SiteDetailScreenModel extends FlutterFlowModel<SiteDetailScreenWidget> {
  ///  State fields for stateful widgets in this page.

  // Reference to the site document
  DocumentReference? siteRef;
  RxBool isLoading = false.obs;
  bool isEditing = false;
  bool isInitialized = false;
  bool isViewOnly = false;

  // State field for switch
  final switchValue1 = false.obs; // Changed from bool? to RxBool

  // State field for PlacePicker
  final Rx<FFPlace> placePickerValue = const FFPlace().obs;

  // Controllers for form fields
  TextEditingController? siteNameFieldTextController;
  TextEditingController? siteDescriptionFieldTextController;
  TextEditingController? phoneNoFieldTextController;
  TextEditingController? addressFieldTextController;
  TextEditingController? latitudeFieldTextController;
  TextEditingController? logitudeFieldTextController;
  TextEditingController? zipCodeFieldTextController; // Added zip code controller

  // Focus nodes for form fields
  FocusNode? siteNameFieldFocusNode;
  FocusNode? siteDescriptionFieldFocusNode;
  FocusNode? phoneNoFieldFocusNode;
  FocusNode? addressFieldFocusNode;
  FocusNode? latitudeFieldFocusNode;
  FocusNode? logitudeFieldFocusNode;
  FocusNode? zipCodeFieldFocusNode; // Added zip code focus node

  // Validators for form fields
  String? Function(BuildContext, String?)? siteNameFieldTextControllerValidator;
  String? Function(BuildContext, String?)? siteDescriptionFieldTextControllerValidator;
  String? Function(BuildContext, String?)? phoneNoFieldTextControllerValidator;
  String? Function(BuildContext, String?)? addressFieldTextControllerValidator;
  String? Function(BuildContext, String?)? latitudeFieldTextControllerValidator;
  String? Function(BuildContext, String?)? logitudeFieldTextControllerValidator;
  String? Function(BuildContext, String?)? zipCodeFieldTextControllerValidator; // Added zip code validator

  // Add this to store temporary weekly hours changes
  Map<String, Map<String, dynamic>> tempWeeklyHours = {};
  void updatePlacePickerValue(FFPlace place) {
    placePickerValue.value = place;
    addressFieldTextController.text = place.address ?? '';
    latitudeFieldTextController.text = place.latLng.latitude.toString();
    logitudeFieldTextController.text = place.latLng.longitude.toString();
    zipCodeFieldTextController.text = place.zipCode ?? '';

    // Debug prints
    print('Selected place: ${place.name}');
    print('Address: ${place.address}');
    print('LatLng: ${place.latLng}');
    print('Zip code: ${place.zipCode}');
  }

  // Method to update temporary weekly hours
  void updateTempWeeklyHours(String day, String openTime, String closeTime) {
    tempWeeklyHours[day] = {
      'openTime': openTime,
      'closeTime': closeTime,
      'isOpen': tempWeeklyHours[day]?['isOpen'] ?? true,
    };
  }

  // Weekly Hours
  RxMap<String, Map<String, dynamic>> weeklyHours = RxMap<String, Map<String, dynamic>>({
    'monday': {'openTime': '09:00', 'closeTime': '17:00', 'isOpen': true},
    'tuesday': {'openTime': '09:00', 'closeTime': '17:00', 'isOpen': true},
    'wednesday': {'openTime': '09:00', 'closeTime': '17:00', 'isOpen': true},
    'thursday': {'openTime': '09:00', 'closeTime': '17:00', 'isOpen': true},
    'friday': {'openTime': '09:00', 'closeTime': '17:00', 'isOpen': true},
    'saturday': {'openTime': '09:00', 'closeTime': '17:00', 'isOpen': true},
    'sunday': {'openTime': '09:00', 'closeTime': '17:00', 'isOpen': false},
  });

  void initializeWithData(Map<String, dynamic> siteData) {
    siteNameFieldTextController.text = siteData['siteName'] ?? '';
    siteDescriptionFieldTextController.text = siteData['description'] ?? '';
    phoneNoFieldTextController.text = siteData['contactPhone'] ?? '';
    addressFieldTextController.text = siteData['siteLocation'] ?? '';
    zipCodeFieldTextController.text = siteData['zipCode'] ?? '';

    if (siteData['weeklyHours'] != null) {
      weeklyHours.value = Map<String, Map<String, dynamic>>.from(siteData['weeklyHours']);
    }

    if (siteData['siteLatLng'] != null) {
      final GeoPoint latLng = siteData['siteLatLng'] as GeoPoint;
      latitudeFieldTextController.text = latLng.latitude.toString();
      logitudeFieldTextController.text = latLng.longitude.toString();
    }
  }

  void updateWeeklyHours(String day, String openTime, String closeTime, bool isOpen) {
    weeklyHours[day] = {
      'openTime': openTime,
      'closeTime': closeTime,
      'isOpen': isOpen,
    };
  }

  void toggleDayStatus(String day, bool isOpen) {
    if (weeklyHours.containsKey(day)) {
      weeklyHours[day]!['isOpen'] = isOpen;
      weeklyHours.refresh(); // Notify listeners of the change
    } else {
      weeklyHours[day] = {
        'openTime': '09:00',
        'closeTime': '17:00',
        'isOpen': isOpen,
      };
    }
  }

  @override
  void initState(BuildContext context) {
    Get.put(this); // Add this line to make the model available globally
    switchValue1.value = false; // Initialize switch value

    siteNameFieldTextController ??= TextEditingController();
    siteDescriptionFieldTextController ??= TextEditingController();
    phoneNoFieldTextController ??= TextEditingController();
    addressFieldTextController ??= TextEditingController();
    latitudeFieldTextController ??= TextEditingController();
    logitudeFieldTextController ??= TextEditingController();
    zipCodeFieldTextController ??= TextEditingController(); // Initialize zip code controller

    siteNameFieldFocusNode ??= FocusNode();
    siteDescriptionFieldFocusNode ??= FocusNode();
    phoneNoFieldFocusNode ??= FocusNode();
    addressFieldFocusNode ??= FocusNode();
    latitudeFieldFocusNode ??= FocusNode();
    logitudeFieldFocusNode ??= FocusNode();
    zipCodeFieldFocusNode ??= FocusNode(); // Initialize zip code focus node

    // Initialize validators
    siteNameFieldTextControllerValidator ??= _defaultValidator;
    siteDescriptionFieldTextControllerValidator ??= _defaultValidator;
    phoneNoFieldTextControllerValidator ??= _defaultValidator;
    addressFieldTextControllerValidator ??= _defaultValidator;
    latitudeFieldTextControllerValidator ??= _defaultValidator;
    logitudeFieldTextControllerValidator ??= _defaultValidator;
    zipCodeFieldTextControllerValidator ??= _defaultValidator; // Initialize zip code validator
  }

  // Default validator function
  String? _defaultValidator(BuildContext context, String? value) {
    if (value == null || value.isEmpty) {
      return 'Field is required';
    }
    return null;
  }

  @override
  void dispose() {
    Get.delete<SiteDetailScreenModel>(); // Clean up when disposing
    siteNameFieldFocusNode?.dispose();
    siteDescriptionFieldFocusNode?.dispose();
    phoneNoFieldFocusNode?.dispose();
    addressFieldFocusNode?.dispose();
    latitudeFieldFocusNode?.dispose();
    logitudeFieldFocusNode?.dispose();
    zipCodeFieldFocusNode?.dispose(); // Dispose zip code focus node

    siteNameFieldTextController?.dispose();
    siteDescriptionFieldTextController?.dispose();
    phoneNoFieldTextController?.dispose();
    addressFieldTextController?.dispose();
    latitudeFieldTextController?.dispose();
    logitudeFieldTextController?.dispose();
    zipCodeFieldTextController?.dispose(); // Dispose zip code controller
  }

  // Add methods to handle site data
  Future<void> updateSiteData() async {
    if (siteRef == null) return;

    try {
      isLoading.value = true;
      await siteRef!.update({
        'siteName': siteNameFieldTextController?.text,
        'description': siteDescriptionFieldTextController?.text,
        'phone': phoneNoFieldTextController?.text,
        'siteLocation': addressFieldTextController?.text,
        'zipCode': zipCodeFieldTextController?.text, // Add zip code to update
        'siteLatLng': GeoPoint(
          double.tryParse(latitudeFieldTextController?.text ?? '') ?? 0,
          double.tryParse(logitudeFieldTextController?.text ?? '') ?? 0,
        ),
      });
      isLoading.value = false;
      isEditing = false;
    } catch (e) {
      print('Error updating site: $e');
      isLoading.value = false;
    }
  }
}
