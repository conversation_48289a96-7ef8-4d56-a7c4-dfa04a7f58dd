import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:unimetals_admin/backend/schema/site_schedules_record.dart';
import 'package:unimetals_admin/pages/site_management/slots_component/slots_component_widget.dart';
import 'package:unimetals_admin/utils/schedule_utils.dart';

import '/flutter_flow/flutter_flow_place_picker.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/flutter_flow/flutter_flow_widgets.dart';
import 'site_detail_screen_model.dart';

export 'site_detail_screen_model.dart';

class SiteDetailScreenWidget extends StatefulWidget {
  const SiteDetailScreenWidget({
    super.key,
    required this.siteRef,
    this.isViewOnly = false,
  });

  final String? siteRef;
  final bool isViewOnly;

  static String routeName = 'site_detail_screen';
  static String routePath = '/siteDetailScreen';

  @override
  State<SiteDetailScreenWidget> createState() => _SiteDetailScreenWidgetState();
}

class _SiteDetailScreenWidgetState extends State<SiteDetailScreenWidget> {
  late SiteDetailScreenModel _model;
  final scaffoldKey = GlobalKey<ScaffoldState>();
  late DocumentReference? siteRef;

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => SiteDetailScreenModel());

    // Initialize the siteRef using the passed siteRef parameter
    if (widget.siteRef != null) {
      siteRef = FirebaseFirestore.instance.collection('sites').doc(widget.siteRef);
      _model.siteRef = siteRef;
    }

    // Set view-only mode from widget parameter
    _model.isViewOnly = widget.isViewOnly;
  }

  @override
  void dispose() {
    _model.dispose();

    super.dispose();
  }

  String formatTime(String time24) {
    try {
      final parts = time24.split(':');
      int hours = int.parse(parts[0]);
      final minutes = parts[1];
      final period = hours >= 12 ? 'PM' : 'AM';

      // Convert to 12-hour format
      hours = hours > 12 ? hours - 12 : hours;
      // Handle midnight (00:00) case
      hours = hours == 0 ? 12 : hours;

      return '$hours:$minutes $period';
    } catch (e) {
      return time24;
    }
  }

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<DocumentSnapshot>(
        stream: siteRef?.snapshots(),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          }

          if (!snapshot.hasData || !snapshot.data!.exists) {
            return Scaffold(
              appBar: AppBar(
                title: const Text('Site Details'),
              ),
              body: const Center(child: Text('Site not found')),
            );
          }

          final siteData = snapshot.data!.data() as Map<String, dynamic>;

          // Initialize the form controllers with the site data
          if (!_model.isInitialized) {
            _model.initializeWithData(siteData);
            _model.isInitialized = true;
          }

          return GestureDetector(
            onTap: () {
              FocusScope.of(context).unfocus();
              FocusManager.instance.primaryFocus?.unfocus();
            },
            child: Scaffold(
              key: scaffoldKey,
              backgroundColor: FlutterFlowTheme.of(context).primaryBackground,
              body: SafeArea(
                top: true,
                child: Column(
                  mainAxisSize: MainAxisSize.max,
                  children: [
                    Container(
                      width: double.infinity,
                      height: 100.0,
                      decoration: BoxDecoration(
                        color: FlutterFlowTheme.of(context).primary,
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.max,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Padding(
                            padding: const EdgeInsetsDirectional.fromSTEB(30.0, 0.0, 0.0, 0.0),
                            child: Text(
                              'Site Details',
                              style: FlutterFlowTheme.of(context).bodyMedium.override(
                                    fontFamily: 'Open Sans',
                                    color: FlutterFlowTheme.of(context).whiteColor,
                                    fontSize: 26.0,
                                    letterSpacing: 0.0,
                                    fontWeight: FontWeight.w600,
                                  ),
                            ),
                          ),
                          Builder(
                              builder: (context) => Padding(
                                    padding: const EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 30.0, 0.0),
                                    child: Obx(
                                      () => FFButtonWidget(
                                        onPressed: _model.isLoading.value
                                            ? () {}
                                            : () async {
                                                try {
                                                  // Show loading indicator
                                                  _model.isLoading.value = true;

                                                  // Prepare the update data
                                                  final updateData = {
                                                    'siteName': _model.siteNameFieldTextController.text,
                                                    'description': _model.siteDescriptionFieldTextController.text,
                                                    'contactPhone': _model.phoneNoFieldTextController.text,
                                                    'siteLocation': _model.addressFieldTextController.text,
                                                    'zipCode': _model.zipCodeFieldTextController.text,
                                                    'siteLatLng': GeoPoint(
                                                      double.tryParse(_model.latitudeFieldTextController.text) ?? 0,
                                                      double.tryParse(_model.logitudeFieldTextController.text) ?? 0,
                                                    ),
                                                    'weeklyHours': _model.weeklyHours, // Add weekly hours to update data
                                                  };
                                                  final siteDoc = await _model.siteRef?.get();

                                                  final scheduleId = siteDoc?['scheduleId'];

                                                  // Fetch the existing schedule to preserve time slot settings
                                                  final scheduleDoc = await FirebaseFirestore.instance.collection('site_schedules').doc(scheduleId).get();

                                                  final existingSchedule = scheduleDoc.exists ? SiteSchedulesRecord.fromSnapshot(scheduleDoc) : null;

                                                  // Update the document
                                                  await _model.siteRef?.update(updateData);
                                                  if (scheduleId != null) {
                                                    final scheduleRef = FirebaseFirestore.instance.collection('site_schedules').doc(scheduleId);

                                                    // Convert weekly hours to the schedule format with time slots
                                                    final weeklySchedule = _model.weeklyHours.map((day, hours) {
                                                      final openTime = TimeOfDay(
                                                        hour: int.parse(hours['openTime'].split(':')[0]),
                                                        minute: int.parse(hours['openTime'].split(':')[1]),
                                                      );
                                                      final closeTime = TimeOfDay(
                                                        hour: int.parse(hours['closeTime'].split(':')[0]),
                                                        minute: int.parse(hours['closeTime'].split(':')[1]),
                                                      );

                                                      // Get existing time slots for this day to preserve disabled status
                                                      final existingTimeSlots = existingSchedule?.weeklySchedule[day]?.timeSlots ?? [];

                                                      // Generate new time slots but preserve disabled status from existing ones
                                                      final newTimeSlots = generateTimeSlots(openTime, closeTime);
                                                      final mergedTimeSlots = newTimeSlots.map((slotMap) {
                                                        final time = slotMap['time'] as String;
                                                        // Find if this time existed in previous slots
                                                        final existingSlot = existingTimeSlots.firstWhere(
                                                          (slot) => slot.time == time,
                                                          orElse: () => const TimeSlot(time: '', isDisabledByAdmin: false),
                                                        );

                                                        // Preserve the disabled status if the slot existed before
                                                        return TimeSlot(
                                                          time: time,
                                                          isDisabledByAdmin: existingSlot.time.isNotEmpty ? existingSlot.isDisabledByAdmin : false,
                                                        );
                                                      }).toList();

                                                      return MapEntry(
                                                        day,
                                                        DaySchedule(
                                                          isOpen: hours['isOpen'] ?? false,
                                                          openTime: openTime,
                                                          closeTime: closeTime,
                                                          timeSlots: mergedTimeSlots,
                                                        ).toMap(),
                                                      );
                                                    });

                                                    await scheduleRef.update({
                                                      'weeklySchedule': weeklySchedule,
                                                      'lastUpdated': FieldValue.serverTimestamp(),
                                                    });
                                                  }
                                                  // Show success message
                                                  ScaffoldMessenger.of(context).showSnackBar(
                                                    SnackBar(
                                                      content: Text(
                                                        'Changes saved successfully!',
                                                        style: FlutterFlowTheme.of(context).bodyMedium.override(
                                                              fontFamily: 'Open Sans',
                                                              color: Colors.white,
                                                            ),
                                                      ),
                                                      backgroundColor: FlutterFlowTheme.of(context).success,
                                                      duration: const Duration(seconds: 2),
                                                    ),
                                                  );
                                                  Navigator.pop(context);
                                                } catch (error) {
                                                  print('Error saving changes: $error'); // Add error logging
                                                  // Show error message
                                                  ScaffoldMessenger.of(context).showSnackBar(
                                                    SnackBar(
                                                      content: Text(
                                                        'Error saving changes: $error',
                                                        style: FlutterFlowTheme.of(context).bodyMedium.override(
                                                              fontFamily: 'Open Sans',
                                                              color: Colors.white,
                                                            ),
                                                      ),
                                                      backgroundColor: FlutterFlowTheme.of(context).error,
                                                      duration: const Duration(seconds: 2),
                                                    ),
                                                  );
                                                } finally {
                                                  _model.isLoading.value = false;
                                                }
                                              },
                                        text: _model.isLoading.value ? 'Saving...' : 'Save Changes',
                                        options: FFButtonOptions(
                                          height: 40.0,
                                          padding: const EdgeInsetsDirectional.fromSTEB(16.0, 0.0, 16.0, 0.0),
                                          iconPadding: const EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 0.0, 0.0),
                                          color: FlutterFlowTheme.of(context).secondary,
                                          textStyle: FlutterFlowTheme.of(context).titleSmall.override(
                                                fontFamily: 'Open Sans',
                                                color: Colors.white,
                                                letterSpacing: 0.0,
                                                fontWeight: FontWeight.bold,
                                              ),
                                          elevation: 0.0,
                                          borderRadius: BorderRadius.circular(10.0),
                                          disabledColor: FlutterFlowTheme.of(context).alternate,
                                        ),
                                      ),
                                    ),
                                  )),
                        ],
                      ),
                    ),
                    Expanded(
                      child: SingleChildScrollView(
                        child: Column(
                          mainAxisSize: MainAxisSize.max,
                          children: [
                            Padding(
                              padding: const EdgeInsets.all(50.0),
                              child: Container(
                                width: double.infinity,
                                decoration: BoxDecoration(
                                  color: FlutterFlowTheme.of(context).secondaryBackground,
                                  boxShadow: const [
                                    BoxShadow(
                                      blurRadius: 4.0,
                                      color: Color(0x33000000),
                                      offset: Offset(
                                        0.0,
                                        2.0,
                                      ),
                                    )
                                  ],
                                  borderRadius: BorderRadius.circular(15.0),
                                ),
                                child: Padding(
                                  padding: const EdgeInsets.all(20.0),
                                  child: Column(
                                    mainAxisSize: MainAxisSize.max,
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Row(
                                        mainAxisSize: MainAxisSize.max,
                                        mainAxisAlignment: MainAxisAlignment.center,
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          Column(
                                            mainAxisSize: MainAxisSize.max,
                                            crossAxisAlignment: CrossAxisAlignment.start,
                                            children: [
                                              const SizedBox(
                                                height: 5,
                                              ),
                                              Text(
                                                'Site name',
                                                style: FlutterFlowTheme.of(context).bodyMedium.override(
                                                      fontFamily: 'Open Sans',
                                                      fontSize: 16.0,
                                                      letterSpacing: 0.0,
                                                      fontWeight: FontWeight.w500,
                                                    ),
                                              ),
                                              Padding(
                                                padding: const EdgeInsetsDirectional.fromSTEB(0.0, 8.0, 0.0, 0.0),
                                                child: SizedBox(
                                                  width: 500.0,
                                                  child: TextFormField(
                                                    controller: _model.siteNameFieldTextController,
                                                    focusNode: _model.siteNameFieldFocusNode,
                                                    autofocus: false,
                                                    textCapitalization: TextCapitalization.words,
                                                    obscureText: false,
                                                    decoration: InputDecoration(
                                                      isDense: false,
                                                      labelStyle: FlutterFlowTheme.of(context).bodyMedium.override(
                                                            fontFamily: 'Open Sans',
                                                            color: FlutterFlowTheme.of(context).blackColor,
                                                            fontSize: 16.0,
                                                            letterSpacing: 0.0,
                                                          ),
                                                      alignLabelWithHint: false,
                                                      hintStyle: FlutterFlowTheme.of(context).labelMedium.override(
                                                            fontFamily: 'Open Sans',
                                                            color: FlutterFlowTheme.of(context).blackColor,
                                                            fontSize: 16.0,
                                                            letterSpacing: 0.0,
                                                          ),
                                                      enabledBorder: OutlineInputBorder(
                                                        borderSide: BorderSide(
                                                          color: FlutterFlowTheme.of(context).alternate,
                                                          width: 1.0,
                                                        ),
                                                        borderRadius: BorderRadius.circular(10.0),
                                                      ),
                                                      focusedBorder: OutlineInputBorder(
                                                        borderSide: BorderSide(
                                                          color: FlutterFlowTheme.of(context).secondaryText,
                                                          width: 1.0,
                                                        ),
                                                        borderRadius: BorderRadius.circular(10.0),
                                                      ),
                                                      errorBorder: OutlineInputBorder(
                                                        borderSide: BorderSide(
                                                          color: FlutterFlowTheme.of(context).error,
                                                          width: 1.0,
                                                        ),
                                                        borderRadius: BorderRadius.circular(10.0),
                                                      ),
                                                      focusedErrorBorder: OutlineInputBorder(
                                                        borderSide: BorderSide(
                                                          color: FlutterFlowTheme.of(context).error,
                                                          width: 1.0,
                                                        ),
                                                        borderRadius: BorderRadius.circular(10.0),
                                                      ),
                                                      filled: true,
                                                      fillColor: FlutterFlowTheme.of(context).secondaryBackground,
                                                    ),
                                                    style: FlutterFlowTheme.of(context).bodyMedium.override(
                                                          fontFamily: 'Open Sans',
                                                          color: FlutterFlowTheme.of(context).blackColor,
                                                          fontSize: 16.0,
                                                          letterSpacing: 0.0,
                                                        ),
                                                    cursorColor: FlutterFlowTheme.of(context).primaryText,
                                                    validator: _model.siteNameFieldTextControllerValidator.asValidator(context),
                                                  ),
                                                ),
                                              ),
                                              Padding(
                                                padding: const EdgeInsetsDirectional.fromSTEB(0.0, 15.0, 0.0, 0.0),
                                                child: Text(
                                                  'Site Description',
                                                  style: FlutterFlowTheme.of(context).bodyMedium.override(
                                                        fontFamily: 'Open Sans',
                                                        fontSize: 16.0,
                                                        letterSpacing: 0.0,
                                                        fontWeight: FontWeight.w500,
                                                      ),
                                                ),
                                              ),
                                              Padding(
                                                padding: const EdgeInsetsDirectional.fromSTEB(0.0, 8.0, 0.0, 0.0),
                                                child: SizedBox(
                                                  width: 500.0,
                                                  child: TextFormField(
                                                    controller: _model.siteDescriptionFieldTextController,
                                                    focusNode: _model.siteDescriptionFieldFocusNode,
                                                    autofocus: false,
                                                    obscureText: false,
                                                    decoration: InputDecoration(
                                                      isDense: false,
                                                      labelStyle: FlutterFlowTheme.of(context).bodyMedium.override(
                                                            fontFamily: 'Open Sans',
                                                            color: FlutterFlowTheme.of(context).blackColor,
                                                            fontSize: 16.0,
                                                            letterSpacing: 0.0,
                                                          ),
                                                      alignLabelWithHint: false,
                                                      hintStyle: FlutterFlowTheme.of(context).labelMedium.override(
                                                            fontFamily: 'Open Sans',
                                                            color: FlutterFlowTheme.of(context).blackColor,
                                                            fontSize: 16.0,
                                                            letterSpacing: 0.0,
                                                          ),
                                                      enabledBorder: OutlineInputBorder(
                                                        borderSide: BorderSide(
                                                          color: FlutterFlowTheme.of(context).alternate,
                                                          width: 1.0,
                                                        ),
                                                        borderRadius: BorderRadius.circular(10.0),
                                                      ),
                                                      focusedBorder: OutlineInputBorder(
                                                        borderSide: BorderSide(
                                                          color: FlutterFlowTheme.of(context).secondaryText,
                                                          width: 1.0,
                                                        ),
                                                        borderRadius: BorderRadius.circular(10.0),
                                                      ),
                                                      errorBorder: OutlineInputBorder(
                                                        borderSide: BorderSide(
                                                          color: FlutterFlowTheme.of(context).error,
                                                          width: 1.0,
                                                        ),
                                                        borderRadius: BorderRadius.circular(10.0),
                                                      ),
                                                      focusedErrorBorder: OutlineInputBorder(
                                                        borderSide: BorderSide(
                                                          color: FlutterFlowTheme.of(context).error,
                                                          width: 1.0,
                                                        ),
                                                        borderRadius: BorderRadius.circular(10.0),
                                                      ),
                                                      filled: true,
                                                      fillColor: FlutterFlowTheme.of(context).secondaryBackground,
                                                    ),
                                                    style: FlutterFlowTheme.of(context).bodyMedium.override(
                                                          fontFamily: 'Open Sans',
                                                          color: FlutterFlowTheme.of(context).blackColor,
                                                          fontSize: 16.0,
                                                          letterSpacing: 0.0,
                                                        ),
                                                    maxLines: 4,
                                                    cursorColor: FlutterFlowTheme.of(context).primaryText,
                                                    validator: _model.siteDescriptionFieldTextControllerValidator.asValidator(context),
                                                  ),
                                                ),
                                              ),
                                              Padding(
                                                padding: const EdgeInsetsDirectional.fromSTEB(0.0, 15.0, 0.0, 0.0),
                                                child: Text(
                                                  'Phone No.',
                                                  style: FlutterFlowTheme.of(context).bodyMedium.override(
                                                        fontFamily: 'Open Sans',
                                                        fontSize: 16.0,
                                                        letterSpacing: 0.0,
                                                        fontWeight: FontWeight.w500,
                                                      ),
                                                ),
                                              ),
                                              Padding(
                                                padding: const EdgeInsetsDirectional.fromSTEB(0.0, 8.0, 0.0, 0.0),
                                                child: SizedBox(
                                                  width: 500.0,
                                                  child: TextFormField(
                                                    controller: _model.phoneNoFieldTextController,
                                                    focusNode: _model.phoneNoFieldFocusNode,
                                                    autofocus: false,
                                                    obscureText: false,
                                                    decoration: InputDecoration(
                                                      isDense: false,
                                                      labelStyle: FlutterFlowTheme.of(context).bodyMedium.override(
                                                            fontFamily: 'Open Sans',
                                                            color: FlutterFlowTheme.of(context).blackColor,
                                                            fontSize: 16.0,
                                                            letterSpacing: 0.0,
                                                          ),
                                                      alignLabelWithHint: false,
                                                      hintStyle: FlutterFlowTheme.of(context).labelMedium.override(
                                                            fontFamily: 'Open Sans',
                                                            color: FlutterFlowTheme.of(context).blackColor,
                                                            fontSize: 16.0,
                                                            letterSpacing: 0.0,
                                                          ),
                                                      enabledBorder: OutlineInputBorder(
                                                        borderSide: BorderSide(
                                                          color: FlutterFlowTheme.of(context).alternate,
                                                          width: 1.0,
                                                        ),
                                                        borderRadius: BorderRadius.circular(10.0),
                                                      ),
                                                      focusedBorder: OutlineInputBorder(
                                                        borderSide: BorderSide(
                                                          color: FlutterFlowTheme.of(context).secondaryText,
                                                          width: 1.0,
                                                        ),
                                                        borderRadius: BorderRadius.circular(10.0),
                                                      ),
                                                      errorBorder: OutlineInputBorder(
                                                        borderSide: BorderSide(
                                                          color: FlutterFlowTheme.of(context).error,
                                                          width: 1.0,
                                                        ),
                                                        borderRadius: BorderRadius.circular(10.0),
                                                      ),
                                                      focusedErrorBorder: OutlineInputBorder(
                                                        borderSide: BorderSide(
                                                          color: FlutterFlowTheme.of(context).error,
                                                          width: 1.0,
                                                        ),
                                                        borderRadius: BorderRadius.circular(10.0),
                                                      ),
                                                      filled: true,
                                                      fillColor: FlutterFlowTheme.of(context).secondaryBackground,
                                                    ),
                                                    style: FlutterFlowTheme.of(context).bodyMedium.override(
                                                          fontFamily: 'Open Sans',
                                                          color: FlutterFlowTheme.of(context).blackColor,
                                                          fontSize: 16.0,
                                                          letterSpacing: 0.0,
                                                        ),
                                                    keyboardType: const TextInputType.numberWithOptions(decimal: true),
                                                    cursorColor: FlutterFlowTheme.of(context).primaryText,
                                                    validator: _model.phoneNoFieldTextControllerValidator.asValidator(context),
                                                  ),
                                                ),
                                              ),
                                            ],
                                          ),
                                          const SizedBox(
                                            width: 40.0,
                                          ),
                                          Column(
                                            mainAxisSize: MainAxisSize.max,
                                            crossAxisAlignment: CrossAxisAlignment.start,
                                            children: [
                                              Text(
                                                'Location',
                                                style: FlutterFlowTheme.of(context).bodyMedium.override(
                                                      fontFamily: 'Open Sans',
                                                      fontSize: 20.0,
                                                      letterSpacing: 0.0,
                                                      fontWeight: FontWeight.w600,
                                                    ),
                                              ),
                                              Padding(
                                                padding: const EdgeInsetsDirectional.fromSTEB(0.0, 8.0, 0.0, 0.0),
                                                child: FlutterFlowPlacePicker(
                                                  iOSGoogleMapsApiKey: '',
                                                  androidGoogleMapsApiKey: '',
                                                  webGoogleMapsApiKey: FFAppConstants.googleMapsKeyWeb,
                                                  proxyBaseUrl: FFAppConstants.googleMapPlacePickerProxyUrl,
                                                  onSelect: (place) async {
                                                    _model.placePickerValue.value = place;
                                                    // Update address field
                                                    _model.addressFieldTextController.text = place.address ?? '';
                                                    // Update latitude field
                                                    _model.latitudeFieldTextController.text = place.latLng.latitude.toString();
                                                    // Update longitude field
                                                    _model.logitudeFieldTextController.text = place.latLng.longitude.toString();
                                                    // Update zip code field
                                                    _model.zipCodeFieldTextController.text = place.zipCode ?? '';
                                                    print('Selected place: ${place.name}');
                                                    print('Address: ${place.address}');
                                                    print('LatLng: ${place.latLng}');
                                                    print('Zip code: ${place.zipCode}');
                                                  },
                                                  defaultText: 'Select Location',
                                                  icon: Icon(
                                                    Icons.place,
                                                    color: FlutterFlowTheme.of(context).primary,
                                                    size: 20.0,
                                                  ),
                                                  buttonOptions: FFButtonOptions(
                                                    width: 500,
                                                    height: 48.0,
                                                    color: FlutterFlowTheme.of(context).secondaryBackground,
                                                    textStyle: FlutterFlowTheme.of(context).titleSmall.override(
                                                          fontFamily: 'Open Sans',
                                                          color: FlutterFlowTheme.of(context).primaryText,
                                                        ),
                                                    elevation: 0.0,
                                                    borderSide: BorderSide(
                                                      color: FlutterFlowTheme.of(context).alternate,
                                                      width: 1.0,
                                                    ),
                                                    borderRadius: BorderRadius.circular(8.0),
                                                  ),
                                                ),
                                              ),
                                              Padding(
                                                padding: const EdgeInsetsDirectional.fromSTEB(0.0, 15.0, 0.0, 0.0),
                                                child: Text(
                                                  'Address',
                                                  style: FlutterFlowTheme.of(context).bodyMedium.override(
                                                        fontFamily: 'Open Sans',
                                                        fontSize: 16.0,
                                                        letterSpacing: 0.0,
                                                        fontWeight: FontWeight.w500,
                                                      ),
                                                ),
                                              ),
                                              Padding(
                                                padding: const EdgeInsetsDirectional.fromSTEB(0.0, 8.0, 0.0, 0.0),
                                                child: SizedBox(
                                                  width: 500.0,
                                                  child: TextFormField(
                                                    controller: _model.addressFieldTextController,
                                                    focusNode: _model.addressFieldFocusNode,
                                                    autofocus: false,
                                                    obscureText: false,
                                                    decoration: InputDecoration(
                                                      isDense: false,
                                                      labelStyle: FlutterFlowTheme.of(context).bodyMedium.override(
                                                            fontFamily: 'Open Sans',
                                                            color: FlutterFlowTheme.of(context).blackColor,
                                                            fontSize: 16.0,
                                                            letterSpacing: 0.0,
                                                          ),
                                                      alignLabelWithHint: false,
                                                      hintStyle: FlutterFlowTheme.of(context).labelMedium.override(
                                                            fontFamily: 'Open Sans',
                                                            color: FlutterFlowTheme.of(context).blackColor,
                                                            fontSize: 16.0,
                                                            letterSpacing: 0.0,
                                                          ),
                                                      enabledBorder: OutlineInputBorder(
                                                        borderSide: BorderSide(
                                                          color: FlutterFlowTheme.of(context).alternate,
                                                          width: 1.0,
                                                        ),
                                                        borderRadius: BorderRadius.circular(10.0),
                                                      ),
                                                      focusedBorder: OutlineInputBorder(
                                                        borderSide: BorderSide(
                                                          color: FlutterFlowTheme.of(context).secondaryText,
                                                          width: 1.0,
                                                        ),
                                                        borderRadius: BorderRadius.circular(10.0),
                                                      ),
                                                      errorBorder: OutlineInputBorder(
                                                        borderSide: BorderSide(
                                                          color: FlutterFlowTheme.of(context).error,
                                                          width: 1.0,
                                                        ),
                                                        borderRadius: BorderRadius.circular(10.0),
                                                      ),
                                                      focusedErrorBorder: OutlineInputBorder(
                                                        borderSide: BorderSide(
                                                          color: FlutterFlowTheme.of(context).error,
                                                          width: 1.0,
                                                        ),
                                                        borderRadius: BorderRadius.circular(10.0),
                                                      ),
                                                      filled: true,
                                                      fillColor: FlutterFlowTheme.of(context).secondaryBackground,
                                                    ),
                                                    style: FlutterFlowTheme.of(context).bodyMedium.override(
                                                          fontFamily: 'Open Sans',
                                                          color: FlutterFlowTheme.of(context).blackColor,
                                                          fontSize: 16.0,
                                                          letterSpacing: 0.0,
                                                        ),
                                                    cursorColor: FlutterFlowTheme.of(context).primaryText,
                                                    validator: _model.addressFieldTextControllerValidator.asValidator(context),
                                                  ),
                                                ),
                                              ),
                                              Container(
                                                width: 500.0,
                                                decoration: const BoxDecoration(),
                                                child: Padding(
                                                  padding: const EdgeInsetsDirectional.fromSTEB(0.0, 15.0, 0.0, 0.0),
                                                  child: Row(
                                                    mainAxisSize: MainAxisSize.max,
                                                    children: [
                                                      Expanded(
                                                        child: Padding(
                                                          padding: const EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 5.0, 0.0),
                                                          child: Column(
                                                            mainAxisSize: MainAxisSize.max,
                                                            crossAxisAlignment: CrossAxisAlignment.start,
                                                            children: [
                                                              Text(
                                                                'Latitude',
                                                                style: FlutterFlowTheme.of(context).bodyMedium.override(
                                                                      fontFamily: 'Open Sans',
                                                                      fontSize: 16.0,
                                                                      letterSpacing: 0.0,
                                                                      fontWeight: FontWeight.w500,
                                                                    ),
                                                              ),
                                                              Padding(
                                                                padding: const EdgeInsetsDirectional.fromSTEB(0.0, 8.0, 0.0, 0.0),
                                                                child: TextFormField(
                                                                  controller: _model.latitudeFieldTextController,
                                                                  focusNode: _model.latitudeFieldFocusNode,
                                                                  autofocus: false,
                                                                  obscureText: false,
                                                                  decoration: InputDecoration(
                                                                    isDense: false,
                                                                    labelStyle: FlutterFlowTheme.of(context).bodyMedium.override(
                                                                          fontFamily: 'Open Sans',
                                                                          color: FlutterFlowTheme.of(context).blackColor,
                                                                          fontSize: 16.0,
                                                                          letterSpacing: 0.0,
                                                                        ),
                                                                    alignLabelWithHint: false,
                                                                    hintStyle: FlutterFlowTheme.of(context).labelMedium.override(
                                                                          fontFamily: 'Open Sans',
                                                                          color: FlutterFlowTheme.of(context).blackColor,
                                                                          fontSize: 16.0,
                                                                          letterSpacing: 0.0,
                                                                        ),
                                                                    enabledBorder: OutlineInputBorder(
                                                                      borderSide: BorderSide(
                                                                        color: FlutterFlowTheme.of(context).alternate,
                                                                        width: 1.0,
                                                                      ),
                                                                      borderRadius: BorderRadius.circular(10.0),
                                                                    ),
                                                                    focusedBorder: OutlineInputBorder(
                                                                      borderSide: BorderSide(
                                                                        color: FlutterFlowTheme.of(context).secondaryText,
                                                                        width: 1.0,
                                                                      ),
                                                                      borderRadius: BorderRadius.circular(10.0),
                                                                    ),
                                                                    errorBorder: OutlineInputBorder(
                                                                      borderSide: BorderSide(
                                                                        color: FlutterFlowTheme.of(context).error,
                                                                        width: 1.0,
                                                                      ),
                                                                      borderRadius: BorderRadius.circular(10.0),
                                                                    ),
                                                                    focusedErrorBorder: OutlineInputBorder(
                                                                      borderSide: BorderSide(
                                                                        color: FlutterFlowTheme.of(context).error,
                                                                        width: 1.0,
                                                                      ),
                                                                      borderRadius: BorderRadius.circular(10.0),
                                                                    ),
                                                                    filled: true,
                                                                    fillColor: FlutterFlowTheme.of(context).secondaryBackground,
                                                                  ),
                                                                  style: FlutterFlowTheme.of(context).bodyMedium.override(
                                                                        fontFamily: 'Open Sans',
                                                                        color: FlutterFlowTheme.of(context).blackColor,
                                                                        fontSize: 16.0,
                                                                        letterSpacing: 0.0,
                                                                      ),
                                                                  keyboardType: const TextInputType.numberWithOptions(decimal: true),
                                                                  cursorColor: FlutterFlowTheme.of(context).primaryText,
                                                                  validator: _model.latitudeFieldTextControllerValidator.asValidator(context),
                                                                ),
                                                              ),
                                                            ],
                                                          ),
                                                        ),
                                                      ),
                                                      Expanded(
                                                        child: Padding(
                                                          padding: const EdgeInsetsDirectional.fromSTEB(5.0, 0.0, 0.0, 0.0),
                                                          child: Column(
                                                            mainAxisSize: MainAxisSize.max,
                                                            crossAxisAlignment: CrossAxisAlignment.start,
                                                            children: [
                                                              Text(
                                                                'Longitude',
                                                                style: FlutterFlowTheme.of(context).bodyMedium.override(
                                                                      fontFamily: 'Open Sans',
                                                                      fontSize: 16.0,
                                                                      letterSpacing: 0.0,
                                                                      fontWeight: FontWeight.w500,
                                                                    ),
                                                              ),
                                                              Padding(
                                                                padding: const EdgeInsetsDirectional.fromSTEB(0.0, 8.0, 0.0, 0.0),
                                                                child: TextFormField(
                                                                  controller: _model.logitudeFieldTextController,
                                                                  focusNode: _model.logitudeFieldFocusNode,
                                                                  autofocus: false,
                                                                  obscureText: false,
                                                                  decoration: InputDecoration(
                                                                    isDense: false,
                                                                    labelStyle: FlutterFlowTheme.of(context).bodyMedium.override(
                                                                          fontFamily: 'Open Sans',
                                                                          color: FlutterFlowTheme.of(context).blackColor,
                                                                          fontSize: 16.0,
                                                                          letterSpacing: 0.0,
                                                                        ),
                                                                    alignLabelWithHint: false,
                                                                    hintStyle: FlutterFlowTheme.of(context).labelMedium.override(
                                                                          fontFamily: 'Open Sans',
                                                                          color: FlutterFlowTheme.of(context).blackColor,
                                                                          fontSize: 16.0,
                                                                          letterSpacing: 0.0,
                                                                        ),
                                                                    enabledBorder: OutlineInputBorder(
                                                                      borderSide: BorderSide(
                                                                        color: FlutterFlowTheme.of(context).alternate,
                                                                        width: 1.0,
                                                                      ),
                                                                      borderRadius: BorderRadius.circular(10.0),
                                                                    ),
                                                                    focusedBorder: OutlineInputBorder(
                                                                      borderSide: BorderSide(
                                                                        color: FlutterFlowTheme.of(context).secondaryText,
                                                                        width: 1.0,
                                                                      ),
                                                                      borderRadius: BorderRadius.circular(10.0),
                                                                    ),
                                                                    errorBorder: OutlineInputBorder(
                                                                      borderSide: BorderSide(
                                                                        color: FlutterFlowTheme.of(context).error,
                                                                        width: 1.0,
                                                                      ),
                                                                      borderRadius: BorderRadius.circular(10.0),
                                                                    ),
                                                                    focusedErrorBorder: OutlineInputBorder(
                                                                      borderSide: BorderSide(
                                                                        color: FlutterFlowTheme.of(context).error,
                                                                        width: 1.0,
                                                                      ),
                                                                      borderRadius: BorderRadius.circular(10.0),
                                                                    ),
                                                                    filled: true,
                                                                    fillColor: FlutterFlowTheme.of(context).secondaryBackground,
                                                                  ),
                                                                  style: FlutterFlowTheme.of(context).bodyMedium.override(
                                                                        fontFamily: 'Open Sans',
                                                                        color: FlutterFlowTheme.of(context).blackColor,
                                                                        fontSize: 16.0,
                                                                        letterSpacing: 0.0,
                                                                      ),
                                                                  keyboardType: const TextInputType.numberWithOptions(decimal: true),
                                                                  cursorColor: FlutterFlowTheme.of(context).primaryText,
                                                                  validator: _model.logitudeFieldTextControllerValidator.asValidator(context),
                                                                ),
                                                              ),
                                                            ],
                                                          ),
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                              ),
                                              Padding(
                                                padding: const EdgeInsetsDirectional.fromSTEB(0.0, 15.0, 0.0, 0.0),
                                                child: Text(
                                                  'Zip Code',
                                                  style: FlutterFlowTheme.of(context).bodyMedium.override(
                                                        fontFamily: 'Open Sans',
                                                        fontSize: 16.0,
                                                        letterSpacing: 0.0,
                                                        fontWeight: FontWeight.w500,
                                                      ),
                                                ),
                                              ),
                                              Padding(
                                                padding: const EdgeInsetsDirectional.fromSTEB(0.0, 8.0, 0.0, 0.0),
                                                child: SizedBox(
                                                  width: 500.0,
                                                  child: TextFormField(
                                                    controller: _model.zipCodeFieldTextController,
                                                    focusNode: _model.zipCodeFieldFocusNode,
                                                    autofocus: false,
                                                    obscureText: false,
                                                    decoration: InputDecoration(
                                                      isDense: false,
                                                      labelStyle: FlutterFlowTheme.of(context).bodyMedium.override(
                                                            fontFamily: 'Open Sans',
                                                            color: FlutterFlowTheme.of(context).blackColor,
                                                            fontSize: 16.0,
                                                            letterSpacing: 0.0,
                                                          ),
                                                      alignLabelWithHint: false,
                                                      hintStyle: FlutterFlowTheme.of(context).labelMedium.override(
                                                            fontFamily: 'Open Sans',
                                                            color: FlutterFlowTheme.of(context).blackColor,
                                                            fontSize: 16.0,
                                                            letterSpacing: 0.0,
                                                          ),
                                                      enabledBorder: OutlineInputBorder(
                                                        borderSide: BorderSide(
                                                          color: FlutterFlowTheme.of(context).alternate,
                                                          width: 1.0,
                                                        ),
                                                        borderRadius: BorderRadius.circular(10.0),
                                                      ),
                                                      focusedBorder: OutlineInputBorder(
                                                        borderSide: BorderSide(
                                                          color: FlutterFlowTheme.of(context).secondaryText,
                                                          width: 1.0,
                                                        ),
                                                        borderRadius: BorderRadius.circular(10.0),
                                                      ),
                                                      errorBorder: OutlineInputBorder(
                                                        borderSide: BorderSide(
                                                          color: FlutterFlowTheme.of(context).error,
                                                          width: 1.0,
                                                        ),
                                                        borderRadius: BorderRadius.circular(10.0),
                                                      ),
                                                      focusedErrorBorder: OutlineInputBorder(
                                                        borderSide: BorderSide(
                                                          color: FlutterFlowTheme.of(context).error,
                                                          width: 1.0,
                                                        ),
                                                        borderRadius: BorderRadius.circular(10.0),
                                                      ),
                                                      filled: true,
                                                      fillColor: FlutterFlowTheme.of(context).secondaryBackground,
                                                    ),
                                                    style: FlutterFlowTheme.of(context).bodyMedium.override(
                                                          fontFamily: 'Open Sans',
                                                          color: FlutterFlowTheme.of(context).blackColor,
                                                          fontSize: 16.0,
                                                          letterSpacing: 0.0,
                                                        ),
                                                    keyboardType: const TextInputType.numberWithOptions(decimal: true),
                                                    cursorColor: FlutterFlowTheme.of(context).primaryText,
                                                    validator: _model.zipCodeFieldTextControllerValidator.asValidator(context),
                                                  ),
                                                ),
                                              ),
                                            ],
                                          ),
                                        ],
                                      ),
                                      Column(
                                        mainAxisSize: MainAxisSize.max,
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          Padding(
                                            padding: const EdgeInsetsDirectional.fromSTEB(0.0, 20.0, 0.0, 0.0),
                                            child: Text(
                                              'Weekly Schedule',
                                              style: FlutterFlowTheme.of(context).bodyMedium.override(
                                                    fontFamily: 'Open Sans',
                                                    fontSize: 20.0,
                                                    letterSpacing: 0.0,
                                                    fontWeight: FontWeight.w600,
                                                  ),
                                            ),
                                          ),
                                          Container(
                                            width: double.infinity,
                                            decoration: const BoxDecoration(),
                                            child: Padding(
                                              padding: const EdgeInsetsDirectional.fromSTEB(0.0, 15.0, 0.0, 0.0),
                                              child: Wrap(
                                                spacing: 10.0,
                                                runSpacing: 10.0,
                                                children: [
                                                  for (var day in ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'])
                                                    Container(
                                                      decoration: BoxDecoration(
                                                        borderRadius: BorderRadius.circular(8.0),
                                                        border: Border.all(
                                                          color: FlutterFlowTheme.of(context).alternate,
                                                        ),
                                                      ),
                                                      child: Row(
                                                        mainAxisSize: MainAxisSize.min,
                                                        children: [
                                                          // Switch for open/closed status
                                                          Padding(
                                                            padding: const EdgeInsetsDirectional.fromSTEB(15.0, 0.0, 15.0, 0.0),
                                                            child: Transform.scale(
                                                              scale: 0.8,
                                                              child: Obx(() => Switch.adaptive(
                                                                    value: _model.weeklyHours[day]?['isOpen'] ?? false,
                                                                    onChanged: (newValue) {
                                                                      _model.toggleDayStatus(day, newValue);
                                                                    },
                                                                    activeColor: FlutterFlowTheme.of(context).primary,
                                                                    activeTrackColor: FlutterFlowTheme.of(context).primary,
                                                                    inactiveTrackColor: FlutterFlowTheme.of(context).alternate,
                                                                    inactiveThumbColor: FlutterFlowTheme.of(context).secondaryBackground,
                                                                  )),
                                                            ),
                                                          ),
                                                          // Schedule container
                                                          Container(
                                                            width: 300.0,
                                                            decoration: BoxDecoration(
                                                              color: FlutterFlowTheme.of(context).primaryBackground,
                                                              borderRadius: BorderRadius.circular(8.0),
                                                            ),
                                                            child: Padding(
                                                              padding: const EdgeInsets.all(7.0),
                                                              child: Row(
                                                                mainAxisSize: MainAxisSize.max,
                                                                children: [
                                                                  Expanded(
                                                                    child: Column(
                                                                      mainAxisSize: MainAxisSize.max,
                                                                      crossAxisAlignment: CrossAxisAlignment.start,
                                                                      children: [
                                                                        Padding(
                                                                          padding: const EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 0.0, 3.0),
                                                                          child: Text(
                                                                            day.capitalize!,
                                                                            style: FlutterFlowTheme.of(context).bodyMedium.override(
                                                                                  fontFamily: 'Open Sans',
                                                                                  fontSize: 16.0,
                                                                                  letterSpacing: 0.0,
                                                                                  fontWeight: FontWeight.w600,
                                                                                ),
                                                                          ),
                                                                        ),
                                                                        Obx(() {
                                                                          if (_model.weeklyHours[day]?['isOpen'] ?? false) {
                                                                            return MouseRegion(
                                                                              cursor: SystemMouseCursors.click,
                                                                              child: GestureDetector(
                                                                                onTap: () async {
                                                                                  final TimeOfDay? openResult = await showTimePicker(
                                                                                    context: context,
                                                                                    initialTime: TimeOfDay(
                                                                                      hour: int.parse(_model.weeklyHours[day]?['openTime']?.split(':')[0] ?? '9'),
                                                                                      minute: int.parse(_model.weeklyHours[day]?['openTime']?.split(':')[1] ?? '0'),
                                                                                    ),
                                                                                    builder: (context, child) {
                                                                                      return MediaQuery(
                                                                                        data: MediaQuery.of(context).copyWith(alwaysUse24HourFormat: false),
                                                                                        child: child!,
                                                                                      );
                                                                                    },
                                                                                  );

                                                                                  if (openResult != null) {
                                                                                    final TimeOfDay? closeResult = await showTimePicker(
                                                                                      context: context,
                                                                                      initialTime: TimeOfDay(
                                                                                        hour: int.parse(_model.weeklyHours[day]?['closeTime']?.split(':')[0] ?? '17'),
                                                                                        minute: int.parse(_model.weeklyHours[day]?['closeTime']?.split(':')[1] ?? '0'),
                                                                                      ),
                                                                                      builder: (context, child) {
                                                                                        return MediaQuery(
                                                                                          data: MediaQuery.of(context).copyWith(alwaysUse24HourFormat: false),
                                                                                          child: child!,
                                                                                        );
                                                                                      },
                                                                                    );

                                                                                    if (closeResult != null) {
                                                                                      final String openTime =
                                                                                          '${openResult.hour.toString().padLeft(2, '0')}:${openResult.minute.toString().padLeft(2, '0')}';
                                                                                      final String closeTime =
                                                                                          '${closeResult.hour.toString().padLeft(2, '0')}:${closeResult.minute.toString().padLeft(2, '0')}';

                                                                                      setState(() {
                                                                                        _model.updateWeeklyHours(
                                                                                          day,
                                                                                          openTime,
                                                                                          closeTime,
                                                                                          true,
                                                                                        );
                                                                                      });
                                                                                    }
                                                                                  }
                                                                                },
                                                                                child: Container(
                                                                                  decoration: BoxDecoration(
                                                                                    color: FlutterFlowTheme.of(context).secondaryBackground,
                                                                                    borderRadius: BorderRadius.circular(8.0),
                                                                                    border: Border.all(
                                                                                      color: FlutterFlowTheme.of(context).primary.withOpacity(0.3),
                                                                                      width: 1.0,
                                                                                    ),
                                                                                  ),
                                                                                  child: Padding(
                                                                                    padding: const EdgeInsets.symmetric(horizontal: 10.0, vertical: 6.0),
                                                                                    child: Row(
                                                                                      mainAxisSize: MainAxisSize.min,
                                                                                      children: [
                                                                                        Icon(
                                                                                          Icons.access_time,
                                                                                          size: 16.0,
                                                                                          color: FlutterFlowTheme.of(context).primary,
                                                                                        ),
                                                                                        const SizedBox(width: 6.0),
                                                                                        Text(
                                                                                          '${formatTime(_model.weeklyHours[day]?['openTime'] ?? '09:00')} - ${formatTime(_model.weeklyHours[day]?['closeTime'] ?? '17:00')}',
                                                                                          style: FlutterFlowTheme.of(context).bodyMedium.override(
                                                                                                fontFamily: 'Open Sans',
                                                                                                fontSize: 14.0,
                                                                                                letterSpacing: 0.0,
                                                                                                fontWeight: FontWeight.normal,
                                                                                              ),
                                                                                        ),
                                                                                        const SizedBox(width: 6.0),
                                                                                        Icon(
                                                                                          Icons.edit,
                                                                                          size: 14.0,
                                                                                          color: FlutterFlowTheme.of(context).secondaryText,
                                                                                        ),
                                                                                      ],
                                                                                    ),
                                                                                  ),
                                                                                ),
                                                                              ),
                                                                            );
                                                                          } else {
                                                                            return Container(
                                                                              width: 126,
                                                                              alignment: Alignment.center,
                                                                              decoration: BoxDecoration(
                                                                                color: FlutterFlowTheme.of(context).secondaryBackground,
                                                                                borderRadius: BorderRadius.circular(8.0),
                                                                              ),
                                                                              child: Padding(
                                                                                padding: const EdgeInsets.all(6.0),
                                                                                child: Text(
                                                                                  'Closed',
                                                                                  style: FlutterFlowTheme.of(context).bodyMedium.override(
                                                                                        fontFamily: 'Open Sans',
                                                                                        fontSize: 14.0,
                                                                                        letterSpacing: 0.0,
                                                                                        fontWeight: FontWeight.normal,
                                                                                      ),
                                                                                ),
                                                                              ),
                                                                            );
                                                                          }
                                                                        })
                                                                      ],
                                                                    ),
                                                                  ),
                                                                  Obx(() {
                                                                    if (_model.weeklyHours[day]?['isOpen'] ?? false) {
                                                                      return MouseRegion(
                                                                        cursor: SystemMouseCursors.click,
                                                                        child: GestureDetector(
                                                                          onTap: () async {
                                                                            if (widget.siteRef == null || widget.siteRef!.isEmpty) {
                                                                              print('Error: Invalid site reference');
                                                                              return;
                                                                            }

                                                                            // Get the current open and close times from the model
                                                                            final openTime = _model.weeklyHours[day]?['openTime'] ?? '09:00';
                                                                            final closeTime = _model.weeklyHours[day]?['closeTime'] ?? '17:00';

                                                                            await showDialog(
                                                                              context: context,
                                                                              builder: (dialogContext) {
                                                                                return Dialog(
                                                                                  elevation: 0,
                                                                                  insetPadding: EdgeInsets.zero,
                                                                                  backgroundColor: Colors.transparent,
                                                                                  alignment: const AlignmentDirectional(0.0, 0.0).resolve(Directionality.of(context)),
                                                                                  child: GestureDetector(
                                                                                    onTap: () {
                                                                                      FocusScope.of(dialogContext).unfocus();
                                                                                      FocusManager.instance.primaryFocus?.unfocus();
                                                                                    },
                                                                                    child: SlotsComponentWidget(
                                                                                      siteRef: widget.siteRef!,
                                                                                      dayName: day,
                                                                                      // Pass the current open/close times to the slots component
                                                                                      openTime: openTime,
                                                                                      closeTime: closeTime,
                                                                                    ),
                                                                                  ),
                                                                                );
                                                                              },
                                                                            );
                                                                          },
                                                                          child: SizedBox(
                                                                            height: 55,
                                                                            child: Icon(
                                                                              Icons.arrow_forward_ios,
                                                                              color: FlutterFlowTheme.of(context).secondaryText,
                                                                              size: 22.0,
                                                                            ),
                                                                          ),
                                                                        ),
                                                                      );
                                                                    }
                                                                    else{
                                                                      return const SizedBox.shrink();
                                                                    }
                                                                  })
                                                                ],
                                                              ),
                                                            ),
                                                          )
                                                        ],
                                                      ),
                                                    ),
                                                ],
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            )
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        });
  }
}
