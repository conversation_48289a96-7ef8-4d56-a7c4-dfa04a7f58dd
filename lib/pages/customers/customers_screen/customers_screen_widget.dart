import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:unimetals_admin/auth/firebase_auth/auth_util.dart';
import 'package:unimetals_admin/backend/schema/enums/enums.dart';
import 'package:unimetals_admin/backend/schema/enums/menu_items.dart';
import 'package:unimetals_admin/flutter_flow/flutter_flow_theme.dart';
import 'package:unimetals_admin/flutter_flow/flutter_flow_util.dart';
import 'package:unimetals_admin/pages/components/side_menu/side_menu_widget.dart';
import 'package:unimetals_admin/pages/components/empty_list_component/empty_list_component_widget.dart';
import '/backend/backend.dart';
import 'package:easy_debounce/easy_debounce.dart';
import 'customers_screen_model.dart';

export 'customers_screen_model.dart';

class CustomersScreenWidget extends StatefulWidget {
  const CustomersScreenWidget({super.key});

  static String routeName = 'customers_screen';
  static String routePath = '/customersScreen';

  @override
  State<CustomersScreenWidget> createState() => _CustomersScreenWidgetState();
}

class _CustomersScreenWidgetState extends State<CustomersScreenWidget> {
  late CustomersScreenModel _model;
  final scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  void initState() {
    super.initState();
    _model = CustomersScreenModel();
    _model.textController ??= TextEditingController();
  }

  @override
  void dispose() {
    _model.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => FocusScope.of(context).unfocus(),
      child: Scaffold(
        key: scaffoldKey,
        backgroundColor: FlutterFlowTheme.of(context).primaryBackground,
        body: SafeArea(
          top: true,
          child: Row(
            mainAxisSize: MainAxisSize.max,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SideMenuWidget(selectedItem: MenuItem.customers),
              Expanded(
                child: Container(
                  width: double.infinity,
                  height: double.infinity,
                  constraints: const BoxConstraints(
                    maxWidth: 1370.0,
                  ),
                  decoration: BoxDecoration(
                    color: FlutterFlowTheme.of(context).primaryBackground,
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.max,
                    children: [
                      // Header Section
                      Padding(
                        padding: const EdgeInsetsDirectional.fromSTEB(16.0, 26.0, 16.0, 15.0),
                        child: Row(
                          mainAxisSize: MainAxisSize.max,
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Expanded(
                              child: Column(
                                mainAxisSize: MainAxisSize.max,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'Customer Management',
                                    style: FlutterFlowTheme.of(context).bodyMedium.override(
                                          fontFamily: 'Open Sans',
                                          color: FlutterFlowTheme.of(context).blackColor,
                                          fontSize: 26.0,
                                          letterSpacing: 0.0,
                                          fontWeight: FontWeight.w600,
                                        ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                      // Main Content Area - Expanded to fill remaining height
                      Expanded(
                        child: Padding(
                          padding: const EdgeInsetsDirectional.fromSTEB(16.0, 0.0, 16.0, 16.0),
                          child: Container(
                            width: double.infinity,
                            height: double.infinity,
                            decoration: BoxDecoration(
                              color: FlutterFlowTheme.of(context).secondaryBackground,
                              border: Border.all(
                                color: FlutterFlowTheme.of(context).alternate,
                                width: 1.0,
                              ),
                              borderRadius: BorderRadius.circular(12.0),
                              boxShadow: [
                                BoxShadow(
                                  color: FlutterFlowTheme.of(context).alternate.withOpacity(0.3),
                                  offset: const Offset(0, 1),
                                  blurRadius: 3,
                                ),
                              ],
                            ),
                            child: Column(
                              children: [
                                // Search Bar
                                Row(
                                  children: [
                                    Expanded(
                                      child: Container(
                                        margin: const EdgeInsetsDirectional.fromSTEB(16.0, 12.0, 10.0, 0.0),
                                        height: 45.0,
                                        decoration: const BoxDecoration(),
                                        child: TextFormField(
                                          controller: _model.textController,
                                          onChanged: (_) => EasyDebounce.debounce(
                                            '_model.textController',
                                            const Duration(milliseconds: 2000),
                                            () => setState(() {}),
                                          ),
                                          decoration: InputDecoration(
                                            isDense: false,
                                            filled: true,
                                            fillColor: Colors.white,
                                            prefixIcon: Icon(
                                              Icons.search_rounded,
                                              color: FlutterFlowTheme.of(context).secondaryText,
                                              size: 20.0,
                                            ),
                                            suffixIcon: _model.textController!.text.isNotEmpty
                                                ? IconButton(
                                                    icon: Icon(
                                                      Icons.clear,
                                                      color: FlutterFlowTheme.of(context).secondaryText,
                                                      size: 20.0,
                                                    ),
                                                    onPressed: () {
                                                      _model.textController?.clear();
                                                      setState(() {});
                                                    },
                                                  )
                                                : null,
                                            labelStyle: FlutterFlowTheme.of(context).bodyMedium.override(
                                                  fontFamily: 'Jost',
                                                  color: FlutterFlowTheme.of(context).blackColor,
                                                  fontSize: 16.0,
                                                  letterSpacing: 0.0,
                                                  useGoogleFonts: false,
                                                ),
                                            hintText: 'Search by name, email...',
                                            hintStyle: FlutterFlowTheme.of(context).labelMedium.override(
                                                  fontFamily: 'Open Sans',
                                                  color: FlutterFlowTheme.of(context).secondaryText,
                                                  fontSize: 14.0,
                                                  letterSpacing: 0.0,
                                                ),
                                            contentPadding:
                                                const EdgeInsetsDirectional.fromSTEB(16.0, 0.0, 16.0, 0.0),
                                            enabledBorder: OutlineInputBorder(
                                              borderSide: BorderSide(
                                                color: FlutterFlowTheme.of(context).alternate,
                                                width: 1.0,
                                              ),
                                              borderRadius: BorderRadius.circular(10.0),
                                            ),
                                            focusedBorder: OutlineInputBorder(
                                              borderSide: BorderSide(
                                                color: FlutterFlowTheme.of(context).primary,
                                                width: 1.0,
                                              ),
                                              borderRadius: BorderRadius.circular(10.0),
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                    currentUserDocument?.role == Role.superAdmin
                                        ? Container(
                                            width: 250,
                                            height: 45,
                                            margin: const EdgeInsetsDirectional.fromSTEB(0, 12.0, 16.0, 0.0),
                                            decoration: BoxDecoration(
                                              color: FlutterFlowTheme.of(context).secondaryBackground,
                                              borderRadius: BorderRadius.circular(10),
                                              border: Border.all(
                                                color: FlutterFlowTheme.of(context).alternate,
                                              ),
                                            ),
                                            child: StreamBuilder<List<SitesRecord>>(
                                              stream: querySitesRecord(
                                                queryBuilder: (sitesRecord) =>
                                                    sitesRecord.orderBy('siteLocation'),
                                              ),
                                              builder: (context, snapshot) {
                                                if (!snapshot.hasData) {
                                                  return Center(
                                                    child: SizedBox(
                                                      width: 20,
                                                      height: 20,
                                                      child: CircularProgressIndicator(
                                                        valueColor: AlwaysStoppedAnimation<Color>(
                                                          FlutterFlowTheme.of(context).primary,
                                                        ),
                                                      ),
                                                    ),
                                                  );
                                                }

                                                final sites = snapshot.data!;
                                                final siteOptions = [
                                                  'All Sites',
                                                  ...sites.map((site) => site.siteName ?? 'Unknown')
                                                ];

                                                // Check if selected site is valid without using setState
                                                final String currentValue =
                                                    siteOptions.contains(_model.selectedSite)
                                                        ? _model.selectedSite!
                                                        : 'All Sites';

                                                return DropdownButtonHideUnderline(
                                                  child: ButtonTheme(
                                                    alignedDropdown: true,
                                                    child: DropdownButton<String>(
                                                      value: currentValue,
                                                      items: siteOptions
                                                          .map((String value) => DropdownMenuItem<String>(
                                                                value: value,
                                                                child: Text(
                                                                  value,
                                                                  style:
                                                                      FlutterFlowTheme.of(context).bodyMedium,
                                                                ),
                                                              ))
                                                          .toList(),
                                                      onChanged: (String? newValue) {
                                                        if (newValue != null) {
                                                          setState(() {
                                                            _model.selectedSite = newValue;
                                                          });
                                                        }
                                                      },
                                                      icon: Icon(
                                                        Icons.keyboard_arrow_down_rounded,
                                                        color: FlutterFlowTheme.of(context).secondaryText,
                                                        size: 24,
                                                      ),
                                                      isExpanded: true,
                                                      dropdownColor:
                                                          FlutterFlowTheme.of(context).primaryBackground,
                                                      borderRadius: BorderRadius.circular(8),
                                                      padding: const EdgeInsetsDirectional.fromSTEB(
                                                          8.0, 0.0, 0, 0.0),
                                                    ),
                                                  ),
                                                );
                                              },
                                            ),
                                          )
                                        : const SizedBox.shrink(),
                                    const SizedBox(
                                      width: 8,
                                    )
                                  ],
                                ),
                                const SizedBox(height: 24.0),
                                // Table Header
                                Container(
                                  margin: const EdgeInsetsDirectional.fromSTEB(16.0, 0.0, 16.0, 0.0),
                                  decoration: BoxDecoration(
                                    color: FlutterFlowTheme.of(context).primaryBackground,
                                    borderRadius: const BorderRadius.only(
                                      topLeft: Radius.circular(8.0),
                                      topRight: Radius.circular(8.0),
                                    ),
                                  ),
                                  child: Padding(
                                    padding: const EdgeInsetsDirectional.fromSTEB(16.0, 12.0, 16.0, 12.0),
                                    child: Row(
                                      mainAxisSize: MainAxisSize.max,
                                      children: [
                                        Expanded(
                                          flex: 2,
                                          child: Text(
                                            'Customer Name',
                                            style: FlutterFlowTheme.of(context).labelSmall.override(
                                                  fontFamily: 'Open Sans',
                                                  letterSpacing: 0.0,
                                                  fontWeight: FontWeight.w600,
                                                ),
                                          ),
                                        ),
                                        Expanded(
                                          flex: 2,
                                          child: Text(
                                            'Contact Info',
                                            style: FlutterFlowTheme.of(context).labelSmall.override(
                                                  fontFamily: 'Open Sans',
                                                  letterSpacing: 0.0,
                                                  fontWeight: FontWeight.w600,
                                                ),
                                          ),
                                        ),
                                        Expanded(
                                          flex: 1,
                                          child: Text(
                                            'Bookings',
                                            style: FlutterFlowTheme.of(context).labelSmall.override(
                                                  fontFamily: 'Open Sans',
                                                  letterSpacing: 0.0,
                                                  fontWeight: FontWeight.w600,
                                                ),
                                          ),
                                        ),
                                        const SizedBox(
                                          width: 20,
                                        ),
                                        Expanded(
                                          flex: 1,
                                          child: Text(
                                            'Last Booking',
                                            style: FlutterFlowTheme.of(context).labelSmall.override(
                                                  fontFamily: 'Open Sans',
                                                  letterSpacing: 0.0,
                                                  fontWeight: FontWeight.w600,
                                                ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                                // Customer List - Expanded to fill remaining height
                                Expanded(
                                  child: Padding(
                                    padding: const EdgeInsetsDirectional.fromSTEB(16.0, 0.0, 16.0, 0.0),
                                    child: StreamBuilder<List<DocumentSnapshot>>(
                                      stream: (() {
                                        final customersQuery = FirebaseFirestore.instance
                                            .collection('site_customer')
                                            .orderBy('createdAt', descending: true);

                                        if (currentUserDocument?.role == Role.admin) {
                                          // For admin, filter by their sites
                                          return FirebaseFirestore.instance
                                              .collection('sites')
                                              .where('siteAdminId', isEqualTo: currentUserDocument?.uid)
                                              .get()
                                              .asStream()
                                              .asyncExpand((sites) {
                                            final siteIds = sites.docs.map((site) => site.id).toList();
                                            if (siteIds.isEmpty) {
                                              return Stream.value(<DocumentSnapshot>[]);
                                            }
                                            return customersQuery
                                                .where('siteId', whereIn: siteIds)
                                                .snapshots()
                                                .map((snapshot) => snapshot.docs);
                                          });
                                        } else {
                                          // For super admin, fetch all customers
                                          return customersQuery.snapshots().map((snapshot) => snapshot.docs);
                                        }
                                      }()),
                                      builder: (context, snapshot) {
                                        if (!snapshot.hasData) {
                                          return SizedBox(
                                            width: double.infinity,
                                            height: MediaQuery.of(context).size.height * 0.6,
                                            child: Center(
                                                child: SizedBox(
                                              width: 40.0,
                                              height: 40.0,
                                              child: CircularProgressIndicator(
                                                valueColor: AlwaysStoppedAnimation<Color>(
                                                  FlutterFlowTheme.of(context).primary,
                                                ),
                                              ),
                                            )),
                                          );
                                        }

                                        final customerDocs = snapshot.data!;

                                        if (customerDocs.isEmpty) {
                                          return SizedBox(
                                            width: double.infinity,
                                            height: MediaQuery.of(context).size.height * 0.6,
                                            child: const Center(
                                              child: EmptyListComponentWidget(
                                                text: 'No Customers',
                                              ),
                                            ),
                                          );
                                        }

                                        return FutureBuilder<List<Map<String, dynamic>>>(
                                          future: Future.wait(
                                            customerDocs.map((doc) async {
                                              final customerData = doc.data() as Map<String, dynamic>;
                                              // Fetch user details
                                              final userDoc = await FirebaseFirestore.instance
                                                  .collection('users')
                                                  .doc(customerData['userId'] as String)
                                                  .get();
                                              final userData = userDoc.data() ?? {};

                                              // Fetch site details
                                              final siteDoc = await FirebaseFirestore.instance
                                                  .collection('sites')
                                                  .doc(customerData['siteId'] as String)
                                                  .get();
                                              final siteData = siteDoc.data() ?? {};

                                              return {
                                                ...customerData,
                                                'userDetails': userData,
                                                'siteDetails': siteData,
                                              };
                                            }),
                                          ),
                                          builder: (context, customerSnapshot) {
                                            if (!customerSnapshot.hasData) {
                                              return SizedBox(
                                                width: double.infinity,
                                                height: MediaQuery.of(context).size.height * 0.6,
                                                child: Center(
                                                    child: SizedBox(
                                                  width: 40.0,
                                                  height: 40.0,
                                                  child: CircularProgressIndicator(
                                                    valueColor: AlwaysStoppedAnimation<Color>(
                                                      FlutterFlowTheme.of(context).primary,
                                                    ),
                                                  ),
                                                )),
                                              );
                                            }

                                            final customers = customerSnapshot.data!;

                                            // Filter customers based on search text and selected site
                                            List<Map<String, dynamic>> filteredCustomers = [...customers];

                                            // Filter by search text if provided
                                            if (_model.textController!.text.isNotEmpty) {
                                              final searchText = _model.textController!.text.toLowerCase();
                                              filteredCustomers.removeWhere((customer) {
                                                final userData =
                                                    customer['userDetails'] as Map<String, dynamic>;
                                                final siteData =
                                                    customer['siteDetails'] as Map<String, dynamic>;
                                                return !userData['display_name']
                                                        .toString()
                                                        .toLowerCase()
                                                        .contains(searchText) &&
                                                    !userData['email']
                                                        .toString()
                                                        .toLowerCase()
                                                        .contains(searchText) &&
                                                    !siteData['siteName']
                                                        .toString()
                                                        .toLowerCase()
                                                        .contains(searchText);
                                              });
                                            }

                                            // Filter by selected site if not "All Sites"
                                            if (_model.selectedSite != null &&
                                                _model.selectedSite != 'All Sites') {
                                              filteredCustomers.removeWhere((customer) {
                                                final siteData =
                                                    customer['siteDetails'] as Map<String, dynamic>;
                                                final siteName = siteData['siteName'] as String?;
                                                return siteName != _model.selectedSite;
                                              });
                                            }

                                            return filteredCustomers.isEmpty
                                                ? SizedBox(
                                                    width: double.infinity,
                                                    height: MediaQuery.of(context).size.height * 0.6,
                                                    child: const Center(
                                                      child: EmptyListComponentWidget(
                                                        text: 'No Customers',
                                                      ),
                                                    ),
                                                  )
                                                : ListView.separated(
                                                    padding: EdgeInsets.zero,
                                                    shrinkWrap: true,
                                                    physics: const NeverScrollableScrollPhysics(),
                                                    itemCount: filteredCustomers.length,
                                                    separatorBuilder: (context, index) =>
                                                        const SizedBox(height: 1.0),
                                                    itemBuilder: (context, index) {
                                                      final customer = filteredCustomers[index];
                                                      final userData =
                                                          customer['userDetails'] as Map<String, dynamic>;
                                                      final siteData =
                                                          customer['siteDetails'] as Map<String, dynamic>;

                                                      return Container(
                                                        decoration: BoxDecoration(
                                                          color: FlutterFlowTheme.of(context)
                                                              .secondaryBackground,
                                                          border: Border(
                                                            bottom: BorderSide(
                                                              color: FlutterFlowTheme.of(context).alternate,
                                                              width: 1.0,
                                                            ),
                                                          ),
                                                        ),
                                                        child: Padding(
                                                          padding: const EdgeInsetsDirectional.fromSTEB(
                                                              16.0, 12.0, 16.0, 12.0),
                                                          child: Row(
                                                            mainAxisSize: MainAxisSize.max,
                                                            children: [
                                                              // Customer Name Column
                                                              Expanded(
                                                                flex: 2,
                                                                child: Column(
                                                                  crossAxisAlignment:
                                                                      CrossAxisAlignment.start,
                                                                  children: [
                                                                    Text(
                                                                      userData['display_name'] ?? 'N/A',
                                                                      style: FlutterFlowTheme.of(context)
                                                                          .titleMedium
                                                                          .override(
                                                                            fontFamily: 'Open Sans',
                                                                            fontWeight: FontWeight.w600,
                                                                          ),
                                                                    ),
                                                                    if (currentUserDocument?.role !=
                                                                        Role.admin)
                                                                      Text(
                                                                        siteData['siteName'] ?? 'N/A',
                                                                        style: FlutterFlowTheme.of(context)
                                                                            .bodyMedium
                                                                            .override(
                                                                              fontFamily: 'Open Sans',
                                                                              color:
                                                                                  FlutterFlowTheme.of(context)
                                                                                      .secondaryText,
                                                                              fontSize: 13.0,
                                                                            ),
                                                                      ),
                                                                  ],
                                                                ),
                                                              ),
                                                              // Contact Info Column
                                                              Expanded(
                                                                flex: 2,
                                                                child: Column(
                                                                  crossAxisAlignment:
                                                                      CrossAxisAlignment.start,
                                                                  children: [
                                                                    Text(
                                                                      userData['phone_number'] ?? 'N/A',
                                                                      style: FlutterFlowTheme.of(context)
                                                                          .bodyMedium,
                                                                    ),
                                                                    Text(
                                                                      userData['email'] ?? 'N/A',
                                                                      style: FlutterFlowTheme.of(context)
                                                                          .bodySmall,
                                                                    ),
                                                                  ],
                                                                ),
                                                              ),
                                                              // Bookings Count Column
                                                              Expanded(
                                                                flex: 1,
                                                                child: Text(
                                                                  '${customer['totalBookings'] ?? 0}',
                                                                  style:
                                                                      FlutterFlowTheme.of(context).bodyMedium,
                                                                ),
                                                              ),
                                                              // Last Booking Column
                                                              Expanded(
                                                                flex: 1,
                                                                child: customer['lastBookingDate'] != null
                                                                    ? Column(
                                                                        crossAxisAlignment:
                                                                            CrossAxisAlignment.start,
                                                                        children: [
                                                                          Text(
                                                                            dateTimeFormat(
                                                                              'MMM d, y',
                                                                              (customer['lastBookingDate']
                                                                                      as Timestamp)
                                                                                  .toDate(),
                                                                            ),
                                                                            style:
                                                                                FlutterFlowTheme.of(context)
                                                                                    .bodyMedium,
                                                                          ),
                                                                          Text(
                                                                            dateTimeFormat(
                                                                              'h:mm a',
                                                                              (customer['lastBookingDate']
                                                                                      as Timestamp)
                                                                                  .toDate(),
                                                                            ),
                                                                            style:
                                                                                FlutterFlowTheme.of(context)
                                                                                    .bodySmall,
                                                                          ),
                                                                        ],
                                                                      )
                                                                    : Text(
                                                                        'N/A',
                                                                        style: FlutterFlowTheme.of(context)
                                                                            .bodyMedium,
                                                                      ),
                                                              ),
                                                            ],
                                                          ),
                                                        ),
                                                      );
                                                    },
                                                  );
                                          },
                                        );
                                      },
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
