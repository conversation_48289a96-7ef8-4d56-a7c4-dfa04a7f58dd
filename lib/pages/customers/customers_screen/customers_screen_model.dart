import 'package:flutter/material.dart';
import 'package:unimetals_admin/flutter_flow/flutter_flow_model.dart';
import 'package:unimetals_admin/pages/customers/customers_screen/customers_screen_widget.dart';

class CustomersScreenModel extends FlutterFlowModel<CustomersScreenWidget> {
  ///  State fields for stateful widgets in this page.
  final unfocusNode = FocusNode();
  // State field(s) for TextField widget.
  FocusNode? textFieldFocusNode;
  TextEditingController? textController;
  String? Function(BuildContext, String?)? textControllerValidator;
  String? selectedFilter = 'All';
  String? selectedSite = 'All Sites';
  
  /// Initialization and disposal methods.
  void initState(BuildContext context) {}

  void dispose() {
    unfocusNode.dispose();
    textFieldFocusNode?.dispose();
    textController?.dispose();
  }
}

CustomersScreenModel createModel(BuildContext context, Function() param1) => CustomersScreenModel();
