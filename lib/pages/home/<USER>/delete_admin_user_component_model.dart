import '/backend/backend.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/flutter_flow/flutter_flow_widgets.dart';
import 'dart:ui';
import '/actions/actions.dart' as action_blocks;
import '/custom_code/actions/index.dart' as actions;
import 'delete_admin_user_component_widget.dart'
    show DeleteAdminUserComponentWidget;
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';

class DeleteAdminUserComponentModel
    extends FlutterFlowModel<DeleteAdminUserComponentWidget> {
  ///  State fields for stateful widgets in this component.

  // Stores action output result for [Custom Action - deleteAdminUser] action in Button widget.
  String? deleteAdminUserResponse;

  @override
  void initState(BuildContext context) {}

  @override
  void dispose() {}
}
