import 'package:easy_debounce/easy_debounce.dart';
import 'package:flutter/material.dart';
import 'package:text_search/text_search.dart';
import 'package:unimetals_admin/backend/schema/enums/menu_items.dart';
import 'package:unimetals_admin/pages/components/side_menu/side_menu_widget.dart';

import '/backend/backend.dart';
import '/backend/schema/enums/enums.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/flutter_flow/flutter_flow_widgets.dart';
import '/pages/components/empty_list_component/empty_list_component_widget.dart';
import '/pages/home/<USER>/add_new_admin_dialog_widget.dart';
import '/pages/home/<USER>/delete_admin_user_component_widget.dart';
import 'home_screen_model.dart';

export 'home_screen_model.dart';

class HomeScreenWidget extends StatefulWidget {
  const HomeScreenWidget({super.key});

  static String routeName = 'home_screen';
  static String routePath = '/homeScreen';

  @override
  State<HomeScreenWidget> createState() => _HomeScreenWidgetState();
}

class _HomeScreenWidgetState extends State<HomeScreenWidget> {
  late HomeScreenModel _model;

  final scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => HomeScreenModel());

    _model.textController ??= TextEditingController();
    _model.textFieldFocusNode ??= FocusNode();

    WidgetsBinding.instance.addPostFrameCallback((_) => safeSetState(() {}));
  }

  @override
  void dispose() {
    _model.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).unfocus();
        FocusManager.instance.primaryFocus?.unfocus();
      },
      child: Scaffold(
        key: scaffoldKey,
        backgroundColor: FlutterFlowTheme.of(context).primaryBackground,
        body: SafeArea(
          top: true,
          child: Row(
            mainAxisSize: MainAxisSize.max,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SideMenuWidget(selectedItem: MenuItem.adminUsers),
              Expanded(
                child: Align(
                  alignment: const AlignmentDirectional(0.0, -1.0),
                  child: Container(
                    width: double.infinity,
                    constraints: const BoxConstraints(
                      maxWidth: 1370.0,
                    ),
                    decoration: const BoxDecoration(),
                    child: Column(
                      mainAxisSize: MainAxisSize.max,
                      children: [
                        Padding(
                          padding: const EdgeInsetsDirectional.fromSTEB(16.0, 26.0, 16.0, 15.0),
                          child: Row(
                            mainAxisSize: MainAxisSize.max,
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Expanded(
                                child: Column(
                                  mainAxisSize: MainAxisSize.max,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Row(
                                      mainAxisSize: MainAxisSize.max,
                                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                      children: [
                                        Text(
                                          'Admin Users Management',
                                          style: FlutterFlowTheme.of(context).bodyMedium.override(
                                                fontFamily: 'Open Sans',
                                                color: FlutterFlowTheme.of(context).blackColor,
                                                fontSize: 26.0,
                                                letterSpacing: 0.0,
                                                fontWeight: FontWeight.w600,
                                              ),
                                        ),
                                        Builder(
                                          builder: (context) => FFButtonWidget(
                                            onPressed: () async {
                                              await showDialog(
                                                context: context,
                                                builder: (dialogContext) {
                                                  return Dialog(
                                                    elevation: 0,
                                                    insetPadding: EdgeInsets.zero,
                                                    backgroundColor: Colors.transparent,
                                                    alignment: const AlignmentDirectional(0.0, 0.0)
                                                        .resolve(Directionality.of(context)),
                                                    child: GestureDetector(
                                                      onTap: () {
                                                        FocusScope.of(dialogContext).unfocus();
                                                        FocusManager.instance.primaryFocus?.unfocus();
                                                      },
                                                      child: const AddNewAdminDialogWidget(),
                                                    ),
                                                  );
                                                },
                                              );
                                            },
                                            text: 'Add New Admin',
                                            options: FFButtonOptions(
                                              height: 40.0,
                                              padding:
                                                  const EdgeInsetsDirectional.fromSTEB(16.0, 0.0, 16.0, 0.0),
                                              iconPadding:
                                                  const EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 0.0, 0.0),
                                              color: FlutterFlowTheme.of(context).secondary,
                                              textStyle: FlutterFlowTheme.of(context).titleSmall.override(
                                                    fontFamily: 'Open Sans',
                                                    color: Colors.white,
                                                    letterSpacing: 0.0,
                                                    fontWeight: FontWeight.bold,
                                                  ),
                                              elevation: 0.0,
                                              borderRadius: BorderRadius.circular(10.0),
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                        Expanded(
                          child: Padding(
                            padding: const EdgeInsetsDirectional.fromSTEB(16.0, 16.0, 15.0, 0.0),
                            child: Container(
                              width: double.infinity,
                              constraints: const BoxConstraints(
                                maxWidth: 1370.0,
                              ),
                              decoration: BoxDecoration(
                                color: FlutterFlowTheme.of(context).secondaryBackground,
                                boxShadow: const [
                                  BoxShadow(
                                    blurRadius: 3.0,
                                    color: Color(0x33000000),
                                    offset: Offset(
                                      0.0,
                                      1.0,
                                    ),
                                  )
                                ],
                                borderRadius: BorderRadius.circular(8.0),
                                border: Border.all(
                                  color: FlutterFlowTheme.of(context).alternate,
                                  width: 1.0,
                                ),
                              ),
                              child: Padding(
                                padding: const EdgeInsets.all(16.0),
                                child: Column(
                                  mainAxisSize: MainAxisSize.max,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Row(
                                      mainAxisSize: MainAxisSize.max,
                                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                      children: [
                                        Expanded(
                                          child: Column(
                                            mainAxisSize: MainAxisSize.max,
                                            crossAxisAlignment: CrossAxisAlignment.start,
                                            children: [
                                              Padding(
                                                padding:
                                                    const EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 12.0, 0.0),
                                                child: Text(
                                                  'Admin Users',
                                                  style: FlutterFlowTheme.of(context).headlineMedium.override(
                                                        fontFamily: 'Open Sans',
                                                        fontSize: 22.0,
                                                        letterSpacing: 0.0,
                                                      ),
                                                ),
                                              ),
                                              Padding(
                                                padding:
                                                    const EdgeInsetsDirectional.fromSTEB(0.0, 4.0, 12.0, 0.0),
                                                child: Text(
                                                  'Here is the list of all admin users',
                                                  style: FlutterFlowTheme.of(context).labelMedium.override(
                                                        fontFamily: 'Jost',
                                                        letterSpacing: 0.0,
                                                        useGoogleFonts: false,
                                                      ),
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                        Container(
                                          height: 45.0,
                                          decoration: const BoxDecoration(),
                                          child: SizedBox(
                                            width: 250.0,
                                            child: TextFormField(
                                              controller: _model.textController,
                                              focusNode: _model.textFieldFocusNode,
                                              onChanged: (_) => EasyDebounce.debounce(
                                                '_model.textController',
                                                const Duration(milliseconds: 2000),
                                                () async {
                                                  await queryUsersRecordOnce()
                                                      .then(
                                                        (records) => _model.simpleSearchResults = TextSearch(
                                                          records
                                                              .map(
                                                                (record) => TextSearchItem.fromTerms(
                                                                    record, [record.email]),
                                                              )
                                                              .toList(),
                                                        )
                                                            .search(_model.textController.text)
                                                            .map((r) => r.object)
                                                            .toList(),
                                                      )
                                                      .onError((_, __) => _model.simpleSearchResults = [])
                                                      .whenComplete(() => safeSetState(() {}));
                                                },
                                              ),
                                              autofocus: false,
                                              obscureText: false,
                                              decoration: InputDecoration(
                                                isDense: false,
                                                labelStyle: FlutterFlowTheme.of(context).bodyMedium.override(
                                                      fontFamily: 'Jost',
                                                      color: FlutterFlowTheme.of(context).blackColor,
                                                      fontSize: 16.0,
                                                      letterSpacing: 0.0,
                                                      useGoogleFonts: false,
                                                    ),
                                                alignLabelWithHint: false,
                                                hintText: 'Search by email',
                                                hintStyle: FlutterFlowTheme.of(context).labelMedium.override(
                                                      fontFamily: 'Open Sans',
                                                      color: FlutterFlowTheme.of(context).secondaryText,
                                                      fontSize: 12.0,
                                                      letterSpacing: 0.0,
                                                    ),
                                                enabledBorder: OutlineInputBorder(
                                                  borderSide: const BorderSide(
                                                    color: Color(0x00000000),
                                                    width: 1.0,
                                                  ),
                                                  borderRadius: BorderRadius.circular(10.0),
                                                ),
                                                focusedBorder: OutlineInputBorder(
                                                  borderSide: const BorderSide(
                                                    color: Color(0x00000000),
                                                    width: 1.0,
                                                  ),
                                                  borderRadius: BorderRadius.circular(10.0),
                                                ),
                                                errorBorder: OutlineInputBorder(
                                                  borderSide: BorderSide(
                                                    color: FlutterFlowTheme.of(context).error,
                                                    width: 1.0,
                                                  ),
                                                  borderRadius: BorderRadius.circular(10.0),
                                                ),
                                                focusedErrorBorder: OutlineInputBorder(
                                                  borderSide: BorderSide(
                                                    color: FlutterFlowTheme.of(context).error,
                                                    width: 1.0,
                                                  ),
                                                  borderRadius: BorderRadius.circular(10.0),
                                                ),
                                                filled: true,
                                                fillColor: FlutterFlowTheme.of(context).primaryBackground,
                                                contentPadding: const EdgeInsetsDirectional.fromSTEB(
                                                    15.0, 20.0, 15.0, 30.0),
                                                suffixIcon: Icon(
                                                  Icons.search_rounded,
                                                  color: FlutterFlowTheme.of(context).secondaryText,
                                                ),
                                              ),
                                              style: FlutterFlowTheme.of(context).bodyMedium.override(
                                                    fontFamily: 'Jost',
                                                    color: FlutterFlowTheme.of(context).blackColor,
                                                    fontSize: 14.0,
                                                    letterSpacing: 0.0,
                                                    useGoogleFonts: false,
                                                  ),
                                              cursorColor: FlutterFlowTheme.of(context).primaryText,
                                              validator: _model.textControllerValidator.asValidator(context),
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                    Padding(
                                      padding: const EdgeInsetsDirectional.fromSTEB(0.0, 16.0, 0.0, 0.0),
                                      child: Container(
                                        width: double.infinity,
                                        height: 40.0,
                                        decoration: BoxDecoration(
                                          color: FlutterFlowTheme.of(context).primaryBackground,
                                          borderRadius: const BorderRadius.only(
                                            bottomLeft: Radius.circular(0.0),
                                            bottomRight: Radius.circular(0.0),
                                            topLeft: Radius.circular(8.0),
                                            topRight: Radius.circular(8.0),
                                          ),
                                        ),
                                        child: Padding(
                                          padding: const EdgeInsetsDirectional.fromSTEB(16.0, 0.0, 0.0, 0.0),
                                          child: Row(
                                            mainAxisSize: MainAxisSize.max,
                                            children: [
                                              Expanded(
                                                flex: 2,
                                                child: Text(
                                                  'Id#',
                                                  style: FlutterFlowTheme.of(context).labelSmall.override(
                                                        fontFamily: 'Open Sans',
                                                        letterSpacing: 0.0,
                                                        fontWeight: FontWeight.w600,
                                                      ),
                                                ),
                                              ),
                                              Expanded(
                                                flex: 2,
                                                child: Text(
                                                  'Email',
                                                  style: FlutterFlowTheme.of(context).labelSmall.override(
                                                        fontFamily: 'Open Sans',
                                                        letterSpacing: 0.0,
                                                        fontWeight: FontWeight.w600,
                                                      ),
                                                ),
                                              ),
                                              Expanded(
                                                flex: 2,
                                                child: Text(
                                                  'Location',
                                                  style: FlutterFlowTheme.of(context).labelSmall.override(
                                                        fontFamily: 'Open Sans',
                                                        letterSpacing: 0.0,
                                                        fontWeight: FontWeight.w600,
                                                      ),
                                                ),
                                              ),
                                              Expanded(
                                                flex: 1,
                                                child: Text(
                                                  'Actions',
                                                  textAlign: TextAlign.center,
                                                  style: FlutterFlowTheme.of(context).labelSmall.override(
                                                        fontFamily: 'Open Sans',
                                                        letterSpacing: 0.0,
                                                        fontWeight: FontWeight.w600,
                                                      ),
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ),
                                    ),
                                    Expanded(
                                      child: Builder(
                                        builder: (context) {
                                          if (_model.textController.text == '') {
                                            return StreamBuilder<List<UsersRecord>>(
                                              stream: queryUsersRecord(
                                                queryBuilder: (usersRecord) => usersRecord
                                                    .where(
                                                      'role',
                                                      isEqualTo: Role.admin.serialize(),
                                                    )
                                                    .orderBy('created_time', descending: true),
                                              ),
                                              builder: (context, snapshot) {
                                                // Customize what your widget looks like when it's loading.
                                                if (!snapshot.hasData) {
                                                  return Center(
                                                    child: SizedBox(
                                                      width: 40.0,
                                                      height: 40.0,
                                                      child: CircularProgressIndicator(
                                                        valueColor: AlwaysStoppedAnimation<Color>(
                                                          FlutterFlowTheme.of(context).primary,
                                                        ),
                                                      ),
                                                    ),
                                                  );
                                                }
                                                List<UsersRecord> listViewUsersRecordList = snapshot.data!;
                                                if (listViewUsersRecordList.isEmpty) {
                                                  return const EmptyListComponentWidget(
                                                    text: 'No Admin Users',
                                                  );
                                                }

                                                return ListView.builder(
                                                  padding: EdgeInsets.zero,
                                                  primary: false,
                                                  shrinkWrap: true,
                                                  scrollDirection: Axis.vertical,
                                                  itemCount: listViewUsersRecordList.length,
                                                  itemBuilder: (context, listViewIndex) {
                                                    final listViewUsersRecord =
                                                        listViewUsersRecordList[listViewIndex];
                                                    return Padding(
                                                      padding: const EdgeInsetsDirectional.fromSTEB(
                                                          0.0, 0.0, 0.0, 1.0),
                                                      child: StreamBuilder<List<SitesRecord>>(
                                                        stream: querySitesRecord(
                                                          queryBuilder: (sitesRecord) => sitesRecord.where(
                                                            'siteAdminId',
                                                            isEqualTo: listViewUsersRecord.uid,
                                                          ),
                                                          singleRecord: true,
                                                        ),
                                                        builder: (context, snapshot) {
                                                          // Customize what your widget looks like when it's loading.
                                                          if (!snapshot.hasData) {
                                                            return Center(
                                                              child: Padding(
                                                                padding: const EdgeInsetsDirectional.fromSTEB(
                                                                    0.0, 46.0, 0.0, 0.0),
                                                                child: LinearProgressIndicator(
                                                                  color: FlutterFlowTheme.of(context)
                                                                      .secondaryBackground,
                                                                ),
                                                              ),
                                                            );
                                                          }
                                                          List<SitesRecord> containerSitesRecordList =
                                                              snapshot.data!;
                                                          final containerSitesRecord =
                                                              containerSitesRecordList.isNotEmpty
                                                                  ? containerSitesRecordList.first
                                                                  : null;

                                                          return Container(
                                                            width: 100.0,
                                                            decoration: BoxDecoration(
                                                              color: FlutterFlowTheme.of(context)
                                                                  .secondaryBackground,
                                                              boxShadow: [
                                                                BoxShadow(
                                                                  blurRadius: 0.0,
                                                                  color: FlutterFlowTheme.of(context)
                                                                      .primaryBackground,
                                                                  offset: const Offset(
                                                                    0.0,
                                                                    1.0,
                                                                  ),
                                                                )
                                                              ],
                                                            ),
                                                            child: Padding(
                                                              padding: const EdgeInsetsDirectional.fromSTEB(
                                                                  16.0, 8.0, 16.0, 8.0),
                                                              child: Row(
                                                                mainAxisSize: MainAxisSize.max,
                                                                children: [
                                                                  Expanded(
                                                                    flex: 2,
                                                                    child: Text(
                                                                      listViewUsersRecord.uid,
                                                                      style: FlutterFlowTheme.of(context)
                                                                          .bodyMedium
                                                                          .override(
                                                                            fontFamily: 'Nunito',
                                                                            letterSpacing: 0.0,
                                                                          ),
                                                                    ),
                                                                  ),
                                                                  Expanded(
                                                                    flex: 2,
                                                                    child: Row(
                                                                      mainAxisSize: MainAxisSize.max,
                                                                      children: [
                                                                        Expanded(
                                                                          child: Text(
                                                                            listViewUsersRecord.email,
                                                                            style:
                                                                                FlutterFlowTheme.of(context)
                                                                                    .titleLarge
                                                                                    .override(
                                                                                      fontFamily: 'Open Sans',
                                                                                      fontSize: 14.0,
                                                                                      letterSpacing: 0.0,
                                                                                    ),
                                                                          ),
                                                                        ),
                                                                      ],
                                                                    ),
                                                                  ),
                                                                  const SizedBox(width: 10),
                                                                  Expanded(
                                                                    flex: 2,
                                                                    child: Text(
                                                                      valueOrDefault<String>(
                                                                        containerSitesRecord?.siteLocation,
                                                                        'location',
                                                                      ),
                                                                      style: FlutterFlowTheme.of(context)
                                                                          .titleLarge
                                                                          .override(
                                                                            fontFamily: 'Open Sans',
                                                                            fontSize: 14.0,
                                                                            letterSpacing: 0.0,
                                                                          ),
                                                                    ),
                                                                  ),
                                                                  Expanded(
                                                                    flex: 1,
                                                                    child: Row(
                                                                      mainAxisSize: MainAxisSize.max,
                                                                      mainAxisAlignment:
                                                                          MainAxisAlignment.end,
                                                                      children: [
                                                                        Builder(
                                                                          builder: (context) =>
                                                                              FFButtonWidget(
                                                                            onPressed: () async {
                                                                              
                                                                              await showDialog(
                                                                                context: context,
                                                                                builder: (dialogContext) {
                                                                                  return Dialog(
                                                                                    elevation: 0,
                                                                                    insetPadding:
                                                                                        EdgeInsets.zero,
                                                                                    backgroundColor:
                                                                                        Colors.transparent,
                                                                                    alignment:
                                                                                        const AlignmentDirectional(
                                                                                                0.0, 0.0)
                                                                                            .resolve(
                                                                                                Directionality.of(
                                                                                                    context)),
                                                                                    child: GestureDetector(
                                                                                      onTap: () {
                                                                                        FocusScope.of(
                                                                                                dialogContext)
                                                                                            .unfocus();
                                                                                        FocusManager.instance
                                                                                            .primaryFocus
                                                                                            ?.unfocus();
                                                                                      },
                                                                                      child:
                                                                                          DeleteAdminUserComponentWidget(
                                                                                        adminUserDoc:
                                                                                            listViewUsersRecord,
                                                                                      ),
                                                                                    ),
                                                                                  );
                                                                                },
                                                                              );
                                                                            },
                                                                            text: 'Remove',
                                                                            options: FFButtonOptions(
                                                                              height: 35.0,
                                                                              padding:
                                                                                  const EdgeInsetsDirectional
                                                                                      .fromSTEB(
                                                                                      16.0, 0.0, 16.0, 0.0),
                                                                              iconPadding:
                                                                                  const EdgeInsetsDirectional
                                                                                      .fromSTEB(
                                                                                      0.0, 0.0, 0.0, 0.0),
                                                                              color:
                                                                                  FlutterFlowTheme.of(context)
                                                                                      .customRed,
                                                                              textStyle:
                                                                                  FlutterFlowTheme.of(context)
                                                                                      .titleSmall
                                                                                      .override(
                                                                                        fontFamily:
                                                                                            'Open Sans',
                                                                                        color: Colors.white,
                                                                                        fontSize: 14.0,
                                                                                        letterSpacing: 0.0,
                                                                                      ),
                                                                              elevation: 0.0,
                                                                              borderRadius:
                                                                                  BorderRadius.circular(8.0),
                                                                            ),
                                                                          ),
                                                                        ),
                                                                      ],
                                                                    ),
                                                                  ),
                                                                ],
                                                              ),
                                                            ),
                                                          );
                                                        },
                                                      ),
                                                    );
                                                  },
                                                );
                                              },
                                            );
                                          } else {
                                            return Builder(
                                              builder: (context) {
                                                final searchAdminUsers = _model.simpleSearchResults
                                                    .where((e) => e.role == Role.admin)
                                                    .toList();
                                                if (searchAdminUsers.isEmpty) {
                                                  return const EmptyListComponentWidget(
                                                    text: 'No Admin Users',
                                                  );
                                                }

                                                return ListView.builder(
                                                  padding: EdgeInsets.zero,
                                                  primary: false,
                                                  shrinkWrap: true,
                                                  scrollDirection: Axis.vertical,
                                                  itemCount: searchAdminUsers.length,
                                                  itemBuilder: (context, searchAdminUsersIndex) {
                                                    final searchAdminUsersItem =
                                                        searchAdminUsers[searchAdminUsersIndex];
                                                    return Padding(
                                                      padding: const EdgeInsetsDirectional.fromSTEB(
                                                          0.0, 0.0, 0.0, 1.0),
                                                      child: StreamBuilder<List<SitesRecord>>(
                                                        stream: querySitesRecord(
                                                          queryBuilder: (sitesRecord) => sitesRecord.where(
                                                            'siteAdminId',
                                                            isEqualTo: searchAdminUsersItem.uid,
                                                          ),
                                                          singleRecord: true,
                                                        ),
                                                        builder: (context, snapshot) {
                                                          // Customize what your widget looks like when it's loading.
                                                          if (!snapshot.hasData) {
                                                            return Center(
                                                              child: Padding(
                                                                padding: const EdgeInsetsDirectional.fromSTEB(
                                                                    0.0, 46.0, 0.0, 0.0),
                                                                child: LinearProgressIndicator(
                                                                  color: FlutterFlowTheme.of(context)
                                                                      .secondaryBackground,
                                                                ),
                                                              ),
                                                            );
                                                          }
                                                          List<SitesRecord> containerSitesRecordList =
                                                              snapshot.data!;
                                                          final containerSitesRecord =
                                                              containerSitesRecordList.isNotEmpty
                                                                  ? containerSitesRecordList.first
                                                                  : null;

                                                          return Container(
                                                            width: 100.0,
                                                            decoration: BoxDecoration(
                                                              color: FlutterFlowTheme.of(context)
                                                                  .secondaryBackground,
                                                              boxShadow: [
                                                                BoxShadow(
                                                                  blurRadius: 0.0,
                                                                  color: FlutterFlowTheme.of(context)
                                                                      .primaryBackground,
                                                                  offset: const Offset(
                                                                    0.0,
                                                                    1.0,
                                                                  ),
                                                                )
                                                              ],
                                                            ),
                                                            child: Padding(
                                                              padding: const EdgeInsetsDirectional.fromSTEB(
                                                                  16.0, 8.0, 16.0, 8.0),
                                                              child: Row(
                                                                mainAxisSize: MainAxisSize.max,
                                                                children: [
                                                                  Expanded(
                                                                    flex: 2,
                                                                    child: Text(
                                                                      searchAdminUsersItem.uid,
                                                                      style: FlutterFlowTheme.of(context)
                                                                          .bodyMedium
                                                                          .override(
                                                                            fontFamily: 'Nunito',
                                                                            letterSpacing: 0.0,
                                                                          ),
                                                                    ),
                                                                  ),
                                                                  Expanded(
                                                                    flex: 2,
                                                                    child: Row(
                                                                      mainAxisSize: MainAxisSize.max,
                                                                      children: [
                                                                        Expanded(
                                                                          child: Text(
                                                                            searchAdminUsersItem.email,
                                                                            style:
                                                                                FlutterFlowTheme.of(context)
                                                                                    .titleLarge
                                                                                    .override(
                                                                                      fontFamily: 'Open Sans',
                                                                                      fontSize: 14.0,
                                                                                      letterSpacing: 0.0,
                                                                                    ),
                                                                          ),
                                                                        ),
                                                                      ],
                                                                    ),
                                                                  ),
                                                                  Expanded(
                                                                    flex: 3,
                                                                    child: Text(
                                                                      valueOrDefault<String>(
                                                                        containerSitesRecord?.siteLocation,
                                                                        'location',
                                                                      ),
                                                                      maxLines: 1,
                                                                      style: FlutterFlowTheme.of(context)
                                                                          .titleLarge
                                                                          .override(
                                                                            fontFamily: 'Open Sans',
                                                                            fontSize: 14.0,
                                                                            letterSpacing: 0.0,
                                                                          ),
                                                                    ),
                                                                  ),
                                                                  Expanded(
                                                                    flex: 1,
                                                                    child: Row(
                                                                      mainAxisSize: MainAxisSize.max,
                                                                      mainAxisAlignment:
                                                                          MainAxisAlignment.end,
                                                                      children: [
                                                                        Builder(
                                                                          builder: (context) =>
                                                                              FFButtonWidget(
                                                                            onPressed: () async {
                                                                              await showDialog(
                                                                                context: context,
                                                                                builder: (dialogContext) {
                                                                                  return Dialog(
                                                                                    elevation: 0,
                                                                                    insetPadding:
                                                                                        EdgeInsets.zero,
                                                                                    backgroundColor:
                                                                                        Colors.transparent,
                                                                                    alignment:
                                                                                        const AlignmentDirectional(
                                                                                                0.0, 0.0)
                                                                                            .resolve(
                                                                                                Directionality.of(
                                                                                                    context)),
                                                                                    child: GestureDetector(
                                                                                      onTap: () {
                                                                                        FocusScope.of(
                                                                                                dialogContext)
                                                                                            .unfocus();
                                                                                        FocusManager.instance
                                                                                            .primaryFocus
                                                                                            ?.unfocus();
                                                                                      },
                                                                                      child:
                                                                                          DeleteAdminUserComponentWidget(
                                                                                        adminUserDoc:
                                                                                            searchAdminUsersItem,
                                                                                      ),
                                                                                    ),
                                                                                  );
                                                                                },
                                                                              );
                                                                            },
                                                                            text: 'Remove',
                                                                            options: FFButtonOptions(
                                                                              height: 35.0,
                                                                              padding:
                                                                                  const EdgeInsetsDirectional
                                                                                      .fromSTEB(
                                                                                      16.0, 0.0, 16.0, 0.0),
                                                                              iconPadding:
                                                                                  const EdgeInsetsDirectional
                                                                                      .fromSTEB(
                                                                                      0.0, 0.0, 0.0, 0.0),
                                                                              color:
                                                                                  FlutterFlowTheme.of(context)
                                                                                      .customRed,
                                                                              textStyle:
                                                                                  FlutterFlowTheme.of(context)
                                                                                      .titleSmall
                                                                                      .override(
                                                                                        fontFamily:
                                                                                            'Open Sans',
                                                                                        color: Colors.white,
                                                                                        fontSize: 14.0,
                                                                                        letterSpacing: 0.0,
                                                                                      ),
                                                                              elevation: 0.0,
                                                                              borderRadius:
                                                                                  BorderRadius.circular(8.0),
                                                                            ),
                                                                          ),
                                                                        ),
                                                                      ],
                                                                    ),
                                                                  ),
                                                                ],
                                                              ),
                                                            ),
                                                          );
                                                        },
                                                      ),
                                                    );
                                                  },
                                                );
                                              },
                                            );
                                          }
                                        },
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ),
                      ].addToEnd(const SizedBox(height: 24.0)),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
