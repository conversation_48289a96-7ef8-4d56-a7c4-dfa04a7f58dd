import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:unimetals_admin/flutter_flow/flutter_flow_util.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/auth/firebase_auth/auth_util.dart';
import '/backend/schema/enums/menu_items.dart';
import '/pages/components/user_profile_component/user_profile_component_widget.dart';

class SideMenuWidget extends StatelessWidget {
  final MenuItem selectedItem;

  const SideMenuWidget({
    super.key,
    required this.selectedItem,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 270.0,
      height: double.infinity,
      decoration: BoxDecoration(
        color: FlutterFlowTheme.of(context).primary,
        boxShadow: [
          BoxShadow(
            blurRadius: 0.0,
            color: FlutterFlowTheme.of(context).alternate,
            offset: const Offset(1.0, 0.0),
          )
        ],
        borderRadius: BorderRadius.circular(0.0),
      ),
      child: Padding(
        padding: const EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 0.0, 16.0),
        child: Column(
          mainAxisSize: MainAxisSize.max,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(context),
            AuthUserStreamWidget(
              builder: (context) {
                if (currentUserDocument?.role == null) {
                  return const SizedBox.shrink();
                }

                final menuItems = MenuItem.getMenuItemsForRole(currentUserDocument!.role!);
                return Column(
                  children: menuItems
                      .map(
                        (item) => _buildMenuItem(
                          context: context,
                          menuItem: item,
                          isSelected: selectedItem == item,
                        ),
                      )
                      .toList(),
                );
              },
            ),
            const Spacer(),
            _buildUserProfile(context),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Padding(
      padding: const EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 0.0, 24.0),
      child: Container(
        width: double.infinity,
        decoration: const BoxDecoration(),
        child: Column(
          mainAxisSize: MainAxisSize.max,
          children: [
            Padding(
              padding: const EdgeInsetsDirectional.fromSTEB(16.0, 24.0, 16.0, 0.0),
              child: Row(
                mainAxisSize: MainAxisSize.max,
                children: [
                  Container(
                    width: 40.0,
                    height: 40.0,
                    decoration: BoxDecoration(
                      color: FlutterFlowTheme.of(context).secondaryBackground,
                      borderRadius: BorderRadius.circular(10.0),
                      shape: BoxShape.rectangle,
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(5.0),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(0.0),
                        child: Image.asset(
                          'assets/images/Adobe_Express_-_file.png',
                          fit: BoxFit.contain,
                        ),
                      ),
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsetsDirectional.fromSTEB(12.0, 0.0, 0.0, 0.0),
                    child: Text(
                      'Unimetals',
                      style: FlutterFlowTheme.of(context).headlineMedium.override(
                            fontFamily: 'Nunito',
                            color: FlutterFlowTheme.of(context).info,
                            letterSpacing: 0.0,
                          ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMenuItem({
    required BuildContext context,
    required MenuItem menuItem,
    required bool isSelected,
  }) {
    return Padding(
      padding: const EdgeInsetsDirectional.fromSTEB(16.0, 0.0, 16.0, 12.0),
      child: InkWell(
        splashColor: Colors.transparent,
        focusColor: Colors.transparent,
        hoverColor: Colors.transparent,
        highlightColor: Colors.transparent,
        onTap: () => Get.toNamed(menuItem.route),
        child: Container(
          width: double.infinity,
          height: 50.0,
          decoration: BoxDecoration(
            color: isSelected ? FlutterFlowTheme.of(context).customAccent1 : Colors.transparent,
            borderRadius: BorderRadius.circular(12.0),
            shape: BoxShape.rectangle,
            border: isSelected
                ? Border.all(
                    color: FlutterFlowTheme.of(context).primary,
                    width: 1.0,
                  )
                : null,
          ),
          child: Padding(
            padding: const EdgeInsetsDirectional.fromSTEB(12.0, 0.0, 12.0, 0.0),
            child: Row(
              mainAxisSize: MainAxisSize.max,
              children: [
                Padding(
                  padding: const EdgeInsetsDirectional.fromSTEB(0.0, 12.0, 12.0, 12.0),
                  child: Container(
                    width: 4.0,
                    height: 100.0,
                    decoration: BoxDecoration(
                      color: isSelected
                          ? FlutterFlowTheme.of(context).info
                          : FlutterFlowTheme.of(context).customAccent1Light,
                      borderRadius: BorderRadius.circular(12.0),
                    ),
                  ),
                ),
                Icon(
                  menuItem.icon,
                  color: isSelected
                      ? FlutterFlowTheme.of(context).info
                      : FlutterFlowTheme.of(context).customAccent2,
                  size: 26.0,
                ),
                Padding(
                  padding: const EdgeInsetsDirectional.fromSTEB(12.0, 0.0, 0.0, 0.0),
                  child: Text(
                    menuItem.label,
                    style: FlutterFlowTheme.of(context).labelMedium.override(
                          fontFamily: 'Open Sans',
                          fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                          color: isSelected
                              ? FlutterFlowTheme.of(context).info
                              : FlutterFlowTheme.of(context).customAccent2,
                          letterSpacing: 0.0,
                        ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildUserProfile(BuildContext context) {
    return Padding(
      padding: const EdgeInsetsDirectional.fromSTEB(16.0, 0.0, 16.0, 16.0),
      child: Column(
        mainAxisSize: MainAxisSize.max,
        mainAxisAlignment: MainAxisAlignment.end,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Divider(
            height: 12.0,
            thickness: 2.0,
            color: FlutterFlowTheme.of(context).customAccent1,
          ),
          Builder(
            builder: (context) => Padding(
              padding: const EdgeInsetsDirectional.fromSTEB(16.0, 0.0, 16.0, 16.0),
              child: InkWell(
                splashColor: Colors.transparent,
                focusColor: Colors.transparent,
                hoverColor: Colors.transparent,
                highlightColor: Colors.transparent,
                onTap: () async {
                  await showDialog(
                    context: context,
                    builder: (dialogContext) {
                      return Dialog(
                        elevation: 0,
                        insetPadding: EdgeInsets.zero,
                        backgroundColor: Colors.transparent,
                        alignment: const AlignmentDirectional(0.0, 0.0).resolve(Directionality.of(context)),
                        child: GestureDetector(
                          onTap: () => Navigator.pop(dialogContext),
                          child: const UserProfileComponentWidget(),
                        ),
                      );
                    },
                  );
                },
                child: Row(
                  mainAxisSize: MainAxisSize.max,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Expanded(
                      child: Padding(
                        padding: const EdgeInsetsDirectional.fromSTEB(12.0, 0.0, 0.0, 4.0),
                        child: Column(
                          mainAxisSize: MainAxisSize.max,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            currentUserDisplayName != ''
                                ? AuthUserStreamWidget(
                                    builder: (context) => Text(
                                      currentUserDisplayName,
                                      maxLines: 1,
                                      style: FlutterFlowTheme.of(context).bodyLarge.override(
                                            fontFamily: 'Jost',
                                            color: FlutterFlowTheme.of(context).info,
                                            letterSpacing: 0.0,
                                            useGoogleFonts: false,
                                          ),
                                    ),
                                  )
                                : const SizedBox.shrink(),
                            Text(
                              currentUserEmail,
                              style: FlutterFlowTheme.of(context).labelSmall.override(
                                    fontFamily: 'Jost',
                                    color: FlutterFlowTheme.of(context).accent4,
                                    letterSpacing: 0.0,
                                    useGoogleFonts: false,
                                  ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    Icon(
                      Icons.logout_rounded,
                      color: FlutterFlowTheme.of(context).info,
                    )
                  ],
                ),
              ),
            ),
          ),
        ].divide(const SizedBox(height: 12.0)),
      ),
    );
  }
}
