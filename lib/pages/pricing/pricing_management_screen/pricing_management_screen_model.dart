import 'package:flutter/material.dart';

import '/flutter_flow/flutter_flow_util.dart';

class PricingManagementScreenModel extends FlutterFlowModel {
  final searchController = TextEditingController();
  final searchFocusNode = FocusNode();
  String selectedFilter = 'All';

  @override
  void dispose() {
    searchController.dispose();
    searchFocusNode.dispose();
  }

  @override
  void initState(BuildContext context) {
    // TODO: implement initState
  }
}
