import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:unimetals_admin/auth/firebase_auth/auth_util.dart';
import 'package:unimetals_admin/backend/schema/enums/enums.dart';
import 'package:unimetals_admin/backend/schema/enums/menu_items.dart';
import 'package:unimetals_admin/flutter_flow/flutter_flow_theme.dart';
import 'package:unimetals_admin/flutter_flow/flutter_flow_util.dart';
import 'package:unimetals_admin/flutter_flow/flutter_flow_widgets.dart';
import 'package:unimetals_admin/pages/components/empty_list_component/empty_list_component_widget.dart';
import 'package:unimetals_admin/pages/components/side_menu/side_menu_widget.dart';
import 'package:unimetals_admin/pages/site_management/delete_site_dialog/delete_site_dialog_widget.dart';
import 'package:file_picker/file_picker.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:unimetals_admin/utils/schedule_utils.dart';

import '/backend/backend.dart';
import 'pricing_management_screen_model.dart';

export 'pricing_management_screen_model.dart';

class PriceController extends GetxController {
  final RxBool isSearching = false.obs;
  final RxString selectedFilter = 'All'.obs;
  final RxList<Map<String, dynamic>> searchResults = <Map<String, dynamic>>[].obs;

  // Stream controller to emit events when prices are refreshed
  final _streamController = StreamController<void>.broadcast();

  // Getter for the stream
  Stream<void> get getStream => _streamController.stream;

  void setSearching(bool value) => isSearching.value = value;
  void setSelectedFilter(String value) => selectedFilter.value = value;
  void setSearchResults(List<Map<String, dynamic>> results) => searchResults.value = results;

  void refreshPrices() {
    update(); // This will trigger a rebuild of any widgets listening to this controller
    _streamController.add(null); // Emit an event to the stream
  }

  @override
  void onClose() {
    _streamController.close(); // Close the stream controller when the controller is disposed
    super.onClose();
  }
}

class PricingManagementScreenWidget extends StatefulWidget {
  const PricingManagementScreenWidget({super.key});

  static String routeName = 'pricing_management_screen';
  static String routePath = '/pricingManagementScreen';

  @override
  State<PricingManagementScreenWidget> createState() => _PricingManagementScreenWidgetState();
}

class _PricingManagementScreenWidgetState extends State<PricingManagementScreenWidget> {
  late PricingManagementScreenModel _model;
  final scaffoldKey = GlobalKey<ScaffoldState>();
  final _priceController = Get.put(PriceController());

  @override
  void initState() {
    super.initState();
    _model = PricingManagementScreenModel();
  }

  @override
  void dispose() {
    _model.dispose();
    super.dispose();
  }

  Stream<List<Map<String, dynamic>>> getPricesStream() {
    return FirebaseFirestore.instance.collection('prices').orderBy('lastUpdated', descending: true).snapshots().asyncMap((snapshot) async {
      final List<Map<String, dynamic>> allPrices = await Future.wait(
        snapshot.docs.map((doc) async {
          final data = doc.data();
          String siteLocation = '';

          if (data['siteId'] != null) {
            try {
              final siteDoc = await FirebaseFirestore.instance.collection('sites').doc(data['siteId']).get();
              if (siteDoc.exists) {
                siteLocation = siteDoc.data()?['siteLocation'] as String? ?? '';
              }
            } catch (e) {
              print('Error fetching site location: $e');
            }
          } else {
            siteLocation = 'Global (All Locations)';
          }

          return {
            'id': doc.id,
            ...data,
            'siteLocation': siteLocation,
            'lastUpdated': (data['lastUpdated'] as Timestamp?)?.toDate() ?? DateTime.now(),
          };
        }),
      );

      // Get current admin's site ID if user is admin
      String? adminSiteId;
      if (currentUserDocument?.role == Role.admin) {
        final siteQuery = await FirebaseFirestore.instance.collection('sites').where('siteAdminId', isEqualTo: currentUserDocument?.uid).get();

        if (siteQuery.docs.isNotEmpty) {
          adminSiteId = siteQuery.docs.first.id;
        }
      }

      // Separate admin's site prices and other prices
      final adminPrices = <Map<String, dynamic>>[];
      final otherPrices = <Map<String, dynamic>>[];

      for (final price in allPrices) {
        // Filter based on category if not 'All'
        if (_model.selectedFilter != 'All' && price['category'] != _model.selectedFilter) {
          continue;
        }

        // For admin, separate their site's prices
        if (adminSiteId != null && price['siteId'] == adminSiteId) {
          adminPrices.add(price);
        }
        // For non-admin users or other prices
        else {
          // If user is not a super admin, only show non-ferrous prices in the "Other Prices" section
          if (currentUserDocument?.role != Role.superAdmin) {
            if (price['category'] == 'Non Ferrous') {
              otherPrices.add(price);
            }
          } else {
            // For super admin, add all prices
            otherPrices.add(price);
          }
        }
      }

      // If user is super admin, show all prices
      if (currentUserDocument?.role == Role.superAdmin) {
        return allPrices;
      }

      // Combine lists with admin prices at the top
      return [...adminPrices, ...otherPrices];
    });
  }

  Widget buildLocationCell(String? fullAddress) {
    if (fullAddress == null) {
      return Text(
        'Global (All Locations)',
        style: FlutterFlowTheme.of(context).bodyMedium,
      );
    }
else{
  return Text(
        fullAddress,
        style: FlutterFlowTheme.of(context).bodyMedium,
      );
}
  }

  DateTime? _getDateTime(dynamic value) {
    if (value == null) return null;
    if (value is DateTime) return value;
    if (value is Timestamp) return value.toDate();
    return null;
  }

  Widget buildLastUpdatedCell(dynamic lastUpdated) {
    if (lastUpdated == null) return const SizedBox();

    DateTime? dateTime;
    if (lastUpdated is Timestamp) {
      dateTime = lastUpdated.toDate();
    } else if (lastUpdated is DateTime) {
      dateTime = lastUpdated;
    }

    if (dateTime == null) return const SizedBox();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          dateTimeFormat('MMM d, y', dateTime),
          style: FlutterFlowTheme.of(context).bodyMedium,
        ),
        Text(
          dateTimeFormat('h:mm a', dateTime),
          style: FlutterFlowTheme.of(context).bodySmall,
        ),
      ],
    );
  }

  Widget buildPricesList() {
    return StreamBuilder<List<Map<String, dynamic>>>(
      stream: getPricesStream(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        }

        if (snapshot.hasError) {
          print(snapshot.error);
          return Center(
            child: Text(
              'Error: ${snapshot.error}',
              style: TextStyle(
                color: FlutterFlowTheme.of(context).error,
              ),
            ),
          );
        }

        final prices = snapshot.data ?? [];

        // Apply search filter
        final searchQuery = _model.searchController.text.toLowerCase();
        if (searchQuery.isNotEmpty) {
          return FutureBuilder<List<Map<String, dynamic>>>(
            future: filterPrices(prices, searchQuery),
            builder: (context, filteredSnapshot) {
              if (filteredSnapshot.connectionState == ConnectionState.waiting) {
                return const Center(
                  child: CircularProgressIndicator(),
                );
              }

              final filteredPrices = filteredSnapshot.data ?? [];

              if (filteredPrices.isEmpty) {
                return const Center(
                  child: EmptyListComponentWidget(
                    text: 'No Prices Found',
                  ),
                );
              }

              return buildPricesListView(filteredPrices);
            },
          );
        }

        return buildPricesListView(prices);
      },
    );
  }

  Future<List<Map<String, dynamic>>> filterPrices(
    List<Map<String, dynamic>> prices,
    String searchQuery,
  ) async {
    final filteredPrices = <Map<String, dynamic>>[];
    final query = searchQuery.toLowerCase();

    for (final price in prices) {
      final metal = (price['metal'] as String?)?.toLowerCase() ?? '';

      // Get site location if siteId exists
      // String siteLocation = '';
      // if (price['siteId'] != null) {
        // try {
          // final siteDoc = await FirebaseFirestore.instance.collection('sites').doc(price['siteId']).get();
          // if (siteDoc.exists) {
            // siteLocation = (siteDoc.data()?['siteLocation'] as String?)?.toLowerCase() ?? '';
          // }
        // } catch (e) {}
      // } else {
        // For global prices
        // siteLocation = 'global (all locations)';
      // }

      // Check if metal name or site location contains the search query
      if (metal.contains(query) 
      // || siteLocation.contains(query)
      ) {
        filteredPrices.add(price);
      }
    }

    return filteredPrices;
  }

  // Synchronous version of filter for immediate results
  List<Map<String, dynamic>> filterPricesSync(List<Map<String, dynamic>> prices, String searchQuery) {
    final query = searchQuery.toLowerCase();

    return prices.where((price) {
      final metal = (price['metal'] as String?)?.toLowerCase() ?? '';
      // For global prices (no siteId)
      final siteLocation = price['siteId'] == null ? 'global (all locations)' : (price['siteLocation'] as String?)?.toLowerCase() ?? ''; // Add siteLocation field to prices data

      // Check both metal name and location
      return metal.contains(query) || siteLocation.contains(query);
    }).toList();
  }

  Widget buildPricesListView(List<Map<String, dynamic>> prices) {
    return FutureBuilder<String?>(
      future: currentUserDocument?.role == Role.admin
          ? FirebaseFirestore.instance.collection('sites').where('siteAdminId', isEqualTo: currentUserDocument?.uid).get().then((snapshot) => snapshot.docs.isNotEmpty ? snapshot.docs.first.id : null)
          : Future.value(null),
      builder: (context, snapshot) {
        final adminSiteId = snapshot.data;
        final isSuperAdmin = currentUserDocument?.role == Role.superAdmin;

        if (prices.isEmpty) {
          return const Center(
            child: EmptyListComponentWidget(
              text: 'No Prices Found',
            ),
          );
        }

        // For super admin, group prices by metal for the delete all functionality
        if (isSuperAdmin) {
          // Group prices by metal
          final Map<String, List<Map<String, dynamic>>> groupedPrices = {};

          for (final price in prices) {
            final metalId = price['metalId'] as String?;
            final metal = price['metal'] as String?;

            if (metalId != null && metal != null) {
              if (!groupedPrices.containsKey(metalId)) {
                groupedPrices[metalId] = [];
              }
              groupedPrices[metalId]!.add(price);
            }
          }

          return SingleChildScrollView(
            child: Column(
              children: groupedPrices.entries.map((entry) {
                final metalId = entry.key;
                final metalPrices = entry.value;
                final metalName = metalPrices.first['metal'] as String;

                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Metal header with delete all button
                    Padding(
                      padding: const EdgeInsetsDirectional.fromSTEB(16.0, 16.0, 16.0, 8.0),
                      child: Row(
                        children: [
                          Container(
                            decoration: BoxDecoration(
                              color: FlutterFlowTheme.of(context).secondaryBackground,
                              borderRadius: BorderRadius.circular(4.0),
                              border: Border.all(
                                color: FlutterFlowTheme.of(context).alternate,
                                width: 1.0,
                              ),
                            ),
                            padding: const EdgeInsetsDirectional.fromSTEB(12.0, 6.0, 12.0, 6.0),
                            child: Text(
                              metalName,
                              style: FlutterFlowTheme.of(context).titleMedium.override(
                                    fontFamily: 'Open Sans',
                                    color: FlutterFlowTheme.of(context).primaryText,
                                    fontWeight: FontWeight.w600,
                                    fontSize: 14,
                                  ),
                            ),
                          ),
                          Expanded(
                            child: Padding(
                              padding: const EdgeInsetsDirectional.fromSTEB(12.0, 0.0, 0.0, 0.0),
                              child: Container(
                                height: 1.0,
                                color: FlutterFlowTheme.of(context).alternate,
                              ),
                            ),
                          ),

                          buildPriceActions(metalId, metalName),
                          // Delete all button for this metal
                          IconButton(
                            icon: Icon(
                              Icons.delete_sweep,
                              color: FlutterFlowTheme.of(context).error,
                              size: 20,
                            ),
                            onPressed: () => deleteAllMetalPrices(metalId, metalName, _priceController),
                            tooltip: 'Delete all prices for this metal',
                          ),
                          const SizedBox(
                            width: 25,
                          )
                        ],
                      ),
                    ),
                    // Metal prices
                    Column(
                      children: List.generate(
                        metalPrices.length,
                        (index) => Column(
                          children: [
                            buildPriceItem(metalPrices[index], false),
                            if (index < metalPrices.length - 1)
                              Divider(
                                height: 1,
                                color: FlutterFlowTheme.of(context).alternate,
                              ),
                          ],
                        ),
                      ),
                    ),
                    Divider(
                      height: 24,
                      thickness: 2,
                      color: FlutterFlowTheme.of(context).alternate,
                    ),
                  ],
                );
              }).toList(),
            ),
          );
        }

        // For non-super admin users, keep the separated view
        final adminPrices = prices.where((price) => price['siteId'] == adminSiteId).toList();

        // Filter other prices based on user role
        List<Map<String, dynamic>> otherPrices;
        if (currentUserDocument?.role != Role.superAdmin) {
          // For non-super admin, only show non-ferrous prices in "Other Prices" section
          otherPrices = prices.where((price) => price['siteId'] != adminSiteId && price['category'] == 'Non Ferrous').toList();
        } else {
          // For super admin, show all other prices
          otherPrices = prices.where((price) => price['siteId'] != adminSiteId).toList();
        }

        return SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (adminPrices.isNotEmpty) ...[
                Padding(
                  padding: const EdgeInsetsDirectional.fromSTEB(16.0, 24.0, 16.0, 12.0),
                  child: Row(
                    children: [
                      Container(
                        decoration: BoxDecoration(
                          color: FlutterFlowTheme.of(context).secondaryBackground,
                          borderRadius: BorderRadius.circular(4.0),
                          border: Border.all(
                            color: FlutterFlowTheme.of(context).alternate,
                            width: 1.0,
                          ),
                        ),
                        padding: const EdgeInsetsDirectional.fromSTEB(12.0, 6.0, 12.0, 6.0),
                        child: Text(
                          'My Site',
                          style: FlutterFlowTheme.of(context).titleMedium.override(
                                fontFamily: 'Open Sans',
                                color: FlutterFlowTheme.of(context).secondaryText,
                                fontWeight: FontWeight.w600,
                                fontSize: 14,
                              ),
                        ),
                      ),
                      Expanded(
                        child: Padding(
                          padding: const EdgeInsetsDirectional.fromSTEB(12.0, 0.0, 0.0, 0.0),
                          child: Container(
                            height: 1.0,
                            color: FlutterFlowTheme.of(context).alternate,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                Column(
                  children: List.generate(
                    adminPrices.length,
                    (index) => Column(
                      children: [
                        buildPriceItem(adminPrices[index], true),
                        if (index < adminPrices.length - 1)
                          Divider(
                            height: 1,
                            color: FlutterFlowTheme.of(context).alternate,
                          ),
                      ],
                    ),
                  ),
                ),
              ],
              if (otherPrices.isNotEmpty) ...[
                Padding(
                  padding: const EdgeInsetsDirectional.fromSTEB(16.0, 24.0, 16.0, 12.0),
                  child: Row(
                    children: [
                      Container(
                        decoration: BoxDecoration(
                          color: FlutterFlowTheme.of(context).secondaryBackground,
                          borderRadius: BorderRadius.circular(4.0),
                          border: Border.all(
                            color: FlutterFlowTheme.of(context).alternate,
                            width: 1.0,
                          ),
                        ),
                        padding: const EdgeInsetsDirectional.fromSTEB(12.0, 6.0, 12.0, 6.0),
                        child: Text(
                          'Non Ferrous',
                          style: FlutterFlowTheme.of(context).titleMedium.override(
                                fontFamily: 'Open Sans',
                                color: FlutterFlowTheme.of(context).secondaryText,
                                fontWeight: FontWeight.w600,
                                fontSize: 14,
                              ),
                        ),
                      ),
                      Expanded(
                        child: Padding(
                          padding: const EdgeInsetsDirectional.fromSTEB(12.0, 0.0, 0.0, 0.0),
                          child: Container(
                            height: 1.0,
                            color: FlutterFlowTheme.of(context).alternate,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                Column(
                  children: List.generate(
                    otherPrices.length,
                    (index) => Column(
                      children: [
                        buildPriceItem(otherPrices[index], false),
                        if (index < otherPrices.length - 1)
                          Divider(
                            height: 1,
                            color: FlutterFlowTheme.of(context).alternate,
                          ),
                      ],
                    ),
                  ),
                ),
              ],
            ],
          ),
        );
      },
    );
  }

  Widget buildPriceItem(Map<String, dynamic> price, bool isAdminSite) {
    return GetBuilder<PriceController>(
      builder: (controller) => FutureBuilder<bool>(
        future: canManagePrice(price),
        builder: (context, snapshot) {
          final bool canManage = snapshot.data ?? false;

          return GestureDetector(
            onTap: currentUserDocument?.role == Role.superAdmin
                ? () {}
                : () async {
                    // First, fetch the complete price data including site location if it exists
                    Map<String, dynamic> completePrice = {...price};

                    if (price['siteId'] != null) {
                      try {
                        final siteDoc = await FirebaseFirestore.instance.collection('sites').doc(price['siteId']).get();

                        if (siteDoc.exists) {
                          completePrice['siteLocation'] = siteDoc.data()?['siteLocation'];
                        }
                      } catch (e) {
                        print('Error fetching site data: $e');
                      }
                    }

                    // Navigate to pricing details screen with the complete price data
                    Get.toNamed(
                      '/pricing-edit', // Changed from '/pricing-details'
                      arguments: {
                        ...completePrice,
                        'isEdit': true,
                      },
                    )?.then((_) => controller.refreshPrices());
                  },
            child: MouseRegion(
              cursor: currentUserDocument?.role == Role.admin ? SystemMouseCursors.click : MouseCursor.defer,
              child: Container(
                padding: const EdgeInsetsDirectional.fromSTEB(30.0, 12.0, 16.0, 12.0),
                decoration: BoxDecoration(
                  color: isAdminSite ? FlutterFlowTheme.of(context).primaryBackground.withOpacity(0.5) : FlutterFlowTheme.of(context).secondaryBackground,
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.max,
                  children: [
                    Expanded(
                      flex: 2,
                      child: buildLocationCell(price['fullAddress']),
                    ),
                    const SizedBox(width: 20.0),
                    Expanded(
                      flex: 1,
                      child: Row(
                        children: [
                          Container(
                            padding: const EdgeInsetsDirectional.fromSTEB(8, 4, 8, 4),
                            decoration: BoxDecoration(
                              color: price['category'] == 'Ferrous' || price['category'] == 'ELV'
                                  ? FlutterFlowTheme.of(context).primary.withOpacity(0.1)
                                  : FlutterFlowTheme.of(context).secondary.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Text(
                              price['category'] ?? '',
                              style: FlutterFlowTheme.of(context).bodySmall.override(
                                    fontFamily: 'Open Sans',
                                    color: price['category'] == 'Ferrous' || price['category'] == 'ELV' ? FlutterFlowTheme.of(context).primary : FlutterFlowTheme.of(context).secondary,
                                    fontSize: 12,
                                    fontWeight: FontWeight.w500,
                                  ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    Expanded(
                      flex: 2,
                      child: Text(
                        price['metal'] ?? '',
                        style: FlutterFlowTheme.of(context).bodyMedium.override(
                              fontFamily: 'Open Sans',
                              fontWeight: FontWeight.w600,
                            ),
                      ),
                    ),
                    Expanded(
                      flex: 2,
                      child: Padding(
                        padding: const EdgeInsets.only(left: 10.0),
                        child: buildLastUpdatedCell(price['lastUpdated']),
                      ),
                    ),
                    // currentUserDocument?.role == Role.superAdmin
                    //     ? buildPriceActions(price, canManage, controller)
                    //     :
                    currentUserDocument?.role == Role.superAdmin
                        ? const SizedBox(
                            width: 90,
                          )
                        : const SizedBox(
                            width: 20,
                          ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget buildPriceActions(String metalId, String metalName) {
    return SizedBox(
      width: 45,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          IconButton(
              tooltip: 'Edit all prices for this metal',
              icon: Icon(
                Icons.edit_outlined,
                color: FlutterFlowTheme.of(context).primary,
                size: 20,
              ),
              onPressed: () async {
                try {
                  // Show loading indicator
                  showDialog(
                    context: context,
                    barrierDismissible: false,
                    builder: (context) => const Center(child: CircularProgressIndicator()),
                  );

                  // Get the first price for this metal to edit
                  final pricesSnapshot = await FirebaseFirestore.instance.collection('prices').where('metalId', isEqualTo: metalId).limit(1).get();

                  // Close loading dialog
                  Navigator.of(context, rootNavigator: true).pop();

                  if (pricesSnapshot.docs.isNotEmpty) {
                    final priceData = pricesSnapshot.docs.first.data();

                    // Navigate to pricing details page instead of edit page
                    Get.toNamed(
                      '/pricing-details',
                      arguments: {
                        ...priceData,
                        'metalId': metalId,
                        'metalName': metalName,
                        'isEdit': true, // Pass isEdit flag to enable editing
                      },
                    )?.then((_) => _priceController.refreshPrices());
                  }
                } catch (e) {
                  // Close loading dialog if error occurs
                  Navigator.of(context, rootNavigator: true).pop();

                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Error loading price data: $e'),
                      backgroundColor: FlutterFlowTheme.of(context).error,
                    ),
                  );
                }
              }),
        ],
      ),
    );
  }

  // Add this helper method to check permissions
  Future<bool> canManagePrice(Map<String, dynamic> price) async {
    final currentUser = currentUserDocument;

    // Super admins can manage all prices
    if (currentUser?.role == Role.superAdmin) {
      return true;
    }

    // Regular admins can only manage their site's price
    if (currentUser?.role == Role.admin && price['siteId'] != null) {
      try {
        final siteDoc = await FirebaseFirestore.instance.collection('sites').doc(price['siteId']).get();

        if (siteDoc.exists) {
          final siteData = siteDoc.data() as Map<String, dynamic>;
          return siteData['siteAdminId'] == currentUser?.uid;
        }
      } catch (e) {
        print('Error checking price permissions: $e');
      }
    }

    return false;
  }

  // Add this method to check if admin's site has a price
  Future<bool> checkAdminSiteHasPrice() async {
    if (currentUserDocument?.role != Role.admin) return false;

    try {
      // Get the site where current admin is assigned
      final siteQuery = await FirebaseFirestore.instance.collection('sites').where('siteAdminId', isEqualTo: currentUserDocument?.uid).get();

      if (siteQuery.docs.isEmpty) return false;

      // Check if the site has a priceId
      final site = siteQuery.docs.first;
      return site.data().containsKey('priceId') && site.data()['priceId'] != null;
    } catch (e) {
      print('Error checking admin site price: $e');
      return false;
    }
  }

  // Navigate to pricing details
  void navigateToPricingDetails() {
    Get.toNamed('/pricing-details')?.then((_) {
      _priceController.refreshPrices();
    });
  }

  // Widget buildFilterDropdown() {
  //   return Obx(() => DropdownButton<String>(
  //         value: _priceController.selectedFilter.value,
  //         items: ['All', 'Ferrous', 'Non Ferrous'].map((String value) {
  //           return DropdownMenuItem<String>(
  //             value: value,
  //             child: Text(value),
  //           );
  //         }).toList(),
  //         onChanged: (String? newValue) {
  //           if (newValue != null) {
  //             _priceController.setSelectedFilter(newValue);
  //             _priceController.refreshPrices();
  //           }
  //         },
  //       ));
  // }

  Widget buildSearchField() {
    return TextField(
      controller: _model.searchController,
      onChanged: (value) {
        _priceController.setSearching(value.isNotEmpty);
        // Implement search logic here
      },
      // ... rest of your search field properties
    );
  }

  // Add this new method to delete all prices for a specific metal
  Future<void> deleteAllMetalPrices(String metalId, String metalName, PriceController controller) async {
    final confirm = await showDialog<bool>(
      context: context,
      builder: (dialogContext) => Dialog(
        elevation: 0,
        insetPadding: EdgeInsets.zero,
        backgroundColor: Colors.transparent,
        alignment: const AlignmentDirectional(0.0, 0.0).resolve(Directionality.of(context)),
        child: GestureDetector(
          onTap: () {
            FocusScope.of(dialogContext).unfocus();
            FocusManager.instance.primaryFocus?.unfocus();
          },
          child: DeleteSiteDialogWidget(
            siteRef: FirebaseFirestore.instance.collection('prices').doc(metalId),
            title: 'Delete All Prices',
            message: 'Are you sure you want to delete all prices for $metalName across all locations?\nThis action cannot be undone.',
          ),
        ),
      ),
    );

    if (confirm == true) {
      try {
        // Show loading indicator
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) => const Center(child: CircularProgressIndicator()),
        );

        // Get all prices for this metal
        final pricesSnapshot = await FirebaseFirestore.instance.collection('prices').where('metalId', isEqualTo: metalId).get();

        // Delete all price documents for this metal
        final batch = FirebaseFirestore.instance.batch();
        for (final doc in pricesSnapshot.docs) {
          batch.delete(doc.reference);
        }

        // Also delete the metal document from metals collection
        batch.delete(FirebaseFirestore.instance.collection('metals').doc(metalId));

        // Delete any subtypes in the subcollection
        final subtypesSnapshot = await FirebaseFirestore.instance.collection('metals').doc(metalId).collection('subtypes').get();

        for (final doc in subtypesSnapshot.docs) {
          batch.delete(doc.reference);
        }

        await batch.commit();

        // Close loading dialog
        Navigator.of(context, rootNavigator: true).pop();

        // Refresh the prices list
        controller.refreshPrices();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('All prices for $metalName deleted successfully'),
              backgroundColor: FlutterFlowTheme.of(context).success,
            ),
          );
        }
      } catch (e) {
        // Close loading dialog
        Navigator.of(context, rootNavigator: true).pop();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error deleting prices: $e'),
              backgroundColor: FlutterFlowTheme.of(context).error,
            ),
          );
        }
      }
    }
  }

  Future<void> importFromCSV() async {
    // Show dialog to select CSV type first
    final String? selectedType = await showDialog<String>(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16.0),
          ),
          child: Container(
            width: 400,
            padding: const EdgeInsets.all(24.0),
            decoration: BoxDecoration(
              color: FlutterFlowTheme.of(context).secondaryBackground,
              borderRadius: BorderRadius.circular(16.0),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Select CSV Type',
                  style: FlutterFlowTheme.of(context).headlineSmall.override(
                        fontFamily: 'Open Sans',
                        fontWeight: FontWeight.w600,
                      ),
                ),
                const SizedBox(height: 16),
                Text(
                  'Please select the type of pricing data you want to import:',
                  style: FlutterFlowTheme.of(context).bodyMedium,
                ),
                const SizedBox(height: 24),
                _buildCsvTypeButton(
                  context: context,
                  type: 'Ferrous',
                  icon: Icons.iron,
                  color: FlutterFlowTheme.of(context).primary,
                ),
                const SizedBox(height: 12),
                _buildCsvTypeButton(
                  context: context,
                  type: 'Non Ferrous',
                  icon: Icons.category,
                  color: FlutterFlowTheme.of(context).secondary,
                ),
                const SizedBox(height: 12),
                _buildCsvTypeButton(
                  context: context,
                  type: 'ELV',
                  icon: Icons.directions_car,
                  color: FlutterFlowTheme.of(context).tertiary,
                ),
                const SizedBox(height: 24),
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    FFButtonWidget(
                      onPressed: () => Navigator.pop(context),
                      text: 'Cancel',
                      options: FFButtonOptions(
                        width: 100,
                        height: 40,
                        padding: const EdgeInsets.all(0),
                        iconPadding: const EdgeInsets.all(0),
                        color: FlutterFlowTheme.of(context).secondaryBackground,
                        textStyle: FlutterFlowTheme.of(context).bodyMedium.override(
                              fontFamily: 'Open Sans',
                              color: FlutterFlowTheme.of(context).primaryText,
                            ),
                        elevation: 0,
                        borderSide: BorderSide(
                          color: FlutterFlowTheme.of(context).alternate,
                          width: 1,
                        ),
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );

    if (selectedType != null) {
      // Now pick the file
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['csv'],
      );

      if (result != null) {
        final file = result.files.single;

        // Show loading dialog
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (BuildContext context) {
            return Dialog(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16.0),
              ),
              child: Container(
                width: 300,
                padding: const EdgeInsets.all(24.0),
                decoration: BoxDecoration(
                  color: FlutterFlowTheme.of(context).secondaryBackground,
                  borderRadius: BorderRadius.circular(16.0),
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const CircularProgressIndicator(),
                    const SizedBox(height: 24),
                    Text(
                      'Validating CSV file...',
                      style: FlutterFlowTheme.of(context).bodyMedium,
                    ),
                  ],
                ),
              ),
            );
          },
        );

        // Process the file in a separate isolate or Future to not block the UI
        try {
          // Read the file content
          final bytes = file.bytes;
          final content = bytes != null ? String.fromCharCodes(bytes) : '';
          // For web, handle file reading differently if bytes is null
          final String csvContent = content.isEmpty && file.path != null ? await File(file.path!).readAsString() : content;
          // Split the content by lines
          final lines = csvContent.split('\n');
          // Check if the file has content
          if (lines.isEmpty) {
            throw Exception('CSV file is empty');
          }

          // Get the header line and trim whitespace
          final header = lines[0].trim();

          // Validate header based on selected type
          bool isValid = false;
          String errorMessage = '';

          if (selectedType == 'Non Ferrous') {
            // Expected: Product, Grade, Price, DefaultProduct (as separate columns)
            final expectedColumns = ['product', 'grade', 'price', 'defaultproduct'];
            // Clean the header by removing BOM character if present
            final headerColumns = header
                .toLowerCase()
                .replaceAll('ï»¿', '') // Remove BOM character
                .split(',')
                .map((e) => e.trim())
                .toList();

            isValid = expectedColumns.every((column) => headerColumns.contains(column));
            if (!isValid) {
              errorMessage = 'Invalid CSV format for Non Ferrous. Please upload a file that contains Product, Grade, and Price columns.';
            }
          } else {
            // Ferrous or ELV: Product, Grade, Price, Lat, Long, Location Name (as separate columns)
            // Note: "LAT,LONG" might be in quotes as a single column
            final expectedColumns = ['product', 'grade', 'price', 'lat', 'long', 'location name'];
            // Clean the header by removing BOM character if present
            final headerColumns = header
                .toLowerCase()
                .replaceAll('ï»¿', '') // Remove BOM character
                .split(',')
                .map((e) => e.trim().replaceAll('"', '')) // Remove quotes
                .toList();

            // Special handling for "LAT,LONG" as a single column
            isValid = headerColumns.contains('product') &&
                headerColumns.contains('grade') &&
                headerColumns.contains('price') &&
                (headerColumns.contains('lat,long') || (headerColumns.contains('lat') && headerColumns.contains('long'))) &&
                headerColumns.contains('location name');

            if (!isValid) {
              errorMessage = 'Invalid CSV format for $selectedType. Please upload a file that contains Product, Grade, Price, Lat, and Long columns.';
            }
          }

          // Close the loading dialog
          Navigator.of(context, rootNavigator: true).pop();

          if (isValid) {
            // Show confirmation dialog for valid file
            showDialog(
              context: context,
              builder: (BuildContext context) {
                return Dialog(
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16.0),
                  ),
                  child: Container(
                    width: 400,
                    padding: const EdgeInsets.all(24.0),
                    decoration: BoxDecoration(
                      color: FlutterFlowTheme.of(context).secondaryBackground,
                      borderRadius: BorderRadius.circular(16.0),
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'CSV File Validated',
                          style: FlutterFlowTheme.of(context).headlineSmall.override(
                                fontFamily: 'Open Sans',
                                fontWeight: FontWeight.w600,
                              ),
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'File: ${file.name}',
                          style: FlutterFlowTheme.of(context).bodyMedium.override(
                                fontFamily: 'Open Sans',
                                fontWeight: FontWeight.w500,
                              ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'Category: $selectedType',
                          style: FlutterFlowTheme.of(context).bodyMedium.override(
                                fontFamily: 'Open Sans',
                                fontWeight: FontWeight.w500,
                              ),
                        ),
                        const SizedBox(height: 24),
                        Text(
                          'Do you want to upload this data to the database?',
                          style: FlutterFlowTheme.of(context).bodyMedium,
                        ),
                        const SizedBox(height: 24),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            FFButtonWidget(
                              onPressed: () => Navigator.pop(context),
                              text: 'Cancel',
                              options: FFButtonOptions(
                                width: 100,
                                height: 40,
                                padding: const EdgeInsets.all(0),
                                iconPadding: const EdgeInsets.all(0),
                                color: FlutterFlowTheme.of(context).secondaryBackground,
                                textStyle: FlutterFlowTheme.of(context).bodyMedium.override(
                                      fontFamily: 'Open Sans',
                                      color: FlutterFlowTheme.of(context).primaryText,
                                    ),
                                elevation: 0,
                                borderSide: BorderSide(
                                  color: FlutterFlowTheme.of(context).alternate,
                                  width: 1,
                                ),
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                            const SizedBox(width: 16),
                            FFButtonWidget(
                              onPressed: () async {
                                Navigator.pop(context);
                                // Process the CSV file and upload to database
                                await processAndUploadCSV(csvContent, selectedType);
                              },
                              text: 'Upload',
                              options: FFButtonOptions(
                                width: 100,
                                height: 40,
                                padding: const EdgeInsets.all(0),
                                iconPadding: const EdgeInsets.all(0),
                                color: FlutterFlowTheme.of(context).primary,
                                textStyle: FlutterFlowTheme.of(context).bodyMedium.override(
                                      fontFamily: 'Open Sans',
                                      color: Colors.white,
                                      fontWeight: FontWeight.w500,
                                    ),
                                elevation: 2,
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                );
              },
            );
          } else {
            // Show error dialog for invalid file
            showDialog(
              context: context,
              builder: (BuildContext context) {
                return Dialog(
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16.0),
                  ),
                  child: Container(
                    width: 400,
                    padding: const EdgeInsets.all(24.0),
                    decoration: BoxDecoration(
                      color: FlutterFlowTheme.of(context).secondaryBackground,
                      borderRadius: BorderRadius.circular(16.0),
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Invalid CSV Format',
                          style: FlutterFlowTheme.of(context).headlineSmall.override(
                                fontFamily: 'Open Sans',
                                fontWeight: FontWeight.w600,
                                color: FlutterFlowTheme.of(context).error,
                              ),
                        ),
                        const SizedBox(height: 16),
                        Text(
                          errorMessage,
                          style: FlutterFlowTheme.of(context).bodyMedium,
                        ),
                        const SizedBox(height: 24),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            FFButtonWidget(
                              onPressed: () => Navigator.pop(context),
                              text: 'OK',
                              options: FFButtonOptions(
                                width: 100,
                                height: 40,
                                padding: const EdgeInsets.all(0),
                                iconPadding: const EdgeInsets.all(0),
                                color: FlutterFlowTheme.of(context).primary,
                                textStyle: FlutterFlowTheme.of(context).bodyMedium.override(
                                      fontFamily: 'Open Sans',
                                      color: Colors.white,
                                    ),
                                elevation: 2,
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                );
              },
            );
          }
        } catch (e) {
          // Close the loading dialog
          Navigator.of(context, rootNavigator: true).pop();

          // Show error message
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error processing CSV file: ${e.toString()}'),
              backgroundColor: FlutterFlowTheme.of(context).error,
            ),
          );
        }
      }
    }
  }

  Widget _buildCsvTypeButton({
    required BuildContext context,
    required String type,
    required IconData icon,
    required Color color,
  }) {
    // State to track hover status
    RxBool isHovered = false.obs;

    return StatefulBuilder(
      builder: (context, setState) {
        return MouseRegion(
          onEnter: (_) => isHovered.value = true,
          onExit: (_) => isHovered.value = false,
          child: InkWell(
            onTap: () => Navigator.pop(context, type),
            child: Obx(() => AnimatedContainer(
                  duration: const Duration(milliseconds: 200),
                  transform: Matrix4.identity()..scale(isHovered.value ? 1.03 : 1.0),
                  transformAlignment: Alignment.center,
                  width: double.infinity,
                  padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 20),
                  decoration: BoxDecoration(
                    color: color.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: color.withOpacity(0.3),
                    ),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        icon,
                        color: color,
                        size: 24,
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              type,
                              style: FlutterFlowTheme.of(context).titleMedium.override(
                                    fontFamily: 'Open Sans',
                                    color: color,
                                    fontWeight: FontWeight.w600,
                                  ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              'Import  pricing data from CSV',
                              style: FlutterFlowTheme.of(context).bodySmall,
                            ),
                          ],
                        ),
                      ),
                      Icon(
                        Icons.arrow_forward_ios,
                        color: color,
                        size: 16,
                      ),
                    ],
                  ),
                )),
          ),
        );
      },
    );
  }

  // Add this method to process and upload CSV data
  Future<void> processAndUploadCSV(String csvContent, String category) async {
    // Show loading dialog
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16.0),
          ),
          child: Container(
            width: 300,
            padding: const EdgeInsets.all(24.0),
            decoration: BoxDecoration(
              color: FlutterFlowTheme.of(context).secondaryBackground,
              borderRadius: BorderRadius.circular(16.0),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const CircularProgressIndicator(),
                const SizedBox(height: 24),
                Text(
                  'Uploading data to database...',
                  style: FlutterFlowTheme.of(context).bodyMedium,
                ),
              ],
            ),
          ),
        );
      },
    );

    try {
      final firestore = FirebaseFirestore.instance;
      
      // 1. Delete existing metals for the selected category
      final metalsToDelete = await firestore.collection('metals').where('category', isEqualTo: category).get();
      
      // First collect all metalIds to delete their associated prices
      final List<String> metalIdsToDelete = metalsToDelete.docs.map((doc) => doc.id).toList();
      
      // Delete prices associated with these metals - handle in smaller batches if needed
      if (metalIdsToDelete.isNotEmpty) {
        // For Non Ferrous, we need to handle the case where there might be many metals
        // Firestore has a limit of 10 items in a whereIn query, so we need to batch them
        for (int i = 0; i < metalIdsToDelete.length; i += 10) {
          final endIdx = min(i + 10, metalIdsToDelete.length);
          final batchIds = metalIdsToDelete.sublist(i, endIdx);
          
          final batch = firestore.batch();
          final pricesToDelete = await firestore.collection('prices')
              .where('metalId', whereIn: batchIds)
              .get();
              
          for (final priceDoc in pricesToDelete.docs) {
            batch.delete(priceDoc.reference);
          }
          
          // Commit the batch to delete this set of prices
          await batch.commit();
        }
      }

      // Now delete the metals and their subtypes
      for (final doc in metalsToDelete.docs) {
        // Delete subtypes subcollection first
        final subtypes = await doc.reference.collection('subtypes').get();
        for (final subtype in subtypes.docs) {
          await subtype.reference.delete();
        }
        // Then delete the metal document
        await doc.reference.delete();
      }

      // 2. Parse CSV content
      final lines = csvContent.split('\n');
      final header = lines[0].toLowerCase().replaceAll('ï»¿', '');
      final rows = lines.sublist(1).where((line) => line.trim().isNotEmpty).toList();

      // Map to store metals by product name
      final Map<String, DocumentReference> metalRefs = {};
      final Map<String, Map<String, DocumentReference>> subtypeRefs = {};

      if (category == 'Non Ferrous') {
        await processNonFerrousCSV(header, rows, metalRefs, subtypeRefs);
      } else {
        await processFerrousOrELVCSV(header, rows, category, metalRefs, subtypeRefs);
      }

      // Close loading dialog
      Navigator.of(context, rootNavigator: true).pop();

      // Show success message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('$category metals uploaded successfully'),
          backgroundColor: FlutterFlowTheme.of(context).success,
        ),
      );

      // Refresh the prices list
      _priceController.refreshPrices();
    } catch (e) {
      // Close loading dialog
      Navigator.of(context, rootNavigator: true).pop();

      // Show error message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error uploading data: ${e.toString()}'),
          backgroundColor: FlutterFlowTheme.of(context).error,
        ),
      );
    }
  }

  Future<void> processNonFerrousCSV(
    String header, 
    List<String> rows,
    Map<String, DocumentReference> metalRefs,
    Map<String, Map<String, DocumentReference>> subtypeRefs
  ) async {
    final firestore = FirebaseFirestore.instance;
    
    // Parse header to find column indices
    final headerColumns = header.split(',').map((e) => e.trim()).toList();
    final productIndex = headerColumns.indexOf('product');
    final gradeIndex = headerColumns.indexOf('grade');
    final priceIndex = headerColumns.indexOf('price');
    final defaultProductIndex = headerColumns.indexOf('defaultproduct');
    
    // First pass: Create all metals and subtypes
    for (final row in rows) {
      final columns = parseCSVRow(row);
      if (columns.length <= max(productIndex, max(gradeIndex, priceIndex))) {
        continue; // Skip invalid rows
      }
      
      final product = columns[productIndex].trim();
      final grade = columns[gradeIndex].trim();
      
      if (product.isEmpty || grade.isEmpty) {
        continue; // Skip rows with empty product or grade
      }
      
      // Create or get metal document (only once per product)
      if (!metalRefs.containsKey(product)) {
        final metalRef = firestore.collection('metals').doc();
        await metalRef.set({
          'id': metalRef.id,
          'type': product,
          'category': 'Non Ferrous',
          'createdAt': FieldValue.serverTimestamp(),
          'updatedAt': FieldValue.serverTimestamp(),
        });
        metalRefs[product] = metalRef;
        subtypeRefs[product] = {};
      }
      
      // Create or get subtype document (only once per product+grade combination)
      final metalRef = metalRefs[product]!;
      if (!subtypeRefs[product]!.containsKey(grade)) {
        final subtypeRef = metalRef.collection('subtypes').doc();
        await subtypeRef.set({
          'id': subtypeRef.id,
          'name': grade,
          'createdAt': FieldValue.serverTimestamp(),
        });
        subtypeRefs[product]![grade] = subtypeRef;
      }
    }
    
    // Second pass: Create price documents for each product
    for (final product in metalRefs.keys) {
      // Get all rows for this product
      final productRows = rows.where((row) {
        final columns = parseCSVRow(row);
        if (columns.length <= productIndex) return false;
        return columns[productIndex].trim() == product;
      }).toList();
      
      // Collect all subtypes for this product
      final List<Map<String, dynamic>> typesList = [];
      bool isDefault = false;
      
      for (final row in productRows) {
        final columns = parseCSVRow(row);
        final grade = columns[gradeIndex].trim();
        final priceStr = columns[priceIndex].trim().replaceAll(',', '');
        final price = double.tryParse(priceStr) ?? 0.0;
        
        // Check if this is a default product
        if (defaultProductIndex >= 0 && 
            defaultProductIndex < columns.length && 
            columns[defaultProductIndex].trim().toLowerCase() == 'yes') {
          isDefault = true;
        }
        
        // Add subtype to the types list
        typesList.add({
          'subtypeId': subtypeRefs[product]![grade]!.id,
          'name': grade,
          'price': price,
        });
      }
      
      // Create a single price document for this product with all its subtypes
      final priceRef = firestore.collection('prices').doc();
      final metalRef = metalRefs[product]!;
      
      
      await priceRef.set({
        'id': priceRef.id,
        'metalId': metalRef.id,
        'metal': product,
        'category': 'Non Ferrous',
       
        'types': typesList,
        'isDefault': isDefault,
        'createdAt': FieldValue.serverTimestamp(),
        'lastUpdated': FieldValue.serverTimestamp(),
      });
    }
  }

  Future<void> processFerrousOrELVCSV(String header, List<String> rows, String category, Map<String, DocumentReference> metalRefs, Map<String, Map<String, DocumentReference>> subtypeRefs) async {
    final firestore = FirebaseFirestore.instance;

    // Parse header to find column indices
    final headerColumns = header.split(',').map((e) => e.trim().replaceAll('"', '')).toList();
    print('Header columns: $headerColumns');

    final productIndex = headerColumns.indexOf('product');
    final gradeIndex = headerColumns.indexOf('grade');
    final priceIndex = headerColumns.indexOf('price');

    // Find location name index
    final locationNameIndex = headerColumns.indexOf('location name');
    print('Location name index: $locationNameIndex');

    // Initialize lat/long indices
    int latLongIndex = -1;
    int latIndex = -1;
    int longIndex = -1;
    int finalLocationNameIndex = locationNameIndex;

    // Check for various possible header formats for lat/long
    if (headerColumns.contains('lat,long')) {
      latLongIndex = headerColumns.indexOf('lat,long');
    } else if (headerColumns.contains('lat, long')) {
      latLongIndex = headerColumns.indexOf('lat, long');
    } else if (headerColumns.contains('location name')) {
      // If we have location name column, check the column before it for lat/long
      int locIndex = headerColumns.indexOf('location name');
      if (locIndex > 0) {
        // The column before location name might be lat/long
        latLongIndex = locIndex - 1;
      }
    } else {
      latIndex = headerColumns.indexOf('lat');
      longIndex = headerColumns.indexOf('long');
    }

    // First pass: Create all metals and subtypes
    for (final row in rows) {
      final columns = parseCSVRow(row);
      if (columns.length <= max(productIndex, max(gradeIndex, priceIndex))) {
        continue; // Skip invalid rows
      }

      final product = columns[productIndex].trim();
      final grade = columns[gradeIndex].trim();

      if (product.isEmpty || grade.isEmpty) {
        continue; // Skip rows with empty product or grade
      }

      // Create or get metal document (only once per product)
      if (!metalRefs.containsKey(product)) {
        final metalRef = firestore.collection('metals').doc();
        await metalRef.set({
          'id': metalRef.id,
          'type': product,
          'category': category,
          'createdAt': FieldValue.serverTimestamp(),
          'updatedAt': FieldValue.serverTimestamp(),
        });
        metalRefs[product] = metalRef;
        subtypeRefs[product] = {};
      }

      // Create or get subtype document (only once per product+grade combination)
      final metalRef = metalRefs[product]!;
      if (!subtypeRefs[product]!.containsKey(grade)) {
        final subtypeRef = metalRef.collection('subtypes').doc();
        await subtypeRef.set({
          'id': subtypeRef.id,
          'name': grade,
          'createdAt': FieldValue.serverTimestamp(),
        });
        subtypeRefs[product]![grade] = subtypeRef;
      }
    }

    // Second pass: Group rows by lat/lng coordinates
    final Map<String, List<Map<String, dynamic>>> rowsByCoordinates = {};
    
    for (final row in rows) {
      final columns = parseCSVRow(row);

      if (columns.length <= max(productIndex, max(gradeIndex, priceIndex))) {
        continue; // Skip invalid rows
      }

      final product = columns[productIndex].trim();
      final grade = columns[gradeIndex].trim();
      final priceStr = columns[priceIndex].trim().replaceAll(',', '');
      final price = double.tryParse(priceStr) ?? 0.0;

      // Get location information
      double? lat;
      double? lng;
      String locationName = '';

      // Get location name if available
      if (finalLocationNameIndex >= 0 && finalLocationNameIndex < columns.length) {
        locationName = columns[finalLocationNameIndex].trim();
      } else if (columns.length >= 5) {
        // Fallback: try to get location from the last column
        locationName = columns[columns.length - 1].trim();
      }

      // Special handling for the specific format in your CSV
      if (columns.length > 3) {
        // Try to parse the 4th column (index 3) which might contain lat/lng
        final latLngCandidate = columns[3].trim();

        // Check if it matches the pattern of two numbers separated by comma
        final latLngParts = latLngCandidate.split(',').map((s) => s.trim()).toList();
        if (latLngParts.length == 2) {
          lat = double.tryParse(latLngParts[0]);
          lng = double.tryParse(latLngParts[1]);
        }
      }

      // If the above didn't work, try the original methods
      if (lat == null || lng == null) {
        if (latLongIndex >= 0 && latLongIndex < columns.length) {
          // Parse combined lat,long - handle potential spaces
          final latLongStr = columns[latLongIndex].trim();
          final latLngParts = latLongStr.split(',').map((s) => s.trim()).toList();
          if (latLngParts.length == 2) {
            lat = double.tryParse(latLngParts[0].trim());
            lng = double.tryParse(latLngParts[1].trim());
          }
        } else if (latIndex >= 0 && longIndex >= 0 && latIndex < columns.length && longIndex < columns.length) {
          // Parse separate lat and long
          lat = double.tryParse(columns[latIndex].trim());
          lng = double.tryParse(columns[longIndex].trim());
        }
      }

      // Skip rows without valid coordinates
      if (lat == null || lng == null) {
        continue;
      }

      // Create a unique key for these coordinates
      final coordKey = '${lat.toStringAsFixed(6)},${lng.toStringAsFixed(6)}';
      
      // Add to rows by coordinates
      if (!rowsByCoordinates.containsKey(coordKey)) {
        rowsByCoordinates[coordKey] = [];
      }

      rowsByCoordinates[coordKey]!.add({
        'product': product,
        'grade': grade,
        'price': price,
        'lat': lat,
        'lng': lng,
        'locationName': locationName, // Store location name
      });
    }

    // For each unique coordinate, create price documents without creating sites
    for (final coordKey in rowsByCoordinates.keys) {
      final locationRows = rowsByCoordinates[coordKey]!;
      final firstRow = locationRows.first;
      final lat = firstRow['lat'] as double;
      final lng = firstRow['lng'] as double;
      final locationName = firstRow['locationName'] as String? ?? 'Unknown Location';

      // Group rows by product
      final Map<String, List<Map<String, dynamic>>> rowsByProduct = {};

      for (final row in locationRows) {
        final product = row['product'] as String;

        if (!rowsByProduct.containsKey(product)) {
          rowsByProduct[product] = [];
        }

        rowsByProduct[product]!.add(row);
      }

      // Create price documents for each product at this location
      for (final product in rowsByProduct.keys) {
        final productRows = rowsByProduct[product]!;
        
        // Collect all subtypes for this product
        final List<Map<String, dynamic>> typesList = [];
        
        for (final row in productRows) {
          final grade = row['grade'] as String;
          final price = row['price'] as double;
          
          typesList.add({
            'subtypeId': subtypeRefs[product]![grade]!.id,
            'name': grade,
            'price': price,
          });
        }

        // Get a proper address from the coordinates
        String formattedAddress = await getAddressFromLatLng(lat, lng);
        
        // Use the location name from CSV if available, otherwise use a simplified address
        final displayLocation = locationName.isNotEmpty && locationName != 'Unknown Location' 
            ? locationName 
            : formattedAddress.split(',').take(2).join(', ');

        // Create price document with lat/lng instead of siteId
        final priceRef = firestore.collection('prices').doc();
        final metalRef = metalRefs[product]!;

        await priceRef.set({
          'id': priceRef.id,
          'metalId': metalRef.id,
          'metal': product,
          'category': category,
          'latLng': GeoPoint(lat, lng),
          'locationName': displayLocation,
          'fullAddress': formattedAddress,
          'types': typesList,
          'createdAt': FieldValue.serverTimestamp(),
          'lastUpdated': FieldValue.serverTimestamp(),
        });
      }
    }
  }

  // Helper function to parse CSV row properly (handling quoted fields)
  List<String> parseCSVRow(String row) {
    final List<String> result = [];
    bool inQuotes = false;
    String currentField = '';

    for (int i = 0; i < row.length; i++) {
      final char = row[i];

      if (char == '"') {
        inQuotes = !inQuotes;
      } else if (char == ',' && !inQuotes) {
        result.add(currentField);
        currentField = '';
      } else {
        currentField += char;
      }
    }

    // Add the last field
    result.add(currentField);
    return result;
  }

  // Add this method to get address from lat/lng
  Future<String> getAddressFromLatLng(double lat, double lng) async {
    try {
      const apiKey = FFAppConstants.googleMapsKeyWeb;
      final url = 'https://maps.googleapis.com/maps/api/geocode/json?latlng=$lat,$lng&key=$apiKey';

      final response = await http.get(Uri.parse(url));
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['status'] == 'OK' && data['results'] != null && data['results'].isNotEmpty) {
          // Get the formatted address from the first result
          return data['results'][0]['formatted_address'] ?? 'Unknown Location';
        }
      }
      return 'Unknown Location';
    } catch (e) {
      print('Error getting address: $e');
      return 'Unknown Location';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      key: scaffoldKey,
      backgroundColor: FlutterFlowTheme.of(context).primaryBackground,
      body: SafeArea(
        top: true,
        child: Row(
          mainAxisSize: MainAxisSize.max,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SideMenuWidget(selectedItem: MenuItem.pricing),
            Expanded(
              child: Container(
                width: double.infinity,
                height: double.infinity,
                constraints: const BoxConstraints(
                  maxWidth: 1370.0,
                ),
                decoration: BoxDecoration(
                  color: FlutterFlowTheme.of(context).primaryBackground,
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.max,
                  children: [
                    // Header Section
                    Padding(
                      padding: const EdgeInsetsDirectional.fromSTEB(16.0, 26.0, 16.0, 15.0),
                      child: Row(
                        mainAxisSize: MainAxisSize.max,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                            child: Column(
                              mainAxisSize: MainAxisSize.max,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Pricing Management',
                                  style: FlutterFlowTheme.of(context).bodyMedium.override(
                                        fontFamily: 'Open Sans',
                                        color: FlutterFlowTheme.of(context).blackColor,
                                        fontSize: 26.0,
                                        letterSpacing: 0.0,
                                        fontWeight: FontWeight.w600,
                                      ),
                                ),
                              ],
                            ),
                          ),
                          FFButtonWidget(
                            onPressed: () {
                              importFromCSV();
                            },
                            text: 'Import from CSV',
                            options: FFButtonOptions(
                              height: 40.0,
                              padding: const EdgeInsetsDirectional.fromSTEB(16.0, 0.0, 16.0, 0.0),
                              iconPadding: const EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 0.0, 0.0),
                              color: FlutterFlowTheme.of(context).primary,
                              textStyle: FlutterFlowTheme.of(context).titleSmall.override(
                                    fontFamily: 'Open Sans',
                                    color: Colors.white,
                                    letterSpacing: 0.0,
                                    fontWeight: FontWeight.bold,
                                  ),
                              elevation: 0.0,
                              borderRadius: BorderRadius.circular(10.0),
                            ),
                          ),
                          const SizedBox(width: 10),
                          StreamBuilder<void>(
                            stream: _priceController.getStream,
                            builder: (context, _) {
                              return FutureBuilder<bool>(
                                future: checkAdminSiteHasPrice(),
                                builder: (context, snapshot) {
                                  final bool isSuperAdmin = currentUserDocument?.role == Role.superAdmin;

                                  if (isSuperAdmin) {
                                    return FFButtonWidget(
                                      onPressed: navigateToPricingDetails,
                                      text: 'Add New Price',
                                      options: FFButtonOptions(
                                        height: 40.0,
                                        padding: const EdgeInsetsDirectional.fromSTEB(16.0, 0.0, 16.0, 0.0),
                                        iconPadding: const EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 0.0, 0.0),
                                        color: FlutterFlowTheme.of(context).secondary,
                                        textStyle: FlutterFlowTheme.of(context).titleSmall.override(
                                              fontFamily: 'Open Sans',
                                              color: Colors.white,
                                              letterSpacing: 0.0,
                                              fontWeight: FontWeight.bold,
                                            ),
                                        elevation: 0.0,
                                        borderRadius: BorderRadius.circular(10.0),
                                      ),
                                    );
                                  }

                                  return const SizedBox.shrink();
                                },
                              );
                            },
                          ),
                        ],
                      ),
                    ),
                    // Search and Filter Section
                    Padding(
                      padding: const EdgeInsetsDirectional.fromSTEB(16.0, 0.0, 16.0, 16.0),
                      child:
                          // Row(
                          //   children: [
                          // Search Field
                          TextFormField(
                        controller: _model.searchController,
                        focusNode: _model.searchFocusNode,
                        onChanged: (value) {
                          setState(() {
                            // Trigger rebuild immediately
                          });
                        },
                        decoration: InputDecoration(
                          hintText: 'Search by metal...',
                          isDense: false,
                          filled: true,
                          fillColor: Colors.white,
                          prefixIcon: Icon(
                            Icons.search_rounded,
                            color: FlutterFlowTheme.of(context).secondaryText,
                            size: 20.0,
                          ),
                          suffixIcon: _model.searchController.text.isNotEmpty
                              ? IconButton(
                                  icon: Icon(
                                    Icons.clear,
                                    color: FlutterFlowTheme.of(context).secondaryText,
                                    size: 20.0,
                                  ),
                                  onPressed: () {
                                    _model.searchController.clear();
                                    setState(() {});
                                  },
                                )
                              : null,
                          enabledBorder: OutlineInputBorder(
                            borderSide: BorderSide(
                              color: FlutterFlowTheme.of(context).alternate,
                              width: 1.0,
                            ),
                            borderRadius: BorderRadius.circular(10.0),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderSide: BorderSide(
                              color: FlutterFlowTheme.of(context).primary,
                              // width: 2.0,
                            ),
                            borderRadius: BorderRadius.circular(10.0),
                          ),
                          contentPadding: const EdgeInsetsDirectional.fromSTEB(20, 0, 0, 0),
                        ),
                      ),

                      // const SizedBox(width: 16),
                      // Filter Dropdown
                      // Container(
                      //   width: 200,
                      //   height: 45,
                      //   decoration: BoxDecoration(
                      //     color: FlutterFlowTheme.of(context).secondaryBackground,
                      //     borderRadius: BorderRadius.circular(8),
                      //     border: Border.all(
                      //       color: FlutterFlowTheme.of(context).alternate,
                      //     ),
                      //   ),
                      //   child: DropdownButtonHideUnderline(
                      //     child: ButtonTheme(
                      //       alignedDropdown: true,
                      //       child: DropdownButton<String>(
                      //         value: _model.selectedFilter,
                      //         items: ['All', 'Ferrous', 'Non Ferrous']
                      //             .map((String value) => DropdownMenuItem<String>(
                      //                   value: value,
                      //                   child: Text(
                      //                     value,
                      //                     style: FlutterFlowTheme.of(context).bodyMedium,
                      //                   ),
                      //                 ))
                      //             .toList(),
                      //         onChanged: (String? newValue) {
                      //           if (newValue != null) {
                      //             setState(() {
                      //               _model.selectedFilter = newValue;
                      //             });
                      //           }
                      //         },
                      //         icon: Icon(
                      //           Icons.keyboard_arrow_down_rounded,
                      //           color: FlutterFlowTheme.of(context).secondaryText,
                      //           size: 24,
                      //         ),
                      //         isExpanded: true,
                      //         dropdownColor: FlutterFlowTheme.of(context).primaryBackground,
                      //         borderRadius: BorderRadius.circular(8),
                      //         padding: const EdgeInsetsDirectional.fromSTEB(16.0, 0.0, 16.0, 0.0),
                      //       ),
                      //     ),
                      //   ),
                      // ),
                      //   ],
                      // ),
                    ),
                    // Main Content Area
                    Expanded(
                      child: Padding(
                        padding: const EdgeInsetsDirectional.fromSTEB(16.0, 0.0, 16.0, 16.0),
                        child: Container(
                          width: double.infinity,
                          height: double.infinity,
                          decoration: BoxDecoration(
                            color: FlutterFlowTheme.of(context).secondaryBackground,
                            border: Border.all(
                              color: FlutterFlowTheme.of(context).alternate,
                              width: 1.0,
                            ),
                            borderRadius: BorderRadius.circular(12.0),
                            boxShadow: [
                              BoxShadow(
                                color: FlutterFlowTheme.of(context).alternate.withOpacity(0.3),
                                offset: const Offset(0, 1),
                                blurRadius: 3,
                              ),
                            ],
                          ),
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(12.0),
                            child: Column(
                              children: [
                                // Table Header
                                Container(
                                  margin: const EdgeInsetsDirectional.fromSTEB(16.0, 12.0, 16.0, 6.0),
                                  decoration: BoxDecoration(
                                    color: FlutterFlowTheme.of(context).primaryBackground,
                                    borderRadius: const BorderRadius.only(
                                      topLeft: Radius.circular(8.0),
                                      topRight: Radius.circular(8.0),
                                    ),
                                  ),
                                  child: Padding(
                                    padding: const EdgeInsetsDirectional.fromSTEB(16.0, 12.0, 16.0, 12.0),
                                    child: Row(
                                      mainAxisSize: MainAxisSize.max,
                                      children: [
                                        Expanded(
                                          flex: 2,
                                          child: Text(
                                            'Location',
                                            style: FlutterFlowTheme.of(context).labelSmall.override(
                                                  fontFamily: 'Open Sans',
                                                  letterSpacing: 0.0,
                                                  fontWeight: FontWeight.w600,
                                                ),
                                          ),
                                        ),
                                                            const SizedBox(width: 20.0),

                                        Expanded(
                                          flex: 1,
                                          child: Text(
                                            'Category',
                                            style: FlutterFlowTheme.of(context).labelSmall.override(
                                                  fontFamily: 'Open Sans',
                                                  letterSpacing: 0.0,
                                                  fontWeight: FontWeight.w600,
                                                ),
                                          ),
                                        ),
                                        Expanded(
                                          flex: 2,
                                          child: Text(
                                            'Type',
                                            style: FlutterFlowTheme.of(context).labelSmall.override(
                                                  fontFamily: 'Open Sans',
                                                  letterSpacing: 0.0,
                                                  fontWeight: FontWeight.w600,
                                                ),
                                          ),
                                        ),
                                        Expanded(
                                          flex: 2,
                                          child: Text(
                                            'Last Updated',
                                            style: FlutterFlowTheme.of(context).labelSmall.override(
                                                  fontFamily: 'Open Sans',
                                                  letterSpacing: 0.0,
                                                  fontWeight: FontWeight.w600,
                                                ),
                                          ),
                                        ),
                                        currentUserDocument?.role == Role.superAdmin
                                            ? SizedBox(
                                                width: 70,
                                                child: Text(
                                                  'Actions',
                                                  style: FlutterFlowTheme.of(context).labelSmall.override(
                                                        fontFamily: 'Open Sans',
                                                        letterSpacing: 0.0,
                                                        fontWeight: FontWeight.w600,
                                                      ),
                                                ),
                                              )
                                            : const SizedBox.shrink(),
                                      ],
                                    ),
                                  ),
                                ),
                                // Pricing List
                                Expanded(
                                  child: SizedBox(
                                    width: double.infinity,
                                    height: double.infinity,
                                    child: buildPricesList(),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
