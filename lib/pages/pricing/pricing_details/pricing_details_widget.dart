import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:unimetals_admin/auth/firebase_auth/auth_util.dart';
import 'package:unimetals_admin/backend/schema/enums/enums.dart';
import 'package:unimetals_admin/flutter_flow/flutter_flow_theme.dart';
import 'package:unimetals_admin/flutter_flow/flutter_flow_widgets.dart';

class PricingDetailsWidget extends StatefulWidget {
  const PricingDetailsWidget({
    super.key,
  });

  static String routeName = 'pricing_details';
  static String routePath = '/pricing-details'; // Make sure this is the correct path

  @override
  _PricingDetailsWidgetState createState() => _PricingDetailsWidgetState();
}

class _PricingDetailsWidgetState extends State<PricingDetailsWidget> {
  final _formKey = GlobalKey<FormState>();

  late TextEditingController _typeController;
  late TextEditingController _priceController;
  late TextEditingController _locationController;
  late TextEditingController _subTypeController;
  late TextEditingController _subPriceController;
  late TextEditingController _fillAllPriceController; // Controller for "fill all" subtype price input
  late TextEditingController _fillAllLocationPriceController; // Controller for "fill all" location price input
  String _selectedCategory = 'Ferrous';
  String _selectedLocation = '';
  final List<Map<String, dynamic>> _pricesList = [];
  List<Map<String, dynamic>> _subTypesList = [];
  bool isSuperAdmin = false;
  List<String> _locations = [];
  String? _priceId;
  int? _editingIndex;
  bool get isEditing => _editingIndex != null;
  final Map<String, double> _locationPrices = {};
  // Modified to store subtype prices per location
  final Map<String, Map<String, double>> _subTypePricesByLocation = {};
  // Track which subtypes have all fields filled
  final Map<int, bool> _filledSubtypes = {};
  // Flag to prevent rebuilds during text entry
  bool _isUpdatingPrice = false;
  // Track if location prices have been filled
  bool _locationPricesFilled = false;
  RxBool showLoader = false.obs;

  @override
  void initState() {
    super.initState();
    // Set loader to true at the beginning
    print('Is editing: $isEditing');
    showLoader.value = true;

    // Initialize controllers
    _typeController = TextEditingController();
    _priceController = TextEditingController();
    _locationController = TextEditingController();
    _subTypeController = TextEditingController();
    _subPriceController = TextEditingController();
    _fillAllPriceController = TextEditingController();
    _fillAllLocationPriceController = TextEditingController();

    // Check user role
    isSuperAdmin = currentUserDocument?.role == Role.superAdmin;

    // Check if we're editing an existing price
    final args = Get.arguments as Map<String, dynamic>?;
    if (args != null) {
      _priceId = args['id'];

      // Check if we're in edit mode
      if (args['isEdit'] == true) {
        // Set editing mode if isEdit is true
        // Using the existing _editingIndex to track editing state
        _editingIndex = 0; // Initialize with a dummy value to indicate editing mode
      }
    }

    // Load data in sequence: first locations, then price data
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      try {
        // First load locations
        await loadLocations();

        // Then load price data
        final args = Get.arguments as Map<String, dynamic>?;
        if (args != null) {
          // If we have a metalId, load the price data
          if (args['metalId'] != null) {
            await _loadPriceByMetalId(args['metalId']);
          } else if (_priceId != null) {
            await _loadPrice();
          }
        }
      } catch (e) {
        // Show error message
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error loading data: ${e.toString()}'),
              backgroundColor: FlutterFlowTheme.of(context).error,
            ),
          );
        }
      } finally {
        // Set loader to false when done, regardless of success or failure
        showLoader.value = false;
      }
    });
  }

  Future<void> _loadPriceByMetalId(String metalId) async {
    try {
      // Query prices collection for all documents with this metalId
      final priceQuery = await FirebaseFirestore.instance.collection('prices').where('metalId', isEqualTo: metalId).get();

      if (priceQuery.docs.isEmpty) {
        return;
      }

      // Get the first price document for basic info
      final firstPriceDoc = priceQuery.docs.first;
      _priceId = firstPriceDoc.id;
      print('Price ID: $_priceId');
      final firstData = firstPriceDoc.data();

      // Set basic info from the first document
      setState(() {
        _typeController.text = firstData['metal'] ?? '';
        _selectedCategory = firstData['category'] ?? 'Ferrous';

        // For Non-Ferrous, set the base price
        if (_selectedCategory == 'Non Ferrous') {
          _priceController.text = (firstData['basePrice'] ?? firstData['price'] ?? 0.0).toString();
          _selectedLocation = 'Global (All Locations)';
        }
      });

      // For Ferrous metals, we need to load all locations
      if (_selectedCategory == 'Ferrous' || _selectedCategory == 'ELV') {
        setState(() {
          // Process each price document (one per location)
          for (final priceDoc in priceQuery.docs) {
            final data = priceDoc.data();
            // Get location information
            final locationName = data['locationName'] as String? ?? 'Unknown Location';
            final fullAddress = data['fullAddress'] as String? ?? locationName;
            
            print('Location: $locationName, Address: $fullAddress');
            
            // Add location to list if not already there
            if (!_locations.contains(fullAddress)) {
              _locations.add(fullAddress);
            }

            // Set price for this location
            _locationPrices[fullAddress] = data['price'] ?? 0.0;

            // Set selected location if not already set
            if (_selectedLocation.isEmpty) {
              _selectedLocation = fullAddress;
            }
          }
        });

        // Now load subtypes with prices for each location
        final allSubtypes = <String, Map<String, double>>{};

        // First, collect all subtypes across all locations
        for (final priceDoc in priceQuery.docs) {
          final data = priceDoc.data();
          final locationName = data['locationName'] as String? ?? 'Unknown Location';
          final fullAddress = data['fullAddress'] as String? ?? locationName;
          final types = data['types'] as List<dynamic>? ?? [];

          for (final type in types) {
            final subtypeName = type['name'] as String;
            final subtypePrice = type['price'] ?? 0.0;

            if (!allSubtypes.containsKey(subtypeName)) {
              allSubtypes[subtypeName] = {};
            }

            allSubtypes[subtypeName]![fullAddress] = subtypePrice is double ? 
                subtypePrice : (subtypePrice != null ? double.tryParse(subtypePrice.toString()) ?? 0.0 : 0.0);
          }
        }

        // Now create the subtype list with prices for all locations
        setState(() {
          _subTypesList = allSubtypes.entries.map((entry) {
            final subtypeName = entry.key;
            final originalPricesMap = entry.value;

            // Create a new map to avoid reference sharing
            final pricesMap = Map<String, double>.from(originalPricesMap);

            // Make sure all locations have a price (even if it's 0.0)
            for (final location in _locations) {
              if (!pricesMap.containsKey(location)) {
                pricesMap[location] = 0.0;
              }
            }

            return {
              'type': subtypeName,
              'prices': pricesMap,
            };
          }).toList();
        });
      } else {
        // For Non-Ferrous, load subtypes with global prices
        final types = firstData['types'] as List<dynamic>? ?? [];
        setState(() {
          _subTypesList = types.map((type) {
            return {
              'type': type['name'],
              'price': type['price'] ?? 0.0,
            };
          }).toList();
        });
      }
    } catch (e) {
      print('Error loading price by metal ID: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading price data: ${e.toString()}'),
            backgroundColor: FlutterFlowTheme.of(context).error,
          ),
        );
      }
    }
  }

  Future<void> _loadPrice() async {
    try {
      final priceDoc = await FirebaseFirestore.instance.collection('prices').doc(_priceId).get();
      if (!priceDoc.exists) {
        return;
      }

      final data = priceDoc.data()!;

      // Get location information from the price document
      final locationName = data['locationName'] as String? ?? '';
      final fullAddress = data['fullAddress'] as String? ?? locationName;
      final displayLocation = fullAddress.isNotEmpty ? fullAddress : locationName;
      
      // Add location to list if not already there
      if (displayLocation.isNotEmpty && !_locations.contains(displayLocation)) {
        setState(() {
          _locations.add(displayLocation);
        });
      }

      setState(() {
        _typeController.text = data['metal'] ?? '';
        _selectedCategory = data['category'] ?? 'Ferrous';

        // Set selected location from the price document
        if ((_selectedCategory == 'Ferrous' || _selectedCategory == 'ELV') && displayLocation.isNotEmpty) {
          _selectedLocation = displayLocation;
        } else if (_selectedCategory == 'Non Ferrous') {
          _selectedLocation = 'Global (All Locations)';
        } else if (_locations.isNotEmpty) {
          _selectedLocation = _locations.first;
        }

        // For Non-Ferrous, set the base price
        if (_selectedCategory == 'Non Ferrous') {
          _priceController.text = (data['basePrice'] ?? data['price'] ?? 0.0).toString();
        }

        // Load subtypes
        final types = data['types'] as List<dynamic>? ?? [];
        if (_selectedCategory == 'Non Ferrous') {
          _subTypesList = types.map((type) {
            return {
              'type': type['name'],
              'price': type['price'],
            };
          }).toList();
        } else {
          // For Ferrous, create a prices map for each location
          _subTypesList = types.map((type) {
            // For Ferrous, create a prices map for each location
            final Map<String, double> pricesMap = {};

            // Add price for all available locations
            for (final location in _locations) {
              // For the current location, use the price from the document
              if (location == displayLocation) {
                pricesMap[location] = type['price'] ?? 0.0;
              } else {
                // For other locations, initialize with 0.0
                pricesMap[location] = 0.0;
              }
            }

            return {
              'type': type['name'],
              'prices': pricesMap,
            };
          }).toList();

          // For Ferrous, set the location prices for all locations
          // Clear existing location prices
          _locationPrices.clear();

          // Set price for all locations
          for (final location in _locations) {
            // For the current location, use the price from the document
            if (location == displayLocation) {
              _locationPrices[location] = data['price'] ?? 0.0;
            } else {
              // For other locations, initialize with 0.0
              _locationPrices[location] = 0.0;
            }
          }
        }
      });
    } catch (e) {
      print('Error loading price: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading price data: ${e.toString()}'),
            backgroundColor: FlutterFlowTheme.of(context).error,
          ),
        );
      }
    }
  }

  Future<void> loadLocations() async {
    try {
      // First check if current user is admin
      final currentUser = currentUserDocument;
      if (currentUser?.role == Role.admin) {
        // For admin, only show their assigned locations
        final priceQuery = await FirebaseFirestore.instance
            .collection('prices')
            .where('adminId', isEqualTo: currentUser?.uid)
            .get();

        if (priceQuery.docs.isNotEmpty) {
          final locations = priceQuery.docs
              .map((doc) => doc.data()['fullAddress'] as String?)
              .where((location) => location != null && location.isNotEmpty)
              .toSet()
              .toList();
              
          setState(() {
            _locations = locations.cast<String>();
            if (_selectedLocation.isEmpty && _locations.isNotEmpty) {
              _selectedLocation = _locations.first;
            }
          });
        }
        return;
      }

      // For super admin or if we have a metalId
      if (_priceId != null || Get.arguments?['metalId'] != null) {
        // If we have a metalId or priceId, we'll load locations from the price documents
        // This is handled in _loadPrice or _loadPriceByMetalId
        return;
      }

      // For new prices, load all available locations from sites collection
      final sitesQuery = await FirebaseFirestore.instance.collection('sites').get();
      
      final locations = sitesQuery.docs
          .map((doc) => doc.data()['siteLocation'] as String?)
          .where((location) => location != null && location.isNotEmpty)
          .toSet() // Use Set to get unique locations
          .toList();

      setState(() {
        _locations = locations.cast<String>();
        // Set initial location if not already set
        if (_selectedLocation.isEmpty) {
          if (_selectedCategory == 'Non Ferrous') {
            _selectedLocation = 'Global (All Locations)';
          } else if (_locations.isNotEmpty) {
            _selectedLocation = _locations.first;
          } else {
            // If no locations available for Ferrous category
            _selectedLocation = '';
          }
        }
      });
    } catch (e) {
      print('Error loading locations: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'Error loading locations: ${e.toString()}',
            style: TextStyle(
              color: FlutterFlowTheme.of(context).info,
            ),
          ),
          backgroundColor: FlutterFlowTheme.of(context).error,
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }

  Future<void> checkUserRole() async {
    final userDoc = currentUserDocument;
    setState(() {
      isSuperAdmin = userDoc?.role == Role.superAdmin;
    });
  }

  void _addSubType() {
    if (_subTypeController.text.isNotEmpty) {
      setState(() {
        if (_selectedCategory == 'Non Ferrous') {
          // For Non Ferrous: single price
          final price = double.tryParse(_subPriceController.text) ?? 0.0;
          final newSubType = {
            'type': _subTypeController.text,
            'price': price,
          };
          _subTypesList.add(newSubType);
        } else {
          // For Ferrous: location-specific prices with empty initial values
          final Map<String, double?> pricesMap = {};
          for (final location in _locations) {
            pricesMap[location] = null;
          }

          final newSubType = {
            'type': _subTypeController.text,
            'prices': pricesMap,
          };
          _subTypesList.add(newSubType);
        }
        _subTypeController.clear();
        _subPriceController.clear();
      });
    }
  }

  void _removeSubType(int index) async {
    if (index < 0 || index >= _subTypesList.length) return;

    // Get the subtype to be removed
    final subType = _subTypesList[index];
    final subtypeName = subType['type'] as String;

    // Show confirmation dialog

    try {
      // Get the metalId
      String metalId;
      if (_priceId != null) {
        // If we're editing an existing price, get the metalId from it
        final priceDoc = await FirebaseFirestore.instance.collection('prices').doc(_priceId).get();
        metalId = priceDoc.data()?['metalId'] ?? '';
      } else {
        // If we're creating a new price, we need to find the metalId
        final metalQuery = await FirebaseFirestore.instance.collection('metals').where('type', isEqualTo: _typeController.text).where('category', isEqualTo: _selectedCategory).get();

        if (metalQuery.docs.isEmpty) {
          // No metal found, just remove from UI
          setState(() {
            _subTypesList.removeAt(index);
          });
          if (mounted) {
            Navigator.of(context, rootNavigator: true).pop(); // Close loading
          }
          return;
        }

        metalId = metalQuery.docs.first.id;
      }

      // 1. Find and delete the subtype document in the subtypes collection
      final subtypesRef = FirebaseFirestore.instance.collection('metals').doc(metalId).collection('subtypes');
      final subtypeQuery = await subtypesRef.where('name', isEqualTo: subtypeName).get();

      String? subtypeId;
      if (subtypeQuery.docs.isNotEmpty) {
        subtypeId = subtypeQuery.docs.first.id;
        // Delete the subtype document
        await subtypeQuery.docs.first.reference.delete();
      }

      // 2. Update all price documents for this metal to remove the subtype
      final pricesQuery = await FirebaseFirestore.instance.collection('prices').where('metalId', isEqualTo: metalId).get();

      final batch = FirebaseFirestore.instance.batch();

      for (final priceDoc in pricesQuery.docs) {
        final priceData = priceDoc.data();
        final types = List<dynamic>.from(priceData['types'] ?? []);

        // Remove the subtype from the types array
        final updatedTypes = types.where((type) => type['name'] != subtypeName && (subtypeId == null || type['subtypeId'] != subtypeId)).toList();

        // Update the price document with the filtered types array
        batch.update(priceDoc.reference, {
          'types': updatedTypes,
          'lastUpdated': FieldValue.serverTimestamp(),
        });
      }

      // Commit all the updates
      await batch.commit();

      // Remove from UI
      if (mounted) {
        setState(() {
          _subTypesList.removeAt(index);
        });

        // Close loading dialog
        Navigator.of(context, rootNavigator: true).pop();

        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Grade "$subtypeName" deleted successfully'),
            backgroundColor: FlutterFlowTheme.of(context).success,
          ),
        );
      }
    } catch (e) {
      // Close loading dialog
      if (mounted) {
        Navigator.of(context, rootNavigator: true).pop();

        // Show error message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error deleting grade: ${e.toString()}'),
            backgroundColor: FlutterFlowTheme.of(context).error,
          ),
        );

        // Still remove from UI
        setState(() {
          _subTypesList.removeAt(index);
        });
      }
    }
  }

  void _startEditing(int index) {
    final subType = _subTypesList[index];
    setState(() {
      _editingIndex = index;
      _subTypeController.text = subType['type'];
      // Store the original name for tracking edits
      _subTypesList[index] = {
        ...subType,
        'originalName': subType['type'],
      };

      if (_selectedCategory == 'Non Ferrous') {
        _subPriceController.text = (subType['price'] ?? 0.0).toString();
      }
    });

    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16.0),
        ),
        child: Container(
          width: 400.0,
          decoration: BoxDecoration(
            color: FlutterFlowTheme.of(context).secondaryBackground,
            boxShadow: const [
              BoxShadow(
                blurRadius: 4.0,
                color: Color(0x33000000),
                offset: Offset(0.0, 2.0),
                spreadRadius: 0.0,
              )
            ],
            borderRadius: BorderRadius.circular(16.0),
          ),
          child: Padding(
            padding: const EdgeInsetsDirectional.fromSTEB(16.0, 16.0, 16.0, 16.0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'Edit Grade',
                  style: FlutterFlowTheme.of(context).bodyMedium.override(
                        fontFamily: 'Open Sans',
                        color: FlutterFlowTheme.of(context).blackColor,
                        fontSize: 26.0,
                        letterSpacing: 0.0,
                        fontWeight: FontWeight.w600,
                      ),
                ),
                const SizedBox(height: 16.0),
                Padding(
                  padding: const EdgeInsetsDirectional.fromSTEB(16.0, 0.0, 16.0, 0.0),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Padding(
                        padding: const EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 0.0, 4.0),
                        child: Text(
                          'Grade Name',
                          style: FlutterFlowTheme.of(context).bodyMedium,
                        ),
                      ),
                      TextFormField(
                        controller: _subTypeController,
                        decoration: _inputDecoration(''),
                      ),
                    ],
                  ),
                ),
                if (_selectedCategory == 'Non Ferrous') ...[
                  const SizedBox(height: 16.0),
                  Padding(
                    padding: const EdgeInsetsDirectional.fromSTEB(16.0, 0.0, 16.0, 0.0),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                          padding: const EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 0.0, 4.0),
                          child: Text(
                            'Price',
                            style: FlutterFlowTheme.of(context).bodyMedium,
                          ),
                        ),
                        TextFormField(
                          controller: _subPriceController,
                          decoration: _inputDecoration('Enter price'),
                          keyboardType: const TextInputType.numberWithOptions(decimal: true),
                        ),
                      ],
                    ),
                  ),
                ],
                const SizedBox(height: 24.0),
                Padding(
                  padding: const EdgeInsetsDirectional.fromSTEB(16.0, 0.0, 16.0, 0.0),
                  child: Row(
                    mainAxisSize: MainAxisSize.max,
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      FFButtonWidget(
                        onPressed: () {
                          _cancelEditing();
                          Navigator.pop(context);
                        },
                        text: 'Cancel',
                        options: FFButtonOptions(
                          width: 120.0,
                          height: 48.0,
                          padding: const EdgeInsets.all(8.0),
                          iconPadding: const EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 0.0, 0.0),
                          color: FlutterFlowTheme.of(context).secondaryBackground,
                          textStyle: FlutterFlowTheme.of(context).bodyLarge,
                          elevation: 0.0,
                          borderSide: BorderSide(
                            color: FlutterFlowTheme.of(context).alternate,
                            width: 1.0,
                          ),
                          borderRadius: BorderRadius.circular(8.0),
                        ),
                      ),
                      FFButtonWidget(
                        onPressed: () {
                          _updateSubType();
                          Navigator.pop(context);
                        },
                        text: 'Update',
                        options: FFButtonOptions(
                          width: 120.0,
                          height: 48.0,
                          padding: const EdgeInsets.all(8.0),
                          iconPadding: const EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 0.0, 0.0),
                          color: FlutterFlowTheme.of(context).primary,
                          textStyle: FlutterFlowTheme.of(context).titleSmall.override(
                                fontFamily: 'Open Sans',
                                color: Colors.white,
                                letterSpacing: 0.0,
                              ),
                          elevation: 0.0,
                          borderSide: const BorderSide(
                            color: Colors.transparent,
                            width: 1.0,
                          ),
                          borderRadius: BorderRadius.circular(8.0),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _updateSubType() {
    try {
      if (_selectedCategory == 'Non Ferrous') {
        print('In non Ferrous');
        final price = double.tryParse(_subPriceController.text) ?? 0.0;

        setState(() {
          // Get the current subtype to preserve originalName
          final currentSubType = _subTypesList[_editingIndex!];
          final originalName = currentSubType['originalName'];

          // Create a completely new list
          final newList = List<Map<String, dynamic>>.from(_subTypesList);

          // Replace the item at the editing index
          newList[_editingIndex!] = {
            'type': _subTypeController.text,
            'price': price,
            // Preserve the original name if it exists
            if (originalName != null) 'originalName': originalName,
          };

          // Update the state variable with the new list
          _subTypesList = newList;
          _cancelEditing();
        });
      } else {
        print('In Ferrous');
        final currentSubType = _subTypesList[_editingIndex!];
        final originalName = currentSubType['originalName'];
        print('Current subtype: $currentSubType');

        // Create a new list and new map to avoid type issues
        final newList = List<Map<String, dynamic>>.from(_subTypesList);
        final newSubType = <String, dynamic>{
          'type': _subTypeController.text,
          // Preserve the original name if it exists
          if (originalName != null) 'originalName': originalName,
        };

        // Handle prices separately
        if (currentSubType.containsKey('prices')) {
          final newPrices = <String, dynamic>{};
          final oldPrices = currentSubType['prices'];

          if (oldPrices is Map) {
            oldPrices.forEach((key, value) {
              newPrices[key.toString()] = value;
            });
          }

          newSubType['prices'] = newPrices;
        }

        setState(() {
          newList[_editingIndex!] = newSubType;
          _subTypesList = newList;
          _cancelEditing();
        });
      }
    } catch (e) {
      print('Error updating subtype: $e');
      _cancelEditing();
    }
  }

  void _cancelEditing() {
    setState(() {
      _editingIndex = null;
      _subTypeController.clear();
    });
  }

  // Method to apply the same price to all locations for a specific subtype
  void _applyPriceToAllLocations(int subtypeIndex, double price) {
    if (subtypeIndex < 0 || subtypeIndex >= _subTypesList.length) return;

    // First, create a completely new list to force a rebuild of all widgets
    final newSubTypesList = List<Map<String, dynamic>>.from(_subTypesList);
    final subType = Map<String, dynamic>.from(newSubTypesList[subtypeIndex]);

    // Create a new map for prices, ensuring it's initialized
    final Map<String, double> pricesMap = (subType['prices'] as Map<String, dynamic>? ?? {}).map(
      (key, value) => MapEntry(key, value is double ? value : (value != null ? double.tryParse(value.toString()) ?? 0.0 : 0.0)),
    );

    // Update all location prices with the same value
    for (final location in _locations) {
      pricesMap[location] = price;
    }

    // Update the subtype with the new prices
    subType['prices'] = pricesMap;
    newSubTypesList[subtypeIndex] = subType;

    // Update the state with the new list to force a complete rebuild
    setState(() {
      _subTypesList = newSubTypesList;
      _fillAllPriceController.clear(); // Clear the input field
      _filledSubtypes[subtypeIndex] = true;
    });
  }

  // Method to apply the same price to all locations in the location prices section
  void _applyPriceToAllLocationPrices(double price) {
    // Create a new map for prices
    final Map<String, double> pricesMap = {};

    // Update all location prices with the same value
    for (final location in _locations) {
      pricesMap[location] = price;
    }

    // Update the state with the new prices
    setState(() {
      _locationPrices.clear();
      _locationPrices.addAll(pricesMap);
      _fillAllLocationPriceController.clear(); // Clear the input field
      _locationPricesFilled = true; // Set flag to show clear button
    });
  }

  // Method to clear all location prices
  void _clearAllLocationPrices() {
    setState(() {
      _locationPrices.clear();
      _locationPricesFilled = false; // Reset flag to hide clear button
    });
  }

  // Method to clear all prices for a specific subtype
  void _clearAllPrices(int subtypeIndex) {
    if (subtypeIndex < 0 || subtypeIndex >= _subTypesList.length) return;

    // First, create a completely new list to force a rebuild of all widgets
    final newSubTypesList = List<Map<String, dynamic>>.from(_subTypesList);
    final subType = Map<String, dynamic>.from(newSubTypesList[subtypeIndex]);

    // Create a new map for prices with null values
    final Map<String, dynamic> pricesMap = {};

    // Set all location prices to empty string to clear the fields
    for (final location in _locations) {
      pricesMap[location] = '';
    }

    // Update the subtype with the cleared prices
    subType['prices'] = pricesMap;
    newSubTypesList[subtypeIndex] = subType;

    // Update the state with the new list to force a complete rebuild
    setState(() {
      _subTypesList = newSubTypesList;
      _filledSubtypes[subtypeIndex] = false;
    });
  }

  // Add this method to convert subtype data structure
  void _convertSubTypeFormat(String newCategory) {
    if (_subTypesList.isEmpty) return;

    setState(() {
      if (newCategory == 'Ferrous' || newCategory == 'ELV') {
        // Convert from Non Ferrous to Ferrous format
        _subTypesList = _subTypesList.map((subType) {
          // Get the original price from Non Ferrous subtype
          final originalPrice = subType['price'] ?? 0.0;

          // Create a new map for prices
          final Map<String, double> pricesMap = {};

          // Assign the original price to all locations
          for (final location in _locations) {
            pricesMap[location] = originalPrice > 0 ? originalPrice : 0.0;
          }

          return {
            'type': subType['type'],
            'prices': pricesMap,
          };
        }).toList();
      } else {
        // Convert from Ferrous to Non Ferrous format
        _subTypesList = _subTypesList.map((subType) {
          return {
            'type': subType['type'],
            'price': 0.0,
          };
        }).toList();
      }
    });
  }

  // Add this method to help debug price issues
  void _debugPriceState() {
    print('=== DEBUG PRICE STATE ===');
    print('Selected Category: $_selectedCategory');
    print('Selected Location: $_selectedLocation');
    print('All Locations: $_locations');
    print('Location Prices: $_locationPrices');
    print('Location Prices Keys: ${_locationPrices.keys.toList()}');

    print('Subtypes:');
    for (int i = 0; i < _subTypesList.length; i++) {
      final subType = _subTypesList[i];
      print('  Subtype $i: ${subType['type']}');

      if (_selectedCategory == 'Non Ferrous') {
        print('    Price: ${subType['price']}');
      } else {
        print('    Prices:');
        final prices = subType['prices'] as Map<String, dynamic>?;
        if (prices != null) {
          print('    Prices Keys: ${prices.keys.toList()}');
          prices.forEach((location, price) {
            print('      "$location": $price (${price.runtimeType})');
          });
        } else {
          print('      No prices map found!');
        }
      }
    }
    print('========================');
  }

  Future<void> savePrice() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    // Debug the current state
    _debugPriceState();
    
    try {
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(
          child: CircularProgressIndicator(),
        ),
      );

      final firestore = FirebaseFirestore.instance;

      // Check if we're in edit mode
      final bool isEditMode = isEditing || _priceId != null;

      // If we're in edit mode, we don't need to create a new metal
      String metalId;
      if (isEditMode) {
        // Get the metalId from the existing price document
        final priceDoc = await firestore.collection('prices').doc(_priceId).get();
        metalId = priceDoc.data()?['metalId'] ?? '';

        // Update the metal document
        if (metalId.isNotEmpty) {
          await firestore.collection('metals').doc(metalId).update({
            'type': _typeController.text,
            'category': _selectedCategory,
            'updatedAt': FieldValue.serverTimestamp(),
          });
        }
      } else {
        // 1. Save or update metal in metals collection
        final metalsRef = firestore.collection('metals');
        final metalDoc = await metalsRef.where('type', isEqualTo: _typeController.text).where('category', isEqualTo: _selectedCategory).get();

        if (metalDoc.docs.isEmpty) {
          // Create new metal
          final newMetalRef = metalsRef.doc();
          metalId = newMetalRef.id;
          await newMetalRef.set({
            'id': metalId,
            'type': _typeController.text,
            'category': _selectedCategory,
            'createdAt': FieldValue.serverTimestamp(),
            'updatedAt': FieldValue.serverTimestamp(),
          });
        } else {
          metalId = metalDoc.docs.first.id;
          await metalsRef.doc(metalId).update({
            'updatedAt': FieldValue.serverTimestamp(),
          });
        }
      }

      // 2. Save or update subtypes in subcollection
      final subtypesRef = firestore.collection('metals').doc(metalId).collection('subtypes');

      // Get existing subtypes
      final existingSubtypes = await subtypesRef.get();
      final existingSubtypeNames = existingSubtypes.docs.map((doc) => doc.data()['name'] as String).toSet();

      // Map to store subtype IDs for price reference
      final Map<String, String> subtypeIdMap = {};

      // Track original subtype IDs if we're editing
      final Map<String, String> originalSubtypeIds = {};
      if (isEditMode) {
        // Get the existing price document to find original subtype IDs
        final priceDoc = await firestore.collection('prices').doc(_priceId).get();
        final types = priceDoc.data()?['types'] as List<dynamic>? ?? [];

        // Create a map of name -> subtypeId from the existing data
        for (final type in types) {
          final name = type['name'] as String;
          final subtypeId = type['subtypeId'] as String?;
          if (subtypeId != null) {
            originalSubtypeIds[name] = subtypeId;
          }
        }
      }

      // Add new subtypes and collect their IDs
      for (final subType in _subTypesList) {
        final subtypeName = subType['type'] as String;
        final originalName = subType['originalName'] as String?; // Add this field when editing starts

        // Check if this is an edited subtype with a new name
        if (originalName != null && originalName != subtypeName && originalSubtypeIds.containsKey(originalName)) {
          // This is an edited subtype - update the existing one
          final subtypeId = originalSubtypeIds[originalName]!;
          await subtypesRef.doc(subtypeId).update({
            'name': subtypeName,
          });
          subtypeIdMap[subtypeName] = subtypeId;
        }
        // Otherwise handle as before
        else if (!existingSubtypeNames.contains(subtypeName)) {
          // Create new subtype
          final newSubtypeRef = subtypesRef.doc();
          await newSubtypeRef.set({
            'id': newSubtypeRef.id,
            'name': subtypeName,
            'createdAt': FieldValue.serverTimestamp(),
          });
          subtypeIdMap[subtypeName] = newSubtypeRef.id;
        } else {
          // Get existing subtype ID
          final existingDoc = existingSubtypes.docs.firstWhere((doc) => doc.data()['name'] == subtypeName);
          subtypeIdMap[subtypeName] = existingDoc.id;
        }
      }

      // 4. Save price data
      final pricesRef = firestore.collection('prices');

      if (_selectedCategory == 'Ferrous' || _selectedCategory == 'ELV') {
        if (isEditMode) {
          // When editing, update ALL price documents for this metal across all locations
          final allPriceDocsQuery = await firestore.collection('prices').where('metalId', isEqualTo: metalId).get();

          // Create a mapping between database locations and UI locations
          final Map<String, String> dbToUILocationMap = {};

          // Build the mapping by comparing database locations with UI locations
          for (final priceDoc in allPriceDocsQuery.docs) {
            final data = priceDoc.data();
            final locationName = data['locationName'] as String? ?? '';
            final fullAddress = data['fullAddress'] as String? ?? locationName;
            final dbLocation = fullAddress.isNotEmpty ? fullAddress : locationName;

            // Find the best matching UI location
            String? bestMatch;
            for (final uiLocation in _locations) {
              if (uiLocation == dbLocation ||
                  uiLocation == fullAddress ||
                  uiLocation == locationName ||
                  uiLocation.contains(dbLocation) ||
                  dbLocation.contains(uiLocation)) {
                bestMatch = uiLocation;
                break;
              }
            }

            if (bestMatch != null) {
              dbToUILocationMap[dbLocation] = bestMatch;
            }
          }

          print('=== Location Mapping ===');
          print('DB to UI Location Map: $dbToUILocationMap');
          print('UI Locations: $_locations');
          print('Location Prices Keys: ${_locationPrices.keys.toList()}');

          // Use batch to update all documents atomically
          final batch = firestore.batch();

          for (final priceDoc in allPriceDocsQuery.docs) {
            final data = priceDoc.data();

            // Get location information for this specific document
            final locationName = data['locationName'] as String? ?? '';
            final fullAddress = data['fullAddress'] as String? ?? locationName;
            final dbLocation = fullAddress.isNotEmpty ? fullAddress : locationName;

            final siteId = data['siteId'] as String?;
            final latLng = data['latLng'];
            final zipCode = data['zipCode'] as String? ?? '';

            // Get the corresponding UI location
            final uiLocation = dbToUILocationMap[dbLocation] ?? dbLocation;

            print('=== Processing Document ===');
            print('DB Location: $dbLocation');
            print('UI Location: $uiLocation');

            // Prepare subtype prices for this specific location
            final subtypePrices = _subTypesList.map((subType) {
              final subtypeName = subType['type'] as String;

              // Get the price for this specific location
              double subtypePrice = 0.0;
              if (subType.containsKey('prices') && subType['prices'] is Map) {
                final pricesMap = subType['prices'] as Map<String, dynamic>;

                // Use the UI location to get the price
                if (pricesMap.containsKey(uiLocation)) {
                  final price = pricesMap[uiLocation];
                  subtypePrice = price is double ? price : (price != null ? double.tryParse(price.toString()) ?? 0.0 : 0.0);
                  print('Found price for $subtypeName at $uiLocation: $subtypePrice');
                } else {
                  print('No price found for $subtypeName at $uiLocation. Available keys: ${pricesMap.keys.toList()}');
                }
              }

              return {
                'subtypeId': subtypeIdMap[subtypeName],
                'name': subtypeName,
                'price': subtypePrice,
              };
            }).toList();

            // Get the main price for this location
            double mainPrice = 0.0;
            if (_locationPrices.containsKey(uiLocation)) {
              mainPrice = _locationPrices[uiLocation] ?? 0.0;
              print('Found main price for $uiLocation: $mainPrice');
            } else {
              print('No main price found for $uiLocation. Available keys: ${_locationPrices.keys.toList()}');
            }

            final priceData = {
              'metalId': metalId,
              'metal': _typeController.text,
              'category': _selectedCategory,
              'price': mainPrice,
              'types': subtypePrices,
              'lastUpdated': FieldValue.serverTimestamp(),
            };

            // Preserve existing fields
            if (siteId != null) priceData['siteId'] = siteId;
            if (latLng != null) priceData['latLng'] = latLng;
            if (locationName.isNotEmpty) priceData['locationName'] = locationName;
            if (fullAddress.isNotEmpty) priceData['fullAddress'] = fullAddress;
            if (zipCode.isNotEmpty) priceData['zipCode'] = zipCode;

            // Add this document to the batch update
            batch.update(priceDoc.reference, priceData);
          }

          // Commit all updates at once
          await batch.commit();
        } else {
          // For new prices, create separate docs for each site
          final sitesSnapshot = await firestore.collection('sites').get();

          // Create price documents for each site
          for (var siteDoc in sitesSnapshot.docs) {
            final siteId = siteDoc.id;
            final siteLocation = siteDoc.data()['siteLocation'] as String;
            final siteLatLng = siteDoc.data()['siteLatLng'];
            final zipCode = siteDoc.data()['zipCode'] as String? ?? '';

            // Create or update price document for this site
            final existingPriceQuery = await pricesRef.where('metalId', isEqualTo: metalId).where('siteId', isEqualTo: siteId).get();

            final priceDocRef = existingPriceQuery.docs.isNotEmpty ? pricesRef.doc(existingPriceQuery.docs.first.id) : pricesRef.doc();

            // Prepare subtype prices for this specific site
            final subtypePrices = _subTypesList.map((subType) {
              final subtypeName = subType['type'] as String;
              final subtypePrice = subType['prices'][siteLocation] ?? 0.0;

              return {
                'subtypeId': subtypeIdMap[subtypeName],
                'name': subtypeName,
                'price': double.parse((subtypePrice ?? 0.0).toString()),
              };
            }).toList();

            final priceData = {
              'id': priceDocRef.id,
              'metalId': metalId,
              'metal': _typeController.text,
              'category': _selectedCategory,
              'siteId': siteId,
              'latLng': siteLatLng,
              'locationName': siteLocation,
              'fullAddress': siteLocation,
              'zipCode': zipCode,
              'price': _locationPrices[siteLocation] ?? 0.0,
              'types': subtypePrices,
              'lastUpdated': FieldValue.serverTimestamp(),
            };

            if (existingPriceQuery.docs.isEmpty) {
              priceData['createdAt'] = FieldValue.serverTimestamp();
            }

            await priceDocRef.set(priceData, SetOptions(merge: true));
          }
        }
      } else {
        // For Non-Ferrous metals (Global prices)
        final priceDocRef = _priceId != null ? pricesRef.doc(_priceId) : pricesRef.doc();

        // Prepare global subtype prices - KEEP subtype prices
        final subtypePrices = _subTypesList.map((subType) {
          final subtypeName = subType['type'] as String;
          final subtypePrice = subType['price'] ?? 0.0;

          return {
            'subtypeId': subtypeIdMap[subtypeName],
            'name': subtypeName,
            'price': double.parse(subtypePrice.toString()),
          };
        }).toList();

        final priceData = {
          'id': priceDocRef.id,
          'metalId': metalId,
          'metal': _typeController.text,
          'category': _selectedCategory,
          'types': subtypePrices, // Keep subtype prices
          'lastUpdated': FieldValue.serverTimestamp(),
        };

        if (_priceId == null) {
          priceData['createdAt'] = FieldValue.serverTimestamp();
        }

        await priceDocRef.set(priceData, SetOptions(merge: true));
      }

      // Close the loading dialog
      Navigator.of(context, rootNavigator: true).pop();

      // Show success message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('Metal structure saved successfully (with subtype prices)'),
          backgroundColor: FlutterFlowTheme.of(context).success,
        ),
      );

      // Navigate back
      if (mounted) {
        // Wait a moment for the snackbar to be visible
        Future.delayed(const Duration(milliseconds: 1500), () {
          if (mounted) {
            // Navigate back to previous screen
            Get.back();
          }
        });
      }
    } catch (e) {
      // Handle errors
      if (mounted) {
        Navigator.pop(context); // Close loading dialog
      }

      debugPrint('Error in savePrice: $e');
      // Error handling code...
    }
  }

  Widget _buildMainForm() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: FlutterFlowTheme.of(context).secondaryBackground,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: FlutterFlowTheme.of(context).alternate.withAlpha(128),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Metal Details',
            style: FlutterFlowTheme.of(context).headlineSmall.override(
                  fontFamily: 'Open Sans',
                  fontWeight: FontWeight.w600,
                ),
          ),
          const SizedBox(height: 24),
          Row(
            children: [
              // Category Dropdown
              Expanded(
                child: DropdownButtonFormField<String>(
                  value: _selectedCategory,
                  decoration: _inputDecoration('Category'),
                  items: ['Ferrous', 'Non Ferrous', 'ELV'].map((category) {
                    return DropdownMenuItem(
                      value: category,
                      child: Text(category),
                    );
                  }).toList(),
                  onChanged: !isEditing && isSuperAdmin
                      ? (value) {
                          if (value != null) {
                            _convertSubTypeFormat(value);
                            setState(() {
                              _selectedCategory = value;
                              // Clear base price when switching to Ferrous
                              if (value == 'Ferrous' || value == 'ELV') {
                                _priceController.clear();
                              }
                            });
                          }
                        }
                      : null,
                ),
              ),
              const SizedBox(width: 16),
              // Metal Name Input
              Expanded(
                flex: 2,
                child: TextFormField(
                  controller: _typeController,
                  decoration: _inputDecoration('Product Name'),
                  validator: (value) => value?.isEmpty ?? true ? 'Please enter product name' : null,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSubTypes() {
    return Container(
      padding: const EdgeInsets.all(24),
      margin: const EdgeInsets.only(top: 24),
      decoration: BoxDecoration(
        color: FlutterFlowTheme.of(context).secondaryBackground,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: FlutterFlowTheme.of(context).alternate.withAlpha(128),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Grade',
                    style: FlutterFlowTheme.of(context).headlineSmall.override(
                          fontFamily: 'Open Sans',
                          fontWeight: FontWeight.w600,
                        ),
                  ),
                  _selectedCategory == 'Ferrous' || _selectedCategory == 'ELV'
                      ? Padding(
                          padding: const EdgeInsets.only(top: 1),
                          child: Text(
                            '${_locations.length} locations',
                            style: FlutterFlowTheme.of(context).bodySmall.override(
                                  fontFamily: 'Open Sans',
                                  color: FlutterFlowTheme.of(context).secondaryText,
                                  fontStyle: FontStyle.italic,
                                ),
                          ),
                        )
                      : const SizedBox.shrink(),
                ],
              ),
              FFButtonWidget(
                onPressed: () {
                  _subTypeController.clear();
                  _subPriceController.clear();
                  showDialog(
                    context: context,
                    builder: (context) => Dialog(
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(16.0),
                      ),
                      child: Container(
                        width: 400.0,
                        decoration: BoxDecoration(
                          color: FlutterFlowTheme.of(context).secondaryBackground,
                          boxShadow: const [
                            BoxShadow(
                              blurRadius: 4.0,
                              color: Color(0x33000000),
                              offset: Offset(0.0, 2.0),
                              spreadRadius: 0.0,
                            )
                          ],
                          borderRadius: BorderRadius.circular(16.0),
                        ),
                        child: Padding(
                          padding: const EdgeInsetsDirectional.fromSTEB(16.0, 16.0, 16.0, 16.0),
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Text(
                                'Add Grade',
                                style: FlutterFlowTheme.of(context).bodyMedium.override(
                                      fontFamily: 'Open Sans',
                                      color: FlutterFlowTheme.of(context).blackColor,
                                      fontSize: 26.0,
                                      letterSpacing: 0.0,
                                      fontWeight: FontWeight.w600,
                                    ),
                              ),
                              const SizedBox(height: 16.0),
                              Padding(
                                padding: const EdgeInsetsDirectional.fromSTEB(16.0, 0.0, 16.0, 0.0),
                                child: Column(
                                  mainAxisSize: MainAxisSize.min,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Padding(
                                      padding: const EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 0.0, 4.0),
                                      child: Text(
                                        'Grade Name',
                                        style: FlutterFlowTheme.of(context).bodyMedium,
                                      ),
                                    ),
                                    TextFormField(
                                      controller: _subTypeController,
                                      decoration: _inputDecoration(''),
                                    ),
                                  ],
                                ),
                              ),
                              if (_selectedCategory == 'Non Ferrous') ...[
                                const SizedBox(height: 16.0),
                                Padding(
                                  padding: const EdgeInsetsDirectional.fromSTEB(16.0, 0.0, 16.0, 0.0),
                                  child: Column(
                                    mainAxisSize: MainAxisSize.min,
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Padding(
                                        padding: const EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 0.0, 4.0),
                                        child: Text(
                                          'Price',
                                          style: FlutterFlowTheme.of(context).bodyMedium,
                                        ),
                                      ),
                                      TextFormField(
                                        controller: _subPriceController,
                                        decoration: _inputDecoration('Enter price'),
                                        keyboardType: const TextInputType.numberWithOptions(decimal: true),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                              const SizedBox(height: 24.0),
                              Padding(
                                padding: const EdgeInsetsDirectional.fromSTEB(16.0, 0.0, 16.0, 0.0),
                                child: Row(
                                  mainAxisSize: MainAxisSize.max,
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  children: [
                                    FFButtonWidget(
                                      onPressed: () {
                                        Navigator.pop(context);
                                      },
                                      text: 'Cancel',
                                      options: FFButtonOptions(
                                        width: 120.0,
                                        height: 48.0,
                                        padding: const EdgeInsets.all(8.0),
                                        iconPadding: const EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 0.0, 0.0),
                                        color: FlutterFlowTheme.of(context).secondaryBackground,
                                        textStyle: FlutterFlowTheme.of(context).bodyLarge,
                                        elevation: 0.0,
                                        borderSide: BorderSide(
                                          color: FlutterFlowTheme.of(context).alternate,
                                          width: 1.0,
                                        ),
                                        borderRadius: BorderRadius.circular(8.0),
                                      ),
                                    ),
                                    FFButtonWidget(
                                      onPressed: () {
                                        _addSubType();
                                        Navigator.pop(context);
                                      },
                                      text: 'Add',
                                      options: FFButtonOptions(
                                        width: 120.0,
                                        height: 48.0,
                                        padding: const EdgeInsets.all(8.0),
                                        iconPadding: const EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 0.0, 0.0),
                                        color: FlutterFlowTheme.of(context).primary,
                                        textStyle: FlutterFlowTheme.of(context).titleSmall.override(
                                              fontFamily: 'Open Sans',
                                              color: Colors.white,
                                              letterSpacing: 0.0,
                                            ),
                                        elevation: 0.0,
                                        borderSide: const BorderSide(
                                          color: Colors.transparent,
                                          width: 1.0,
                                        ),
                                        borderRadius: BorderRadius.circular(8.0),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  );
                },
                text: 'Add Grade',
                options: FFButtonOptions(
                  height: 40,
                  padding: const EdgeInsetsDirectional.fromSTEB(16, 0, 16, 0),
                  color: FlutterFlowTheme.of(context).primary,
                  textStyle: FlutterFlowTheme.of(context).titleSmall.override(
                        fontFamily: 'Open Sans',
                        color: Colors.white,
                      ),
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ],
          ),
          const SizedBox(height: 24),
          Wrap(
            spacing: 20,
            runSpacing: 20,
            children: List.generate(_subTypesList.length, (index) {
              final subType = _subTypesList[index];
              // Add null checks for the 'type' property
              final typeName = subType['type'] as String? ?? '';

              return Container(
                width: 350,
                decoration: BoxDecoration(
                  color: FlutterFlowTheme.of(context).secondaryBackground,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: FlutterFlowTheme.of(context).alternate,
                    width: 1,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withAlpha(10),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                      spreadRadius: 0,
                    ),
                  ],
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(12),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Header with type name and actions
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                        decoration: BoxDecoration(
                          color: FlutterFlowTheme.of(context).primary.withAlpha(15),
                          border: Border(
                            bottom: BorderSide(
                              color: FlutterFlowTheme.of(context).alternate,
                              width: 1,
                            ),
                          ),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Expanded(
                              child: Text(
                                typeName, // Use the safely extracted value
                                style: FlutterFlowTheme.of(context).titleMedium.override(
                                      fontFamily: 'Open Sans',
                                      fontWeight: FontWeight.w600,
                                      color: FlutterFlowTheme.of(context).primaryText,
                                    ),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            Row(
                              children: [
                                // Edit button
                                Container(
                                  decoration: BoxDecoration(
                                    color: FlutterFlowTheme.of(context).secondaryBackground,
                                    borderRadius: BorderRadius.circular(8),
                                    border: Border.all(
                                      color: FlutterFlowTheme.of(context).alternate,
                                      width: 1,
                                    ),
                                  ),
                                  child: IconButton(
                                    icon: Icon(
                                      Icons.edit_outlined,
                                      size: 20,
                                      color: FlutterFlowTheme.of(context).primary,
                                    ),
                                    onPressed: () => _startEditing(index),
                                    tooltip: 'Edit',
                                    padding: const EdgeInsets.all(8),
                                    constraints: const BoxConstraints(),
                                    splashRadius: 24,
                                  ),
                                ),
                                const SizedBox(width: 8),
                                // Delete button
                                Container(
                                  decoration: BoxDecoration(
                                    color: FlutterFlowTheme.of(context).secondaryBackground,
                                    borderRadius: BorderRadius.circular(8),
                                    border: Border.all(
                                      color: FlutterFlowTheme.of(context).alternate,
                                      width: 1,
                                    ),
                                  ),
                                  child: IconButton(
                                    icon: Icon(
                                      Icons.delete_outline,
                                      size: 20,
                                      color: FlutterFlowTheme.of(context).error,
                                    ),
                                    onPressed: () => _removeSubType(index),
                                    tooltip: 'Delete',
                                    padding: const EdgeInsets.all(8),
                                    constraints: const BoxConstraints(),
                                    splashRadius: 24,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),

                      // Content area
                      Padding(
                        padding: const EdgeInsets.all(0),
                        child: _selectedCategory == 'Non Ferrous' ? _buildNonFerrousPrice(subType) : _buildFerrousPrices(subType, index),
                      ),
                    ],
                  ),
                ),
              );
            }),
          ),
        ],
      ),
    );
  }

  // Helper method to build the price display for Non Ferrous metals
  Widget _buildNonFerrousPrice(Map<String, dynamic> subType) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: FlutterFlowTheme.of(context).secondaryBackground,
        borderRadius: BorderRadius.circular(10),
        border: Border.all(
          color: FlutterFlowTheme.of(context).alternate,
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(5),
            blurRadius: 4,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Base Price',
                style: FlutterFlowTheme.of(context).bodyMedium.override(
                      fontFamily: 'Open Sans',
                      fontWeight: FontWeight.w600,
                      fontSize: 14,
                      color: FlutterFlowTheme.of(context).secondaryText,
                    ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: FlutterFlowTheme.of(context).secondary.withAlpha(30),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  'Non Ferrous',
                  style: FlutterFlowTheme.of(context).bodySmall.override(
                        fontFamily: 'Open Sans',
                        fontWeight: FontWeight.w600,
                        color: FlutterFlowTheme.of(context).secondary,
                      ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: FlutterFlowTheme.of(context).primary.withAlpha(15),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: FlutterFlowTheme.of(context).primary.withAlpha(25),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.attach_money,
                    size: 20,
                    color: FlutterFlowTheme.of(context).primary,
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  '${subType['price']}',
                  style: FlutterFlowTheme.of(context).titleMedium.override(
                        fontFamily: 'Open Sans',
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                        color: FlutterFlowTheme.of(context).primaryText,
                      ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Helper method to build the location prices for Ferrous metals
  Widget _buildFerrousPrices(Map<String, dynamic> subType, int index) {
    // Add null check for the 'prices' map
    final Map<String, dynamic> prices = subType['prices'] as Map<String, dynamic>? ?? {};

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: FlutterFlowTheme.of(context).secondaryBackground,
        borderRadius: BorderRadius.circular(10),
        border: Border.all(
          color: FlutterFlowTheme.of(context).alternate,
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(5),
            blurRadius: 4,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Simple fill all fields row with tick and cross buttons
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
            margin: const EdgeInsets.only(bottom: 8),
            decoration: BoxDecoration(
              color: FlutterFlowTheme.of(context).primary.withAlpha(10),
              borderRadius: BorderRadius.circular(6),
              border: Border.all(
                color: FlutterFlowTheme.of(context).primary.withAlpha(30),
                width: 1,
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.price_change_outlined,
                  size: 14,
                  color: FlutterFlowTheme.of(context).primary,
                ),
                const SizedBox(width: 4),
                Text(
                  'Fill All Fields:',
                  style: FlutterFlowTheme.of(context).bodySmall.override(
                        fontFamily: 'Open Sans',
                        fontWeight: FontWeight.w600,
                        fontSize: 11,
                      ),
                ),
                const SizedBox(width: 4),
                // Price input field
                Expanded(
                  child: SizedBox(
                    height: 28,
                    child: TextField(
                      controller: _fillAllPriceController,
                      keyboardType: const TextInputType.numberWithOptions(decimal: true),
                      style: FlutterFlowTheme.of(context).bodySmall.override(
                            fontFamily: 'Open Sans',
                            fontSize: 11,
                          ),
                      decoration: InputDecoration(
                        hintText: 'Enter price for all locations',
                        hintStyle: FlutterFlowTheme.of(context).bodySmall.override(
                              fontFamily: 'Open Sans',
                              fontSize: 11,
                              color: FlutterFlowTheme.of(context).secondaryText.withAlpha(150),
                            ),
                        isDense: true,
                        enabledBorder: OutlineInputBorder(
                          borderSide: BorderSide(
                            color: FlutterFlowTheme.of(context).alternate,
                            width: 1,
                          ),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderSide: BorderSide(
                            color: FlutterFlowTheme.of(context).primary,
                            width: 1,
                          ),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        filled: true,
                        fillColor: Colors.white,
                        contentPadding: const EdgeInsets.symmetric(horizontal: 6, vertical: 6),
                        prefixIcon: Icon(
                          Icons.attach_money,
                          size: 12,
                          color: FlutterFlowTheme.of(context).primary,
                        ),
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 6),
                // Tick button to apply price to all locations
                InkWell(
                  onTap: () {
                    final priceText = _fillAllPriceController.text.trim();
                    if (priceText.isNotEmpty) {
                      final price = double.tryParse(priceText);
                      if (price != null) {
                        _applyPriceToAllLocations(index, price);
                      }
                    }
                  },
                  child: Container(
                    padding: const EdgeInsets.all(6),
                    decoration: BoxDecoration(
                      color: FlutterFlowTheme.of(context).primary,
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: const Icon(
                      Icons.check,
                      color: Colors.white,
                      size: 16,
                    ),
                  ),
                ),
                // Show cross button only if this subtype has been filled
                if (_filledSubtypes[index] == true) ...[
                  const SizedBox(width: 4),
                  InkWell(
                    onTap: () {
                      _clearAllPrices(index);
                      setState(() {
                        _filledSubtypes[index] = false;
                      });
                    },
                    child: Container(
                      padding: const EdgeInsets.all(6),
                      decoration: BoxDecoration(
                        color: FlutterFlowTheme.of(context).error,
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: const Icon(
                        Icons.close,
                        color: Colors.white,
                        size: 16,
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ),

          // Compact location prices table
          Container(
            decoration: BoxDecoration(
              color: FlutterFlowTheme.of(context).primary.withAlpha(5),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: FlutterFlowTheme.of(context).alternate,
                width: 1,
              ),
            ),
            child: Column(
              children: [
                // Table body - limited height with scrolling
                Container(
                  constraints: const BoxConstraints(maxHeight: 250),
                  child: SingleChildScrollView(
                    child: Column(
                      children: _locations.map((location) {
                        final price = subType['prices'][location];

                        return Container(
                          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                          decoration: BoxDecoration(
                            border: Border(
                              bottom: BorderSide(
                                color: FlutterFlowTheme.of(context).alternate,
                                width: 0.5,
                              ),
                            ),
                          ),
                          child: Row(
                            children: [
                              // Location column
                              Expanded(
                                flex: 3,
                                child: Row(
                                  children: [
                                    Icon(
                                      Icons.location_on_outlined,
                                      size: 14,
                                      color: FlutterFlowTheme.of(context).primary,
                                    ),
                                    const SizedBox(width: 6),
                                    Expanded(
                                      child: Text(
                                        location,
                                        style: FlutterFlowTheme.of(context).bodySmall.override(
                                              fontFamily: 'Open Sans',
                                              fontWeight: FontWeight.w500,
                                              fontSize: 11,
                                            ),
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                    ),
                                  ],
                                ),
                              ),

                              // Price column
                              Expanded(
                                flex: 2,
                                child: Row(
                                  children: [
                                    Expanded(
                                      child: SizedBox(
                                        height: 36,
                                        child: PriceTextField(
                                          initialValue: price != null ? price.toString() : '',
                                          onChanged: (value) {
                                            if (!_isUpdatingPrice) {
                                              _isUpdatingPrice = true;
                                              final newPrice = value.isEmpty ? null : double.tryParse(value) ?? 0.0;

                                              // Create a deep copy of the subtype to avoid reference sharing
                                              final updatedSubType = Map<String, dynamic>.from(subType);

                                              // Create a deep copy of the prices map
                                              final updatedPrices = Map<String, dynamic>.from(subType['prices'] as Map<String, dynamic>? ?? {});
                                              updatedPrices[location] = newPrice;
                                              updatedSubType['prices'] = updatedPrices;

                                              // Update the list with the new subtype
                                              _subTypesList[index] = updatedSubType;
                                              setState(() {});
                                              _isUpdatingPrice = false;
                                            }
                                          },
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        );
                      }).toList(),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  InputDecoration _inputDecoration(String label) {
    return InputDecoration(
      labelText: label,
      labelStyle: FlutterFlowTheme.of(context).bodyMedium,
      enabledBorder: OutlineInputBorder(
        borderSide: BorderSide(
          color: FlutterFlowTheme.of(context).alternate,
          width: 1,
        ),
        borderRadius: BorderRadius.circular(8),
      ),
      focusedBorder: OutlineInputBorder(
        borderSide: BorderSide(
          color: FlutterFlowTheme.of(context).primary,
          width: 2,
        ),
        borderRadius: BorderRadius.circular(8),
      ),
      errorBorder: OutlineInputBorder(
        borderSide: BorderSide(
          color: FlutterFlowTheme.of(context).error,
          width: 1,
        ),
        borderRadius: BorderRadius.circular(8),
      ),
      focusedErrorBorder: OutlineInputBorder(
        borderSide: BorderSide(
          color: FlutterFlowTheme.of(context).error,
          width: 2,
        ),
        borderRadius: BorderRadius.circular(8),
      ),
      filled: true,
      fillColor: FlutterFlowTheme.of(context).secondaryBackground,
      contentPadding: const EdgeInsetsDirectional.fromSTEB(16, 12, 16, 12),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          Container(
            width: double.infinity,
            height: 100.0,
            decoration: BoxDecoration(
              color: FlutterFlowTheme.of(context).primary,
            ),
            child: Row(
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Padding(
                  padding: const EdgeInsetsDirectional.fromSTEB(30.0, 0.0, 0.0, 0.0),
                  child: Text(
                    'Pricing Details',
                    style: FlutterFlowTheme.of(context).bodyMedium.override(
                          fontFamily: 'Open Sans',
                          color: FlutterFlowTheme.of(context).whiteColor,
                          fontSize: 26.0,
                          letterSpacing: 0.0,
                          fontWeight: FontWeight.w600,
                        ),
                  ),
                ),
                Builder(
                    builder: (context) => Padding(
                          padding: const EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 30.0, 0.0),
                          child: FFButtonWidget(
                            onPressed: savePrice,
                            text: 'Save Changes',
                            options: FFButtonOptions(
                              height: 40.0,
                              padding: const EdgeInsetsDirectional.fromSTEB(16.0, 0.0, 16.0, 0.0),
                              iconPadding: const EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 0.0, 0.0),
                              color: FlutterFlowTheme.of(context).secondary,
                              textStyle: FlutterFlowTheme.of(context).titleSmall.override(
                                    fontFamily: 'Open Sans',
                                    color: Colors.white,
                                    letterSpacing: 0.0,
                                    fontWeight: FontWeight.bold,
                                  ),
                              elevation: 0.0,
                              borderRadius: BorderRadius.circular(10.0),
                              disabledColor: FlutterFlowTheme.of(context).alternate,
                            ),
                          ),
                        )),
              ],
            ),
          ),
          Obx(() => showLoader.value
              ? const Expanded(child: Center(child: CircularProgressIndicator()))
              : Expanded(
                  child: Form(
                    key: _formKey,
                    child: SingleChildScrollView(
                      padding: const EdgeInsets.all(24),
                      child: Column(
                        children: [
                          _buildMainForm(),
                          _buildSubTypes(),
                        ],
                      ),
                    ),
                  ),
                )),
        ],
      ),
    );
  }

  @override
  void dispose() {
    // Dispose all controllers to prevent memory leaks
    _typeController.dispose();
    _priceController.dispose();
    _locationController.dispose();
    _subTypeController.dispose();
    _subPriceController.dispose();
    _fillAllPriceController.dispose();
    _fillAllLocationPriceController.dispose();

    super.dispose();
  }
}

// Custom TextField widget that maintains focus when typing
class PriceTextField extends StatefulWidget {
  final String initialValue;
  final Function(String) onChanged;

  const PriceTextField({
    super.key,
    required this.initialValue,
    required this.onChanged,
  });

  @override
  State<PriceTextField> createState() => _PriceTextFieldState();
}

class _PriceTextFieldState extends State<PriceTextField> {
  late TextEditingController _controller;
  final FocusNode _focusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.initialValue);
  }

  @override
  void didUpdateWidget(PriceTextField oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Only update the controller text if the initialValue changed and the field is not focused
    if (widget.initialValue != oldWidget.initialValue && !_focusNode.hasFocus) {
      _controller.text = widget.initialValue;
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      controller: _controller,
      focusNode: _focusNode,
      keyboardType: const TextInputType.numberWithOptions(decimal: true),
      decoration: InputDecoration(
        isDense: true,
        prefixIcon: Icon(
          Icons.attach_money,
          size: 14,
          color: FlutterFlowTheme.of(context).primary,
        ),
        enabledBorder: OutlineInputBorder(
          borderSide: BorderSide(
            color: FlutterFlowTheme.of(context).alternate,
            width: 1,
          ),
          borderRadius: BorderRadius.circular(6),
        ),
        focusedBorder: OutlineInputBorder(
          borderSide: BorderSide(
            color: FlutterFlowTheme.of(context).primary,
            width: 1,
          ),
          borderRadius: BorderRadius.circular(6),
        ),
        filled: true,
        fillColor: FlutterFlowTheme.of(context).secondaryBackground,
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 6,
          vertical: 6,
        ),
      ),
      style: FlutterFlowTheme.of(context).bodySmall.override(
            fontFamily: 'Open Sans',
            fontSize: 12,
          ),
      onChanged: widget.onChanged,
    );
  }
}
