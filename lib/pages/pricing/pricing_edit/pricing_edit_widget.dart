import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:unimetals_admin/auth/firebase_auth/auth_util.dart';
import 'package:unimetals_admin/backend/schema/enums/enums.dart';
import 'package:unimetals_admin/flutter_flow/flutter_flow_theme.dart';
import 'package:unimetals_admin/flutter_flow/flutter_flow_util.dart';
import 'package:unimetals_admin/flutter_flow/flutter_flow_widgets.dart';

class PricingEditWidget extends StatefulWidget {
  const PricingEditWidget({
    super.key,
  });

  static String routeName = 'pricing_edit';
  static String routePath = '/pricing-edit';

  @override
  _PricingEditWidgetState createState() => _PricingEditWidgetState();
}

class _PricingEditWidgetState extends State<PricingEditWidget> {
  final _formKey = GlobalKey<FormState>();

  late TextEditingController _typeController;
  late TextEditingController _priceController;
  late TextEditingController _locationController;
  late TextEditingController _subTypeController;
  late TextEditingController _subPriceController;
  late TextEditingController _fillAllPriceController; // Controller for "fill all" subtype price input
  late TextEditingController
      _fillAllLocationPriceController; // Controller for "fill all" location price input
  String _selectedCategory = 'Ferrous';
  String _selectedLocation = '';
  final List<Map<String, dynamic>> _pricesList = [];
  List<Map<String, dynamic>> _subTypesList = [];
  bool isSuperAdmin = false;
  List<String> _locations = [];
  String? _priceId;
  int? _editingIndex;
  bool get isEditing => _editingIndex != null;
  final Map<String, double> _locationPrices = {};
  // Modified to store subtype prices per location
  final Map<String, Map<String, double>> _subTypePricesByLocation = {};
  // Track which subtypes have all fields filled
  final Map<int, bool> _filledSubtypes = {};
  // Flag to prevent rebuilds during text entry
  bool _isUpdatingPrice = false;
  // Track if location prices have been filled
  bool _locationPricesFilled = false;
  bool _isReadOnly = false; // Add this variable to track read-only mode
  RxBool showLoader = false.obs;

  @override
  void initState() {
    super.initState();
    showLoader.value = true;
    // Initialize controllers
    _typeController = TextEditingController();
    _priceController = TextEditingController();
    _locationController = TextEditingController();
    _subTypeController = TextEditingController();
    _subPriceController = TextEditingController();
    _fillAllPriceController = TextEditingController();
    _fillAllLocationPriceController = TextEditingController();

    // Initialize empty lists and maps
    _subTypesList = [];
    _locationPrices.clear();
    _filledSubtypes.clear();
    _isUpdatingPrice = false;

    // Check if we're editing an existing price
    final args = Get.arguments as Map<String, dynamic>?;
    if (args != null) {
      _priceId = args['id'];

      // Check if we're in edit mode
      if (args['isEdit'] == true) {
        _isReadOnly = true; // Set read-only mode if isEdit is true
      }

      // If we have a specific site location, set it
      if (args['siteLocation'] != null) {
        _selectedLocation = args['siteLocation'];
      }
    }

    // Load user role to determine if super admin
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      try {
        final currentUser = currentUserDocument;
        if (currentUser != null) {
          setState(() {
            isSuperAdmin = currentUser.role == Role.superAdmin;
          });
        }

        // Load locations first (this will set _locations)
        await loadLocations();

        // Then load price data (which depends on _locations)
        if (_priceId != null) {
          await loadPrice();
        }
      } catch (e) {
        // Show error message
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error loading data: ${e.toString()}'),
              backgroundColor: FlutterFlowTheme.of(context).error,
            ),
          );
        }
      } finally {
        // Set loader to false when done, regardless of success or failure
        showLoader.value = false;
      }
    });
  }

  Future<void> loadLocations() async {
    try {
      // First check if current user is admin
      final currentUser = currentUserDocument;
      if (currentUser?.role == Role.admin) {
        // For admin, only show their assigned site
        final siteQuery = await FirebaseFirestore.instance
            .collection('sites')
            .where('siteAdminId', isEqualTo: currentUser?.uid)
            .get();

        if (siteQuery.docs.isNotEmpty) {
          final adminSite = siteQuery.docs.first;
          setState(() {
            _locations = [adminSite.data()['siteLocation'] as String];
            if (_selectedLocation.isEmpty) {
              _selectedLocation = _locations.first;
            }
          });
        }
        return;
      }

      // For other users, check if we have a selected location from arguments
      final args = Get.arguments as Map<String, dynamic>?;
      if (args != null && args['siteLocation'] != null) {
        setState(() {
          _locations = [args['siteLocation'] as String];
          _selectedLocation = _locations.first;
        });
        return;
      }

      // If no specific location is selected, load all locations as before
      // For super admin, show all available sites
      final sitesSnapshot = await FirebaseFirestore.instance.collection('sites').get();

      // Get the current siteId if we're editing a Ferrous price
      String? currentSiteId;
      if (_priceId != null && (_selectedCategory == 'Ferrous' || _selectedCategory == 'ELV')) {
        final priceDoc = await FirebaseFirestore.instance.collection('prices').doc(_priceId).get();
        currentSiteId = priceDoc.data()?['siteId'] as String?;
      }

      // Filter locations: include sites that either:
      // 1. Don't have a priceId, or
      // 2. Belong to the current price being edited
      final locations = sitesSnapshot.docs
          .where((doc) {
            final data = doc.data();
            final hasPriceId = data.containsKey('priceId') && data['priceId'] != null;
            final isCurrentSite = doc.id == currentSiteId;

            return !hasPriceId || isCurrentSite;
          })
          .map((doc) => doc.data()['siteLocation'] as String?)
          .where((location) => location != null && location.isNotEmpty)
          .toSet() // Use Set to get unique locations
          .toList();

      setState(() {
        _locations = locations.cast<String>();
        // Set initial location if not already set
        if (_selectedLocation.isEmpty) {
          if (_selectedCategory == 'Non Ferrous') {
            _selectedLocation = 'Global (All Locations)';
          } else if (_locations.isNotEmpty) {
            _selectedLocation = _locations.first;
          } else {
            // If no locations available for Ferrous category
            _selectedLocation = '';
          }
        }
      });
    } catch (e) {
      print('Error loading locations: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'Error loading locations: ${e.toString()}',
            style: TextStyle(
              color: FlutterFlowTheme.of(context).info,
            ),
          ),
          backgroundColor: FlutterFlowTheme.of(context).error,
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }

  Future<void> checkUserRole() async {
    final userDoc = currentUserDocument;
    setState(() {
      isSuperAdmin = userDoc?.role == Role.superAdmin;
    });
  }

  void _addSubType() {
    if (_subTypeController.text.isEmpty) return;

    setState(() {
      if (_selectedCategory == 'Non Ferrous') {
        // For Non Ferrous: single price
        final price = double.tryParse(_subPriceController.text) ?? 0.0;
        final newSubType = {
          'type': _subTypeController.text,
          'price': price,
        };
        _subTypesList.add(newSubType);
      } else {
        // For Ferrous: only add price for the selected location
        final Map<String, double?> pricesMap = {};
        // Only add the selected location
        if (_locations.isNotEmpty) {
          pricesMap[_locations.first] = null;
        }

        final newSubType = {
          'type': _subTypeController.text,
          'prices': pricesMap,
        };
        _subTypesList.add(newSubType);
      }
      _subTypeController.clear();
      _subPriceController.clear();
    });
  }

  void _removeSubType(int index) {
    setState(() {
      _subTypesList.removeAt(index);
    });
  }

  void _startEditing(int index) {
    final subType = _subTypesList[index];
    setState(() {
      _editingIndex = index;
      _subTypeController.text = subType['type'];
      if (_selectedCategory == 'Non Ferrous') {
        _subPriceController.text = (subType['price'] ?? 0.0).toString();
      }
    });

    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16.0),
        ),
        child: Container(
          width: 400.0,
          decoration: BoxDecoration(
            color: FlutterFlowTheme.of(context).secondaryBackground,
            boxShadow: const [
              BoxShadow(
                blurRadius: 4.0,
                color: Color(0x33000000),
                offset: Offset(0.0, 2.0),
                spreadRadius: 0.0,
              )
            ],
            borderRadius: BorderRadius.circular(16.0),
          ),
          child: Padding(
            padding: const EdgeInsetsDirectional.fromSTEB(16.0, 16.0, 16.0, 16.0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'Edit Grade',
                  style: FlutterFlowTheme.of(context).bodyMedium.override(
                        fontFamily: 'Open Sans',
                        color: FlutterFlowTheme.of(context).blackColor,
                        fontSize: 26.0,
                        letterSpacing: 0.0,
                        fontWeight: FontWeight.w600,
                      ),
                ),
                const SizedBox(height: 16.0),
                Padding(
                  padding: const EdgeInsetsDirectional.fromSTEB(16.0, 0.0, 16.0, 0.0),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Padding(
                        padding: const EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 0.0, 4.0),
                        child: Text(
                          'Grade Name',
                          style: FlutterFlowTheme.of(context).bodyMedium,
                        ),
                      ),
                      TextFormField(
                        controller: _subTypeController,
                        decoration: _inputDecoration(''),
                      ),
                    ],
                  ),
                ),
                if (_selectedCategory == 'Non Ferrous') ...[
                  const SizedBox(height: 16.0),
                  Padding(
                    padding: const EdgeInsetsDirectional.fromSTEB(16.0, 0.0, 16.0, 0.0),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                          padding: const EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 0.0, 4.0),
                          child: Text(
                            'Price',
                            style: FlutterFlowTheme.of(context).bodyMedium,
                          ),
                        ),
                        TextFormField(
                          controller: _subPriceController,
                          decoration: _inputDecoration('Enter price'),
                          keyboardType: const TextInputType.numberWithOptions(decimal: true),
                        ),
                      ],
                    ),
                  ),
                ],
                const SizedBox(height: 24.0),
                Padding(
                  padding: const EdgeInsetsDirectional.fromSTEB(16.0, 0.0, 16.0, 0.0),
                  child: Row(
                    mainAxisSize: MainAxisSize.max,
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      FFButtonWidget(
                        onPressed: () {
                          _cancelEditing();
                          Navigator.pop(context);
                        },
                        text: 'Cancel',
                        options: FFButtonOptions(
                          width: 120.0,
                          height: 48.0,
                          padding: const EdgeInsets.all(8.0),
                          iconPadding: const EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 0.0, 0.0),
                          color: FlutterFlowTheme.of(context).secondaryBackground,
                          textStyle: FlutterFlowTheme.of(context).bodyLarge,
                          elevation: 0.0,
                          borderSide: BorderSide(
                            color: FlutterFlowTheme.of(context).alternate,
                            width: 1.0,
                          ),
                          borderRadius: BorderRadius.circular(8.0),
                        ),
                      ),
                      FFButtonWidget(
                        onPressed: () {
                          _updateSubType();
                          Navigator.pop(context);
                        },
                        text: 'Update',
                        options: FFButtonOptions(
                          width: 120.0,
                          height: 48.0,
                          padding: const EdgeInsets.all(8.0),
                          iconPadding: const EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 0.0, 0.0),
                          color: FlutterFlowTheme.of(context).primary,
                          textStyle: FlutterFlowTheme.of(context).titleSmall.override(
                                fontFamily: 'Open Sans',
                                color: Colors.white,
                                letterSpacing: 0.0,
                              ),
                          elevation: 0.0,
                          borderSide: const BorderSide(
                            color: Colors.transparent,
                            width: 1.0,
                          ),
                          borderRadius: BorderRadius.circular(8.0),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _updateSubType() {
    if (_editingIndex != null && _subTypeController.text.isNotEmpty) {
      setState(() {
        if (_selectedCategory == 'Non Ferrous') {
          final price = double.tryParse(_subPriceController.text) ?? 0.0;
          _subTypesList[_editingIndex!] = {
            'type': _subTypeController.text,
            'price': price,
          };
        } else {
          final currentSubType = _subTypesList[_editingIndex!];
          _subTypesList[_editingIndex!] = {
            'type': _subTypeController.text,
            'prices': currentSubType['prices'] ?? {},
          };
        }
        _cancelEditing();
      });
    }
  }

  void _cancelEditing() {
    setState(() {
      _editingIndex = null;
      _subTypeController.clear();
    });
  }

  // Method to apply the same price to all locations for a specific subtype
  void _applyPriceToAllLocations(int subtypeIndex, double price) {
    if (subtypeIndex < 0 || subtypeIndex >= _subTypesList.length) return;

    // First, create a completely new list to force a rebuild of all widgets
    final newSubTypesList = List<Map<String, dynamic>>.from(_subTypesList);
    final subType = Map<String, dynamic>.from(newSubTypesList[subtypeIndex]);

    // Create a new map for prices, ensuring it's initialized
    final Map<String, double> pricesMap = (subType['prices'] as Map<String, dynamic>? ?? {}).map(
      (key, value) => MapEntry(
          key, value is double ? value : (value != null ? double.tryParse(value.toString()) ?? 0.0 : 0.0)),
    );

    // Update all location prices with the same value
    for (final location in _locations) {
      pricesMap[location] = price;
    }

    // Update the subtype with the new prices
    subType['prices'] = pricesMap;
    newSubTypesList[subtypeIndex] = subType;

    // Update the state with the new list to force a complete rebuild
    setState(() {
      _subTypesList = newSubTypesList;
      _fillAllPriceController.clear(); // Clear the input field
      _filledSubtypes[subtypeIndex] = true;
    });
  }

  // Method to apply the same price to all locations in the location prices section
  void _applyPriceToAllLocationPrices(double price) {
    // Create a new map for prices
    final Map<String, double> pricesMap = {};

    // Update all location prices with the same value
    for (final location in _locations) {
      pricesMap[location] = price;
    }

    // Update the state with the new prices
    setState(() {
      _locationPrices.clear();
      _locationPrices.addAll(pricesMap);
      _fillAllLocationPriceController.clear(); // Clear the input field
      _locationPricesFilled = true; // Set flag to show clear button
    });
  }

  // Method to clear all location prices
  void _clearAllLocationPrices() {
    setState(() {
      _locationPrices.clear();
      _locationPricesFilled = false; // Reset flag to hide clear button
    });
  }

  // Method to clear all prices for a specific subtype
  void _clearAllPrices(int subtypeIndex) {
    if (subtypeIndex < 0 || subtypeIndex >= _subTypesList.length) return;

    // First, create a completely new list to force a rebuild of all widgets
    final newSubTypesList = List<Map<String, dynamic>>.from(_subTypesList);
    final subType = Map<String, dynamic>.from(newSubTypesList[subtypeIndex]);

    // Create a new map for prices with null values
    final Map<String, dynamic> pricesMap = {};

    // Set all location prices to empty string to clear the fields
    for (final location in _locations) {
      pricesMap[location] = '';
    }

    // Update the subtype with the cleared prices
    subType['prices'] = pricesMap;
    newSubTypesList[subtypeIndex] = subType;

    // Update the state with the new list to force a complete rebuild
    setState(() {
      _subTypesList = newSubTypesList;
      _filledSubtypes[subtypeIndex] = false;
    });
  }

  // Add this method to convert subtype data structure
  void _convertSubTypeFormat(String newCategory) {
    if (_subTypesList.isEmpty) return;

    setState(() {
      if (newCategory == 'Ferrous' || newCategory == 'ELV') {
        // Convert from Non Ferrous to Ferrous format
        _subTypesList = _subTypesList.map((subType) {
          // Get the original price from Non Ferrous subtype
          final originalPrice = subType['price'] ?? 0.0;

          // Create a new map for prices with only the selected location
          final Map<String, double> pricesMap = {};
          if (_locations.isNotEmpty) {
            pricesMap[_locations.first] = originalPrice > 0 ? originalPrice : 0.0;
          }

          return {
            'type': subType['type'],
            'prices': pricesMap,
          };
        }).toList();
      } else {
        // Convert from Ferrous to Non Ferrous format
        _subTypesList = _subTypesList.map((subType) {
          return {
            'type': subType['type'],
            'price': 0.0,
          };
        }).toList();
      }
    });
  }

  Future<void> savePrice() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    try {
      double? basePrice;

      // Only validate and parse base price for Non Ferrous metals
      if (_selectedCategory == 'Non Ferrous') {
        final cleanedPrice = _priceController.text.trim().replaceAll(',', '');
        basePrice = double.tryParse(cleanedPrice);

        if (basePrice == null) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Base price "${_priceController.text}" is not a valid number'),
              backgroundColor: FlutterFlowTheme.of(context).error,
            ),
          );
          return;
        }
      }

      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(
          child: CircularProgressIndicator(),
        ),
      );

      final firestore = FirebaseFirestore.instance;

      // 1. Save or update metal in metals collection
      final metalsRef = firestore.collection('metals');
      final metalDoc = await metalsRef
          .where('name', isEqualTo: _typeController.text)
          .where('category', isEqualTo: _selectedCategory)
          .get();

      String metalId;
      if (metalDoc.docs.isEmpty) {
        // Create new metal
        final newMetalRef = metalsRef.doc();
        metalId = newMetalRef.id;
        await newMetalRef.set({
          'id': metalId,
          'type': _typeController.text,
          'category': _selectedCategory,
          'createdAt': FieldValue.serverTimestamp(),
          'updatedAt': FieldValue.serverTimestamp(),
        });
      } else {
        metalId = metalDoc.docs.first.id;
        await metalsRef.doc(metalId).update({
          'updatedAt': FieldValue.serverTimestamp(),
        });
      }

      // 2. Save or update subtypes in subcollection
      final subtypesRef = metalsRef.doc(metalId).collection('subtypes');

      // Get existing subtypes to avoid duplication
      final existingSubtypes = await subtypesRef.get();
      final existingSubtypeNames = existingSubtypes.docs.map((doc) => doc.data()['name'] as String).toSet();

      // Map to store subtype IDs for price reference
      final Map<String, String> subtypeIdMap = {};

      // Add new subtypes and collect their IDs
      for (final subType in _subTypesList) {
        final subtypeName = subType['type'] as String;

        if (!existingSubtypeNames.contains(subtypeName)) {
          // Create new subtype
          final newSubtypeRef = subtypesRef.doc();
          await newSubtypeRef.set({
            'id': newSubtypeRef.id,
            'name': subtypeName,
            'createdAt': FieldValue.serverTimestamp(),
          });
          subtypeIdMap[subtypeName] = newSubtypeRef.id;
        } else {
          // Get existing subtype ID
          final existingDoc = existingSubtypes.docs.firstWhere((doc) => doc.data()['name'] == subtypeName);
          subtypeIdMap[subtypeName] = existingDoc.id;
        }
      }

      // 3. Handle site ID for Ferrous metals
      String? siteId;
      if (_selectedCategory == 'Ferrous' || _selectedCategory == 'ELV') {
        final siteSnapshot =
            await firestore.collection('sites').where('siteLocation', isEqualTo: _selectedLocation).get();

        if (siteSnapshot.docs.isNotEmpty) {
          siteId = siteSnapshot.docs.first.id;
        }
      }

      // 4. Save price data
      final pricesRef = firestore.collection('prices');

      if (_selectedCategory == 'Ferrous' || _selectedCategory == 'ELV') {
        // For Ferrous metals, create separate docs for each site
        final sitesSnapshot = await firestore.collection('sites').get();

        // Create price documents for each site
        for (var siteDoc in sitesSnapshot.docs) {
          final siteId = siteDoc.id;
          final siteLocation = siteDoc.data()['siteLocation'] as String;

          // Create or update price document for this site
          final existingPriceQuery =
              await pricesRef.where('metalId', isEqualTo: metalId).where('siteId', isEqualTo: siteId).get();

          final priceDocRef = existingPriceQuery.docs.isNotEmpty
              ? pricesRef.doc(existingPriceQuery.docs.first.id)
              : pricesRef.doc();

          // Prepare subtype prices for this specific site
          final subtypePrices = _subTypesList.map((subType) {
            final subtypeName = subType['type'] as String;
            final subtypePrice = subType['prices'][siteLocation] ?? 0.0;

            return {
              'subtypeId': subtypeIdMap[subtypeName],
              'name': subtypeName,
              'price': double.parse((subtypePrice ?? 0.0).toString()),
            };
          }).toList();

          // Calculate average price from subtypes for display in the list

          final priceData = {
            'id': priceDocRef.id,
            'metalId': metalId,
            'metal': _typeController.text, // Add metal name
            'category': _selectedCategory, // Add category
            'siteId': siteId,
            'price': _locationPrices[siteLocation], // Add price field for display in the list
            'types': subtypePrices, // Changed from 'subtypes' to 'types' to match schema
            'lastUpdated': FieldValue.serverTimestamp(),
          };

          if (existingPriceQuery.docs.isEmpty) {
            priceData['createdAt'] = FieldValue.serverTimestamp();
          }

          await priceDocRef.set(priceData, SetOptions(merge: true));
        }
      } else {
        // For Non-Ferrous metals (Global prices)
        final priceDocRef = _priceId != null ? pricesRef.doc(_priceId) : pricesRef.doc();

        // Prepare global subtype prices
        final subtypePrices = _subTypesList.map((subType) {
          final subtypeName = subType['type'] as String;
          final subtypePrice = subType['prices']['Global (All Locations)'] ?? 0.0;

          return {
            'subtypeId': subtypeIdMap[subtypeName],
            'name': subtypeName,
            'price': double.parse(subtypePrice.toString()),
          };
        }).toList();

        final priceData = {
          'id': priceDocRef.id,
          'metalId': metalId,
          'metal': _typeController.text, // Add metal name
          'category': _selectedCategory, // Add category
          'basePrice': basePrice!,
          'price': basePrice, // Add price field to match schema used in pricing_management_screen
          'types': subtypePrices, // Changed from 'subtypes' to 'types' to match schema
          'lastUpdated': FieldValue.serverTimestamp(),
        };

        if (_priceId == null) {
          priceData['createdAt'] = FieldValue.serverTimestamp();
        }

        await priceDocRef.set(priceData, SetOptions(merge: true));
      }

      if (mounted) {
        Navigator.pop(context); // Close loading dialog
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(_priceId == null ? 'Price added successfully' : 'Price updated successfully'),
            backgroundColor: FlutterFlowTheme.of(context).success,
          ),
        );
        Navigator.pop(context); // Return to previous screen
      }
    } catch (e) {
      if (mounted) {
        Navigator.pop(context); // Close loading dialog
      }
      // Use a logger in production instead of print
      debugPrint('Error in savePrice: $e'); // Debug log the error

      String errorMessage;
      if (e.toString().contains('FormatException') || e.toString().contains('Invalid double')) {
        errorMessage = 'Invalid number format. Please enter numbers only (e.g., 123.45)';
      } else if (e.toString().contains('permission-denied')) {
        errorMessage = 'You don\'t have permission to perform this action';
      } else if (e.toString().contains('network')) {
        errorMessage = 'Network error. Please check your connection';
      } else {
        errorMessage = 'Error: ${e.toString()}';
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(errorMessage),
            backgroundColor: FlutterFlowTheme.of(context).error,
            duration: const Duration(seconds: 4),
          ),
        );
      }
    }
  }

  Widget _buildMainForm() {
    return Container(
      height: _selectedCategory == 'Non Ferrous' ? 285 : 277,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: FlutterFlowTheme.of(context).secondaryBackground,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: FlutterFlowTheme.of(context).alternate.withAlpha(128),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Metal Details',
            style: FlutterFlowTheme.of(context).headlineSmall.override(
                  fontFamily: 'Open Sans',
                  fontWeight: FontWeight.w600,
                ),
          ),
          const SizedBox(height: 24),
          // Category Dropdown
          DropdownButtonFormField<String>(
              value: _selectedCategory,
              decoration: _inputDecoration('Category'),
              items: ['Ferrous', 'Non Ferrous', 'ELV'].map((category) {
                return DropdownMenuItem(
                  value: category,
                  child: Text(category),
                );
              }).toList(),
              onChanged:
                  // _isReadOnly?
                  null // Disable dropdown if in read-only mode
              // : isSuperAdmin
              //     ? (value) {
              //         if (value != null) {
              //           _convertSubTypeFormat(value);
              //           setState(() {
              //             _selectedCategory = value;
              //             // Clear base price when switching to Ferrous
              //             if (value == 'Ferrous') {
              //               _priceController.clear();
              //             }
              //           });
              //         }
              //       }
              //     : null,
              ),

          const SizedBox(height: 16),
          TextFormField(
            readOnly: _isReadOnly,
            controller: _typeController,
            decoration: _inputDecoration('Product Name'),
            validator: (value) => value?.isEmpty ?? true ? 'Please enter product name' : null,
          ),
          // Only show base price field for Non Ferrous metals
          const SizedBox(height: 16),

          if (_selectedCategory == 'Non Ferrous')
            TextFormField(
              controller: _priceController,
              decoration: _inputDecoration('Base Price'),
              keyboardType: const TextInputType.numberWithOptions(decimal: true),
              readOnly: _isReadOnly, // Make read-only if in view mode
              onChanged: (value) {
                final cleanedValue = value.replaceAll(RegExp(r'[^0-9.]'), '');
                if (cleanedValue != value) {
                  _priceController.value = TextEditingValue(
                    text: cleanedValue,
                    selection: TextSelection.collapsed(offset: cleanedValue.length),
                  );
                }
              },
              validator: (value) {
                if (_selectedCategory == 'Non Ferrous') {
                  if (value == null || value.isEmpty) {
                    return 'Please enter a price';
                  }
                  final cleanedValue = value.trim().replaceAll(',', '');
                  if (double.tryParse(cleanedValue) == null) {
                    return 'Please enter a valid number';
                  }
                }
                return null;
              },
            ),
        ],
      ),
    );
  }

  Widget _buildLocationPrices() {
    if (_selectedCategory == 'Non Ferrous') return const SizedBox.shrink();
    if (_locations.isEmpty) return const SizedBox.shrink();

    return Expanded(
      child: Container(
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          color: FlutterFlowTheme.of(context).secondaryBackground,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: FlutterFlowTheme.of(context).alternate.withAlpha(128),
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withAlpha(5),
              blurRadius: 4,
              offset: const Offset(0, 1),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with location count
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Location Price',
                      style: FlutterFlowTheme.of(context).headlineSmall.override(
                            fontFamily: 'Open Sans',
                            fontWeight: FontWeight.w600,
                          ),
                    ),
                    Padding(
                      padding: const EdgeInsets.only(top: 4),
                      child: Text(
                        _locations.first,
                        style: FlutterFlowTheme.of(context).bodySmall.override(
                              fontFamily: 'Open Sans',
                              color: FlutterFlowTheme.of(context).secondaryText,
                              fontStyle: FontStyle.italic,
                              fontSize: 12,
                            ),
                      ),
                    ),
                  ],
                ),
                // Remove the "Fill All Fields" section since we only have one location
              ],
            ),
            const SizedBox(height: 24),
            // Single location price input
            _buildSingleLocationPrice(_locations.first),
          ],
        ),
      ),
    );
  }

  Widget _buildSingleLocationPrice(String location) {
    final price = _locationPrices[location];

    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: FlutterFlowTheme.of(context).secondaryBackground,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: FlutterFlowTheme.of(context).alternate,
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(5),
            blurRadius: 4,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Location header
          Container(
            height: 60,
            width: double.infinity,
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: FlutterFlowTheme.of(context).primary.withAlpha(15),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(7),
                topRight: Radius.circular(7),
              ),
              border: Border(
                bottom: BorderSide(
                  color: FlutterFlowTheme.of(context).alternate,
                  width: 1,
                ),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.location_on_outlined,
                  size: 16,
                  color: FlutterFlowTheme.of(context).primary,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    location,
                    style: FlutterFlowTheme.of(context).bodyMedium.override(
                          fontFamily: 'Open Sans',
                          fontWeight: FontWeight.w600,
                          color: FlutterFlowTheme.of(context).primaryText,
                        ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ),

          // Price input
          Padding(
            padding: const EdgeInsets.all(12),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Price',
                  style: FlutterFlowTheme.of(context).bodySmall.override(
                        fontFamily: 'Open Sans',
                        color: FlutterFlowTheme.of(context).secondaryText,
                      ),
                ),
                const SizedBox(height: 8),
                SizedBox(
                  height: 40,
                  child: PriceTextField(
                    initialValue: price != null ? price.toString() : '',
                    readOnly: _isReadOnly, // Pass the read-only flag
                    onChanged: (value) {
                      if (!_isUpdatingPrice) {
                        _isUpdatingPrice = true;
                        final newPrice = value.isEmpty ? 0.0 : double.tryParse(value) ?? 0.0;
                        _locationPrices[location] = newPrice;
                        setState(() {});
                        _isUpdatingPrice = false;
                      }
                    },
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSubTypes() {
    return Container(
      padding: const EdgeInsets.all(24),
      margin: const EdgeInsets.only(top: 24),
      decoration: BoxDecoration(
        color: FlutterFlowTheme.of(context).secondaryBackground,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: FlutterFlowTheme.of(context).alternate.withAlpha(128),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Grade',
                    style: FlutterFlowTheme.of(context).headlineSmall.override(
                          fontFamily: 'Open Sans',
                          fontWeight: FontWeight.w600,
                        ),
                  ),
                  Padding(
                    padding: const EdgeInsets.only(top: 1),
                    child: Text(
                      _locations.isEmpty ? 'Global' : _locations.first,
                      style: FlutterFlowTheme.of(context).bodySmall.override(
                            fontFamily: 'Open Sans',
                            color: FlutterFlowTheme.of(context).secondaryText,
                            fontStyle: FontStyle.italic,
                          ),
                    ),
                  ),
                ],
              ),
              _isReadOnly
                  ? const SizedBox.shrink()
                  : FFButtonWidget(
                      onPressed: () {
                        _subTypeController.clear();
                        _subPriceController.clear();
                        showDialog(
                          context: context,
                          builder: (context) => Dialog(
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(16.0),
                            ),
                            child: Container(
                              width: 400.0,
                              decoration: BoxDecoration(
                                color: FlutterFlowTheme.of(context).secondaryBackground,
                                boxShadow: const [
                                  BoxShadow(
                                    blurRadius: 4.0,
                                    color: Color(0x33000000),
                                    offset: Offset(0.0, 2.0),
                                    spreadRadius: 0.0,
                                  )
                                ],
                                borderRadius: BorderRadius.circular(16.0),
                              ),
                              child: Padding(
                                padding: const EdgeInsetsDirectional.fromSTEB(16.0, 16.0, 16.0, 16.0),
                                child: Column(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Text(
                                      'Add Grade',
                                      style: FlutterFlowTheme.of(context).bodyMedium.override(
                                            fontFamily: 'Open Sans',
                                            color: FlutterFlowTheme.of(context).blackColor,
                                            fontSize: 26.0,
                                            letterSpacing: 0.0,
                                            fontWeight: FontWeight.w600,
                                          ),
                                    ),
                                    const SizedBox(height: 16.0),
                                    Padding(
                                      padding: const EdgeInsetsDirectional.fromSTEB(16.0, 0.0, 16.0, 0.0),
                                      child: Column(
                                        mainAxisSize: MainAxisSize.min,
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          Padding(
                                            padding: const EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 0.0, 4.0),
                                            child: Text(
                                              'Grade Name',
                                              style: FlutterFlowTheme.of(context).bodyMedium,
                                            ),
                                          ),
                                          TextFormField(
                                            controller: _subTypeController,
                                            decoration: _inputDecoration(''),
                                          ),
                                        ],
                                      ),
                                    ),
                                    if (_selectedCategory == 'Non Ferrous') ...[
                                      const SizedBox(height: 16.0),
                                      Padding(
                                        padding: const EdgeInsetsDirectional.fromSTEB(16.0, 0.0, 16.0, 0.0),
                                        child: Column(
                                          mainAxisSize: MainAxisSize.min,
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            Padding(
                                              padding:
                                                  const EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 0.0, 4.0),
                                              child: Text(
                                                'Price',
                                                style: FlutterFlowTheme.of(context).bodyMedium,
                                              ),
                                            ),
                                            TextFormField(
                                              controller: _subPriceController,
                                              decoration: _inputDecoration('Enter price'),
                                              keyboardType:
                                                  const TextInputType.numberWithOptions(decimal: true),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                    const SizedBox(height: 24.0),
                                    Padding(
                                      padding: const EdgeInsetsDirectional.fromSTEB(16.0, 0.0, 16.0, 0.0),
                                      child: Row(
                                        mainAxisSize: MainAxisSize.max,
                                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                        children: [
                                          FFButtonWidget(
                                            onPressed: () => Navigator.pop(context),
                                            text: 'Cancel',
                                            options: FFButtonOptions(
                                              width: 120.0,
                                              height: 48.0,
                                              padding: const EdgeInsets.all(8.0),
                                              iconPadding:
                                                  const EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 0.0, 0.0),
                                              color: FlutterFlowTheme.of(context).secondaryBackground,
                                              textStyle: FlutterFlowTheme.of(context).titleSmall.override(
                                                    fontFamily: 'Open Sans',
                                                    color: FlutterFlowTheme.of(context).primaryText,
                                                    letterSpacing: 0.0,
                                                  ),
                                              elevation: 0.0,
                                              borderSide: BorderSide(
                                                color: FlutterFlowTheme.of(context).alternate,
                                                width: 1.0,
                                              ),
                                              borderRadius: BorderRadius.circular(8.0),
                                            ),
                                          ),
                                          const SizedBox(width: 12.0),
                                          FFButtonWidget(
                                            onPressed: () {
                                              _addSubType();
                                              Navigator.pop(context);
                                            },
                                            text: 'Add',
                                            options: FFButtonOptions(
                                              width: 120.0,
                                              height: 48.0,
                                              padding: const EdgeInsets.all(8.0),
                                              iconPadding:
                                                  const EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 0.0, 0.0),
                                              color: FlutterFlowTheme.of(context).primary,
                                              textStyle: FlutterFlowTheme.of(context).titleSmall.override(
                                                    fontFamily: 'Open Sans',
                                                    color: Colors.white,
                                                    letterSpacing: 0.0,
                                                  ),
                                              elevation: 0.0,
                                              borderSide: const BorderSide(
                                                color: Colors.transparent,
                                                width: 1.0,
                                              ),
                                              borderRadius: BorderRadius.circular(8.0),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        );
                      },
                      text: 'Add Grade',
                      options: FFButtonOptions(
                        height: 40,
                        padding: const EdgeInsetsDirectional.fromSTEB(16, 0, 16, 0),
                        color: FlutterFlowTheme.of(context).primary,
                        textStyle: FlutterFlowTheme.of(context).titleSmall.override(
                              fontFamily: 'Open Sans',
                              color: Colors.white,
                            ),
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
            ],
          ),
          const SizedBox(height: 24),
          // Debug text to check if subtypes are loaded
          if (_subTypesList.isEmpty)
            Padding(
              padding: const EdgeInsets.only(bottom: 16),
              child: Text(
                'No subtypes added yet. Click "Add Grade" to add one.',
                style: FlutterFlowTheme.of(context).bodyMedium.override(
                      fontFamily: 'Open Sans',
                      fontStyle: FontStyle.italic,
                      color: FlutterFlowTheme.of(context).secondaryText,
                    ),
              ),
            ),
          Wrap(
            spacing: 20,
            runSpacing: 20,
            children: List.generate(_subTypesList.length, (index) {
              final subType = _subTypesList[index];
              // Add null checks for the 'type' property
              final typeName = subType['type'] as String? ?? '';

              return Container(
                width: 300,
                decoration: BoxDecoration(
                  color: FlutterFlowTheme.of(context).secondaryBackground,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: FlutterFlowTheme.of(context).alternate,
                    width: 1,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withAlpha(5),
                      blurRadius: 4,
                      offset: const Offset(0, 1),
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Header with edit/delete buttons
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: FlutterFlowTheme.of(context).secondaryBackground,
                        borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(11),
                          topRight: Radius.circular(11),
                        ),
                        border: Border(
                          bottom: BorderSide(
                            color: FlutterFlowTheme.of(context).alternate,
                            width: 1,
                          ),
                        ),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                            child: Text(
                              typeName, // Use the safely extracted value
                              style: FlutterFlowTheme.of(context).titleMedium.override(
                                    fontFamily: 'Open Sans',
                                    fontWeight: FontWeight.w600,
                                    color: FlutterFlowTheme.of(context).primaryText,
                                  ),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          _isReadOnly
                              ? const SizedBox.shrink()
                              : Row(
                                  children: [
                                    // Edit button
                                    Container(
                                      decoration: BoxDecoration(
                                        color: FlutterFlowTheme.of(context).secondaryBackground,
                                        borderRadius: BorderRadius.circular(8),
                                        border: Border.all(
                                          color: FlutterFlowTheme.of(context).alternate,
                                          width: 1,
                                        ),
                                      ),
                                      child: IconButton(
                                        icon: Icon(
                                          Icons.edit_outlined,
                                          size: 20,
                                          color: FlutterFlowTheme.of(context).primary,
                                        ),
                                        onPressed: () => _startEditing(index),
                                        tooltip: 'Edit',
                                        padding: const EdgeInsets.all(8),
                                        constraints: const BoxConstraints(),
                                        splashRadius: 24,
                                      ),
                                    ),
                                    const SizedBox(width: 8),
                                    // Delete button
                                    Container(
                                      decoration: BoxDecoration(
                                        color: FlutterFlowTheme.of(context).secondaryBackground,
                                        borderRadius: BorderRadius.circular(8),
                                        border: Border.all(
                                          color: FlutterFlowTheme.of(context).alternate,
                                          width: 1,
                                        ),
                                      ),
                                      child: IconButton(
                                        icon: Icon(
                                          Icons.delete_outline,
                                          size: 20,
                                          color: FlutterFlowTheme.of(context).error,
                                        ),
                                        onPressed: () => _removeSubType(index),
                                        tooltip: 'Delete',
                                        padding: const EdgeInsets.all(8),
                                        constraints: const BoxConstraints(),
                                        splashRadius: 24,
                                      ),
                                    ),
                                  ],
                                ),
                        ],
                      ),
                    ),

                    // Content area
                    Padding(
                      padding: const EdgeInsets.all(0),
                      child: _selectedCategory == 'Non Ferrous'
                          ? _buildNonFerrousPrice(subType)
                          : _buildFerrousPrices(subType, index),
                    ),
                  ],
                ),
              );
            }),
          ),
        ],
      ),
    );
  }

  // Helper method to build the price display for Non Ferrous metals
  Widget _buildNonFerrousPrice(Map<String, dynamic> subType) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: FlutterFlowTheme.of(context).secondaryBackground,
        borderRadius: BorderRadius.circular(10),
        border: Border.all(
          color: FlutterFlowTheme.of(context).alternate,
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(5),
            blurRadius: 4,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Base Price',
                style: FlutterFlowTheme.of(context).bodyMedium.override(
                      fontFamily: 'Open Sans',
                      fontWeight: FontWeight.w600,
                      fontSize: 14,
                      color: FlutterFlowTheme.of(context).secondaryText,
                    ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: FlutterFlowTheme.of(context).secondary.withAlpha(30),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  'Non Ferrous',
                  style: FlutterFlowTheme.of(context).bodySmall.override(
                        fontFamily: 'Open Sans',
                        fontWeight: FontWeight.w600,
                        color: FlutterFlowTheme.of(context).secondary,
                      ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: FlutterFlowTheme.of(context).primary.withAlpha(15),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: FlutterFlowTheme.of(context).primary.withAlpha(25),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.attach_money,
                    size: 20,
                    color: FlutterFlowTheme.of(context).primary,
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  '${subType['price']}',
                  style: FlutterFlowTheme.of(context).titleMedium.override(
                        fontFamily: 'Open Sans',
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                        color: FlutterFlowTheme.of(context).primaryText,
                      ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Helper method to build the location prices for Ferrous metals
  Widget _buildFerrousPrices(Map<String, dynamic> subType, int index) {
    // Add null check for the 'prices' map
    final Map<String, dynamic> prices = subType['prices'] as Map<String, dynamic>? ?? {};
    final location = _locations.isNotEmpty ? _locations.first : 'Default Location';

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: FlutterFlowTheme.of(context).secondaryBackground,
        borderRadius: BorderRadius.circular(10),
        border: Border.all(
          color: FlutterFlowTheme.of(context).alternate,
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(5),
            blurRadius: 4,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Single location price input
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: FlutterFlowTheme.of(context).primary.withAlpha(5),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: FlutterFlowTheme.of(context).alternate,
                width: 1,
              ),
            ),
            child: Row(
              children: [
                // Location column
                Expanded(
                  flex: 3,
                  child: Row(
                    children: [
                      Icon(
                        Icons.location_on_outlined,
                        size: 16,
                        color: FlutterFlowTheme.of(context).primary,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          location,
                          style: FlutterFlowTheme.of(context).bodyMedium.override(
                                fontFamily: 'Open Sans',
                                fontWeight: FontWeight.w500,
                              ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                ),

                // Price column
                Expanded(
                  flex: 2,
                  child: Row(
                    children: [
                      Expanded(
                        child: SizedBox(
                          height: 36,
                          child: PriceTextField(
                            readOnly: _isReadOnly,
                            initialValue: prices[location] != null ? prices[location].toString() : '',
                            onChanged: (value) {
                              if (!_isUpdatingPrice) {
                                _isUpdatingPrice = true;
                                final newPrice = value.isEmpty ? null : double.tryParse(value) ?? 0.0;

                                // Make sure the prices map exists
                                if (subType['prices'] == null) {
                                  subType['prices'] = <String, dynamic>{};
                                }

                                subType['prices'][location] = newPrice;
                                _subTypesList[index] = subType;
                                setState(() {});
                                _isUpdatingPrice = false;
                              }
                            },
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Helper method to create input decoration with read-only styling
  InputDecoration _inputDecoration(String hint) {
    return InputDecoration(
      hintText: hint,
      hintStyle: FlutterFlowTheme.of(context).bodySmall,
      enabledBorder: OutlineInputBorder(
        borderSide: BorderSide(
          color: _isReadOnly
              ? FlutterFlowTheme.of(context).alternate.withOpacity(0.5)
              : FlutterFlowTheme.of(context).alternate,
          width: 1.0,
        ),
        borderRadius: BorderRadius.circular(8.0),
      ),
      focusedBorder: OutlineInputBorder(
        borderSide: BorderSide(
          color: FlutterFlowTheme.of(context).primary,
          width: 1.0,
        ),
        borderRadius: BorderRadius.circular(8.0),
      ),
      errorBorder: OutlineInputBorder(
        borderSide: BorderSide(
          color: FlutterFlowTheme.of(context).error,
          width: 1.0,
        ),
        borderRadius: BorderRadius.circular(8.0),
      ),
      focusedErrorBorder: OutlineInputBorder(
        borderSide: BorderSide(
          color: FlutterFlowTheme.of(context).error,
          width: 1.0,
        ),
        borderRadius: BorderRadius.circular(8.0),
      ),
      filled: true,
      fillColor: _isReadOnly
          ? FlutterFlowTheme.of(context).alternate.withOpacity(0.1)
          : FlutterFlowTheme.of(context).secondaryBackground,
      contentPadding: const EdgeInsetsDirectional.fromSTEB(16.0, 12.0, 16.0, 12.0),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          Container(
            width: double.infinity,
            height: 100.0,
            decoration: BoxDecoration(
              color: FlutterFlowTheme.of(context).primary,
            ),
            child: Row(
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Padding(
                  padding: const EdgeInsetsDirectional.fromSTEB(30.0, 0.0, 0.0, 0.0),
                  child: Text(
                    'Pricing Details',
                    style: FlutterFlowTheme.of(context).bodyMedium.override(
                          fontFamily: 'Open Sans',
                          color: FlutterFlowTheme.of(context).whiteColor,
                          fontSize: 26.0,
                          letterSpacing: 0.0,
                          fontWeight: FontWeight.w600,
                        ),
                  ),
                ),
                _isReadOnly
                    ? const SizedBox.shrink()
                    : Builder(
                        builder: (context) => Padding(
                              padding: const EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 30.0, 0.0),
                              child: FFButtonWidget(
                                onPressed: savePrice,
                                text: 'Save Changes',
                                options: FFButtonOptions(
                                  height: 40.0,
                                  padding: const EdgeInsetsDirectional.fromSTEB(16.0, 0.0, 16.0, 0.0),
                                  iconPadding: const EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 0.0, 0.0),
                                  color: FlutterFlowTheme.of(context).secondary,
                                  textStyle: FlutterFlowTheme.of(context).titleSmall.override(
                                        fontFamily: 'Open Sans',
                                        color: Colors.white,
                                        letterSpacing: 0.0,
                                        fontWeight: FontWeight.bold,
                                      ),
                                  elevation: 0.0,
                                  borderRadius: BorderRadius.circular(10.0),
                                  disabledColor: FlutterFlowTheme.of(context).alternate,
                                ),
                              ),
                            )),
              ],
            ),
          ),
          Obx(() => showLoader.value
              ? const Expanded(child: Center(child: CircularProgressIndicator()))
              : Expanded(
                  child: Form(
                    key: _formKey,
                    child: SingleChildScrollView(
                      padding: const EdgeInsets.all(24),
                      child: Column(
                        children: [
                          
                            _buildMainForm(),
                          _buildSubTypes(),
                        ],
                      ),
                    ),
                  ),
                )),
        ],
      ),
    );
  }

  @override
  void dispose() {
    // Dispose all controllers to prevent memory leaks
    _typeController.dispose();
    _priceController.dispose();
    _locationController.dispose();
    _subTypeController.dispose();
    _subPriceController.dispose();
    _fillAllPriceController.dispose();
    _fillAllLocationPriceController.dispose();

    super.dispose();
  }

  Future<void> loadPrice() async {
    try {
      final priceDoc = await FirebaseFirestore.instance.collection('prices').doc(_priceId).get();
      if (!priceDoc.exists) {
        return;
      }

      final data = priceDoc.data()!;
      safeSetState(() {
        _typeController.text = data['metal'] ?? '';
        _selectedCategory = data['category'] ?? 'Ferrous';

        // For Non-Ferrous, set the base price
        if (_selectedCategory == 'Non Ferrous') {
          _priceController.text = (data['basePrice'] ?? 0.0).toString();
        }

        // Load subtypes
        final types = data['types'] as List<dynamic>? ?? [];
        _subTypesList = types.map((type) {
          if (_selectedCategory == 'Non Ferrous') {
            return {
              'type': type['name'],
              'price': type['price'],
            };
          } else {
            // For Ferrous, create a prices map for each location
            final Map<String, double> pricesMap = {};
            pricesMap[_selectedLocation] = type['price'] ?? 0.0;

            return {
              'type': type['name'],
              'prices': pricesMap,
            };
          }
        }).toList();

        // For Ferrous, set the location price
        if (_selectedCategory == 'Ferrous' || _selectedCategory == 'ELV') {
          _locationPrices[_selectedLocation] = data['price'] ?? 0.0;
        }
      });
    } catch (e) {
      print('Error loading price: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading price data: ${e.toString()}'),
            backgroundColor: FlutterFlowTheme.of(context).error,
          ),
        );
      }
    }
  }
}

// Custom TextField widget that maintains focus when typing
class PriceTextField extends StatefulWidget {
  final String initialValue;
  final Function(String) onChanged;
  final bool readOnly;

  const PriceTextField({
    super.key,
    required this.initialValue,
    required this.onChanged,
    this.readOnly = false,
  });

  @override
  State<PriceTextField> createState() => _PriceTextFieldState();
}

class _PriceTextFieldState extends State<PriceTextField> {
  late TextEditingController _controller;
  final FocusNode _focusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.initialValue);
  }

  @override
  void didUpdateWidget(PriceTextField oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Only update the controller text if the initialValue changed and the field is not focused
    if (widget.initialValue != oldWidget.initialValue && !_focusNode.hasFocus) {
      _controller.text = widget.initialValue;
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      controller: _controller,
      readOnly: widget.readOnly,
      focusNode: _focusNode,
      keyboardType: const TextInputType.numberWithOptions(decimal: true),
      decoration: InputDecoration(
        isDense: true,
        prefixIcon: Icon(
          Icons.attach_money,
          size: 14,
          color: FlutterFlowTheme.of(context).primary,
        ),
        enabledBorder: OutlineInputBorder(
          borderSide: BorderSide(
            color: FlutterFlowTheme.of(context).alternate,
            width: 1,
          ),
          borderRadius: BorderRadius.circular(6),
        ),
        focusedBorder: OutlineInputBorder(
          borderSide: BorderSide(
            color: FlutterFlowTheme.of(context).primary,
            width: 1,
          ),
          borderRadius: BorderRadius.circular(6),
        ),
        filled: true,
        fillColor: FlutterFlowTheme.of(context).secondaryBackground,
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 6,
          vertical: 6,
        ),
      ),
      style: FlutterFlowTheme.of(context).bodySmall.override(
            fontFamily: 'Open Sans',
            fontSize: 12,
          ),
      onChanged: widget.onChanged,
    );
  }
}
