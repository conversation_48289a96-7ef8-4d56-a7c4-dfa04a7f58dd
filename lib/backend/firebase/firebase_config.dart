import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/foundation.dart';

Future initFirebase() async {
  if (kIsWeb) {
    await Firebase.initializeApp(
        options: FirebaseOptions(
            apiKey: "AIzaSyDJhQOqwXko46Rbmkl5ABJGt2UDm7wROY8",
            authDomain: "unimetals-2e360.firebaseapp.com",
            projectId: "unimetals-2e360",
            storageBucket: "unimetals-2e360.firebasestorage.app",
            messagingSenderId: "367727537813",
            appId: "1:367727537813:web:0586166f4c74dae817b59c"));
  } else {
    await Firebase.initializeApp();
  }
}
