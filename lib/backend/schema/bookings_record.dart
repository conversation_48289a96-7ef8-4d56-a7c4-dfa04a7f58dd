import 'package:cloud_firestore/cloud_firestore.dart';

class BookingsRecord {
  final String bookingId;
  final Timestamp bookingDateTime;
  final Timestamp endDateTime;
  final String userId;
  final String siteId;
  final String siteAdminId;
  final Timestamp createdAt;

  BookingsRecord({
    required this.bookingId,
    required this.bookingDateTime,
    required this.endDateTime,
    required this.userId,
    required this.siteId,
    required this.siteAdminId,
    required this.createdAt,
  });

  factory BookingsRecord.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return BookingsRecord(
      bookingId: doc.id,
      bookingDateTime: data['bookingDateTime'] as Timestamp,
      endDateTime: data['endDateTime'] as Timestamp,
      userId: data['userId'] ?? '',
      siteId: data['siteId'] ?? '',
      siteAdminId: data['siteAdminId'] ?? '',
      createdAt: data['createdAt'] as Timestamp,
    );
  }
}

Stream<List<BookingsRecord>> queryBookingsRecord({
  Query Function(Query)? queryBuilder,
  int limit = -1,
}) {
  Query query = FirebaseFirestore.instance.collection('bookings');

  if (queryBuilder != null) {
    query = queryBuilder(query);
  }

  if (limit > 0) {
    query = query.limit(limit);
  }

  return query.snapshots().map((snapshots) =>
      snapshots.docs.map((doc) => BookingsRecord.fromFirestore(doc)).toList());
}
