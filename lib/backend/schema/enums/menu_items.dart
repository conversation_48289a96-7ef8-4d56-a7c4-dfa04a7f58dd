import 'package:flutter/material.dart';
import 'package:unimetals_admin/backend/schema/enums/enums.dart';

enum MenuItem {
  adminUsers,
  siteManagement,
  bookings,
  customers,
  pricing,  // Add new menu item
  notifications;

  String get label {
    switch (this) {
      case MenuItem.adminUsers:
        return 'Admin Users';
      case MenuItem.siteManagement:
        return 'Site Management';
      case MenuItem.bookings:
        return 'Bookings';
      case MenuItem.customers:
        return 'Customers';
      case MenuItem.pricing:
        return 'Pricing Management';
      case MenuItem.notifications:
        return 'Send Notification';
    }
  }

  IconData get icon {
    switch (this) {
      case MenuItem.adminUsers:
        return Icons.people_outline;
      case MenuItem.siteManagement:
        return Icons.location_on_outlined;
      case MenuItem.bookings:
        return Icons.calendar_today_outlined;
      case MenuItem.customers:
        return Icons.group_outlined;
      case MenuItem.pricing:
        return Icons.attach_money_outlined;
      case MenuItem.notifications:
        return Icons.notifications_outlined;
    }
  }

  String get route {
    switch (this) {
      case MenuItem.adminUsers:
        return '/adminUsers';
      case MenuItem.siteManagement:
        return '/siteManagementScreen';
      case MenuItem.bookings:
        return '/bookingsScreen';
      case MenuItem.customers:
        return '/customersScreen';
      case MenuItem.pricing:
        return '/pricingManagementScreen';  // Make sure this matches PricingManagementScreenWidget.routePath
      case MenuItem.notifications:
        return '/sendNotificationScreen';
    }
  }

  static List<MenuItem> getMenuItemsForRole(Role role) {
    if (role == Role.superAdmin) {
      return [
        MenuItem.adminUsers,
        MenuItem.siteManagement,
        MenuItem.bookings,
        MenuItem.customers,
        MenuItem.pricing,
        MenuItem.notifications,
      ];
    } else {
      return [
        MenuItem.siteManagement,
        MenuItem.bookings,
        MenuItem.customers,
        MenuItem.pricing,
      ];
    }
  }
}
