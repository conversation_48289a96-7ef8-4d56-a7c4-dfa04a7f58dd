import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:unimetals_admin/backend/schema/util/firestore_util.dart';

class SiteSchedulesRecord extends FirestoreRecord {
  SiteSchedulesRecord._(
    DocumentReference reference,
    Map<String, dynamic> data,
  ) : super(reference, data) {
    _initializeFields();
  }

  // "siteId" field.
  String? _siteId;
  String get siteId => _siteId ?? '';
  bool hasSiteId() => _siteId != null;

  // "weeklySchedule" field.
  Map<String, DaySchedule>? _weeklySchedule;
  Map<String, DaySchedule> get weeklySchedule => _weeklySchedule ?? {};
  bool hasWeeklySchedule() => _weeklySchedule != null;

  void _initializeFields() {
    _siteId = snapshotData['siteId'] as String?;
    
    final weeklyScheduleData = snapshotData['weeklySchedule'] as Map<String, dynamic>?;
    if (weeklyScheduleData != null) {
      _weeklySchedule = weeklyScheduleData.map((key, value) => MapEntry(
        key,
        DaySchedule.fromMap(value as Map<String, dynamic>),
      ));
    }
  }

  static CollectionReference get collection =>
      FirebaseFirestore.instance.collection('site_schedules');

  static Stream<SiteSchedulesRecord> getDocument(DocumentReference ref) =>
      ref.snapshots().map((s) => SiteSchedulesRecord.fromSnapshot(s));

  static Future<SiteSchedulesRecord> getDocumentOnce(DocumentReference ref) =>
      ref.get().then((s) => SiteSchedulesRecord.fromSnapshot(s));

  static SiteSchedulesRecord fromSnapshot(DocumentSnapshot snapshot) => SiteSchedulesRecord._(
        snapshot.reference,
        mapFromFirestore(snapshot.data() as Map<String, dynamic>),
      );

  static SiteSchedulesRecord getDocumentFromData(
    Map<String, dynamic> data,
    DocumentReference reference,
  ) =>
      SiteSchedulesRecord._(reference, mapFromFirestore(data));

  @override
  String toString() =>
      'SiteSchedulesRecord(reference: ${reference.path}, data: $snapshotData)';

  @override
  int get hashCode => reference.path.hashCode;

  @override
  bool operator ==(other) =>
      other is SiteSchedulesRecord &&
      reference.path.hashCode == other.reference.path.hashCode;
}

@immutable
class DaySchedule {
  const DaySchedule({
    required this.isOpen,
    this.openTime,
    this.closeTime,
    this.timeSlots,
  });

  final bool isOpen;
  final TimeOfDay? openTime;
  final TimeOfDay? closeTime;
  final List<TimeSlot>? timeSlots;

  Map<String, dynamic> toMap() {
    return {
      'isOpen': isOpen,
      'openTime': openTime != null 
          ? '${openTime!.hour.toString().padLeft(2, '0')}:${openTime!.minute.toString().padLeft(2, '0')}'
          : null,
      'closeTime': closeTime != null 
          ? '${closeTime!.hour.toString().padLeft(2, '0')}:${closeTime!.minute.toString().padLeft(2, '0')}'
          : null,
      'timeSlots': timeSlots?.map((slot) => slot.toMap()).toList(),
    };
  }

  factory DaySchedule.fromMap(Map<String, dynamic> map) {
    return DaySchedule(
      isOpen: map['isOpen'] ?? false,
      openTime: map['openTime'] != null 
          ? _timeOfDayFromString(map['openTime'] as String)
          : null,
      closeTime: map['closeTime'] != null 
          ? _timeOfDayFromString(map['closeTime'] as String)
          : null,
      timeSlots: map['timeSlots'] != null 
          ? List<TimeSlot>.from(
              (map['timeSlots'] as List).map((x) => TimeSlot.fromMap(x as Map<String, dynamic>)))
          : null,
    );
  }

  static TimeOfDay _timeOfDayFromString(String time) {
    final parts = time.split(':');
    return TimeOfDay(
      hour: int.parse(parts[0]),
      minute: int.parse(parts[1]),
    );
  }
}

@immutable
class TimeSlot {
  const TimeSlot({
    required this.time,
    required this.isDisabledByAdmin,
  });

  final String time;  // Format: "HH:mm"
  final bool isDisabledByAdmin;

  Map<String, dynamic> toMap() {
    return {
      'time': time,
      'isDisabledByAdmin': isDisabledByAdmin,
    };
  }

  factory TimeSlot.fromMap(Map<String, dynamic> map) {
    return TimeSlot(
      time: map['time'] as String,
      isDisabledByAdmin: map['isDisabledByAdmin'] ?? true,
    );
  }
}
