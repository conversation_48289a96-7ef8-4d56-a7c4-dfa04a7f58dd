import 'dart:async';

import 'package:collection/collection.dart';

import '/backend/schema/util/firestore_util.dart';
import '/backend/schema/util/schema_util.dart';
import '/backend/schema/enums/enums.dart';

import 'index.dart';
import '/flutter_flow/flutter_flow_util.dart';

class SitesRecord extends FirestoreRecord {
  SitesRecord._(
    DocumentReference reference,
    Map<String, dynamic> data,
  ) : super(reference, data) {
    _initializeFields();
  }

  // "siteName" field.
  String? _siteName;
  String get siteName => _siteName ?? '';
  bool hasSiteName() => _siteName != null;

  // "contactPhone" field.
  String? _contactPhone;
  String get contactPhone => _contactPhone ?? '';
  bool hasContactPhone() => _contactPhone != null;

  // "createdAt" field.
  DateTime? _createdAt;
  DateTime? get createdAt => _createdAt;
  bool hasCreatedAt() => _createdAt != null;

  // "siteLocation" field.
  String? _siteLocation;
  String get siteLocation => _siteLocation ?? '';
  bool hasSiteLocation() => _siteLocation != null;

  // "siteLatLng" field.
  LatLng? _siteLatLng;
  LatLng? get siteLatLng => _siteLatLng;
  bool hasSiteLatLng() => _siteLatLng != null;

  // "description" field.
  String? _description;
  String get description => _description ?? '';
  bool hasDescription() => _description != null;

  // "siteAdminId" field.
  String? _siteAdminId;
  String get siteAdminId => _siteAdminId ?? '';
  bool hasSiteAdminId() => _siteAdminId != null;

  // "siteId" field.
  String? _siteId;
  String get siteId => _siteId ?? '';
  bool hasSiteId() => _siteId != null;

  // "scheduleId" field.
  String? _scheduleId;
  String get scheduleId => _scheduleId ?? '';
  bool hasScheduleId() => _scheduleId != null;

  // Add weekly hours field
  Map<String, dynamic>? _weeklyHours;
  Map<String, dynamic> get weeklyHours => _weeklyHours ?? {};
  bool hasWeeklyHours() => _weeklyHours != null;

  void _initializeFields() {
    _siteName = snapshotData['siteName'] as String?;
    _contactPhone = snapshotData['contactPhone'] as String?;
    _createdAt = snapshotData['createdAt'] as DateTime?;
    _siteLocation = snapshotData['siteLocation'] as String?;
    _siteLatLng = snapshotData['siteLatLng'] as LatLng?;
    _description = snapshotData['description'] as String?;
    _siteAdminId = snapshotData['siteAdminId'] as String?;
    _siteId = snapshotData['siteId'] as String?;
    _scheduleId = snapshotData['scheduleId'] as String?;
    _weeklyHours = snapshotData['weeklyHours'] as Map<String, dynamic>?;
  }

  static CollectionReference get collection =>
      FirebaseFirestore.instance.collection('sites');

  static Stream<SitesRecord> getDocument(DocumentReference ref) =>
      ref.snapshots().map((s) => SitesRecord.fromSnapshot(s));

  static Future<SitesRecord> getDocumentOnce(DocumentReference ref) =>
      ref.get().then((s) => SitesRecord.fromSnapshot(s));

  static SitesRecord fromSnapshot(DocumentSnapshot snapshot) => SitesRecord._(
        snapshot.reference,
        mapFromFirestore(snapshot.data() as Map<String, dynamic>),
      );

  static SitesRecord getDocumentFromData(
    Map<String, dynamic> data,
    DocumentReference reference,
  ) =>
      SitesRecord._(reference, mapFromFirestore(data));

  @override
  String toString() =>
      'SitesRecord(reference: ${reference.path}, data: $snapshotData)';

  @override
  int get hashCode => reference.path.hashCode;

  @override
  bool operator ==(other) =>
      other is SitesRecord &&
      reference.path.hashCode == other.reference.path.hashCode;
}

Map<String, dynamic> createSitesRecordData({
  String? siteName,
  String? contactPhone,
  DateTime? createdAt,
  String? siteLocation,
  LatLng? siteLatLng,
  String? description,
  String? siteAdminId,
  String? siteId,
  String? scheduleId,
  Map<String, dynamic>? weeklyHours,
}) {
  final firestoreData = mapToFirestore(
    <String, dynamic>{
      'siteName': siteName,
      'contactPhone': contactPhone,
      'createdAt': createdAt,
      'siteLocation': siteLocation,
      'siteLatLng': siteLatLng,
      'description': description,
      'siteAdminId': siteAdminId,
      'siteId': siteId,
      'scheduleId': scheduleId,
      'weeklyHours': weeklyHours,
    }.withoutNulls,
  );

  return firestoreData;
}

class SitesRecordDocumentEquality implements Equality<SitesRecord> {
  const SitesRecordDocumentEquality();

  @override
  bool equals(SitesRecord? e1, SitesRecord? e2) {
    return e1?.siteName == e2?.siteName &&
        e1?.contactPhone == e2?.contactPhone &&
        e1?.createdAt == e2?.createdAt &&
        e1?.siteLocation == e2?.siteLocation &&
        e1?.siteLatLng == e2?.siteLatLng &&
        e1?.description == e2?.description &&
        e1?.siteAdminId == e2?.siteAdminId &&
        e1?.siteId == e2?.siteId;
  }

  @override
  int hash(SitesRecord? e) => const ListEquality().hash([
        e?.siteName,
        e?.contactPhone,
        e?.createdAt,
        e?.siteLocation,
        e?.siteLatLng,
        e?.description,
        e?.siteAdminId,
        e?.siteId
      ]);

  @override
  bool isValidKey(Object? o) => o is SitesRecord;
}
