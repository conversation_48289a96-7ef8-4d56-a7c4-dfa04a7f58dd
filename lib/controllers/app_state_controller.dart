import 'package:get/get.dart';
import '../auth/firebase_auth/auth_util.dart';
import '../backend/schema/enums/enums.dart';

class AppStateController extends GetxController {
  final _loggedIn = false.obs;
  final _showSplashImage = true.obs;
  final _userRole = Rx<Role?>(null);

  bool get loggedIn => _loggedIn.value;
  bool get showSplashImage => _showSplashImage.value;
  Role? get userRole => _userRole.value;

  void setLoggedIn(bool value) {
    _loggedIn.value = value;
  }

  void stopShowingSplashImage() {
    _showSplashImage.value = false;
  }

  void updateAuthState(BaseAuthUser user) {
    _loggedIn.value = user.loggedIn;
    
    // Update role when auth state changes
    if (user.loggedIn && currentUserDocument != null) {
      _userRole.value = currentUserDocument?.role;
    } else {
      _userRole.value = null;
    }
  }

  String getInitialRoute() {
    if (!loggedIn) return '/loginScreen';
    
    switch (_userRole.value) {
      case Role.superAdmin:
        return '/homeScreen';
      case Role.admin:
        return '/siteManagementScreen';
      default:
        return '/loginScreen';
    }
  }
}
