import '/backend/backend.dart';
import '/backend/schema/enums/enums.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/custom_code/actions/index.dart' as actions;
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

Future snackbarAction(
  BuildContext context, {
  String? snackbarMessage,
}) async {
  ScaffoldMessenger.of(context).showSnackBar(
    SnackBar(
      content: Text(
        snackbarMessage!,
        style: TextStyle(
          color: FlutterFlowTheme.of(context).snackbarText,
        ),
      ),
      duration: Duration(milliseconds: 4000),
      backgroundColor: FlutterFlowTheme.of(context).snackbarBackground,
    ),
  );
}

Future navigateAndRemovePage(
  BuildContext context, {
  String? page,
}) async {
  await actions.navigateAndRemovePage(
    context,
    page!,
  );
}
