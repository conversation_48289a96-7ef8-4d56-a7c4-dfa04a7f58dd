// Automatic FlutterFlow imports
// Begin custom action code
// DO NOT REMOVE OR MODIFY THE CODE ABOVE!

import 'package:firebase_auth/firebase_auth.dart';

import '/backend/backend.dart';
import 'index.dart'; // Imports other custom actions

Future<String> deleteAdminUser(String userId, String email, String password) async {
  String tempAppName = DateTime.now().millisecondsSinceEpoch.toString();
  FirebaseApp? tempApp;

  try {
    // Check if an app with this name already exists and delete it
    try {
      tempApp = Firebase.app(tempAppName);
      await tempApp.delete();
    } catch (e) {
      // App doesn't exist, which is fine
    }

    // Initialize secondary Firebase app
    tempApp = await Firebase.initializeApp(
      name: tempAppName,
      options: Firebase.app().options,
    );

    String decryptPass = await decryptPassword(password);

    // Log in using email and password
    UserCredential userCredential = await FirebaseAuth.instanceFor(app: tempApp)
        .signInWithEmailAndPassword(email: email, password: decryptPass);

    // Delete the currently logged-in user from secondary app
    await userCredential.user!.delete();

    // Delete user document
    await FirebaseFirestore.instance.collection('users').doc(userId).delete();

    // Delete any associated sites
    final siteQuery =
        await FirebaseFirestore.instance.collection('sites').where('siteAdminId', isEqualTo: userId).get();

    for (final doc in siteQuery.docs) {
      await doc.reference.update({
        'siteAdminId': '', // Set to empty string instead of deleting the site
      });
    }

    return 'Success';
  } on FirebaseAuthException catch (e) {
    return 'Auth error: ${e.message}';
  } catch (e) {
    return 'Error: $e';
  } finally {
    // Ensure the temporary app is always deleted
    if (tempApp != null) {
      try {
        await tempApp.delete();
      } catch (e) {
        // Ignore cleanup errors
      }
    }
  }
}
