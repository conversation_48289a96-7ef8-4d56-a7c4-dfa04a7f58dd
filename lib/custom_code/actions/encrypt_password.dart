// Automatic FlutterFlow imports
import '/backend/backend.dart';
import '/backend/schema/enums/enums.dart';
import '/actions/actions.dart' as action_blocks;
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import 'index.dart'; // Imports other custom actions
import 'package:flutter/material.dart';
// Begin custom action code
// DO NOT REMOVE OR MODIFY THE CODE ABOVE!

import 'package:encrypt/encrypt.dart' as encrypt;
import 'dart:convert';

Future<String> encryptPassword(String password) async {
  // Add your function code here!
  String encryptionKey = FFAppConstants.encryptionKey;
  String ivKey = FFAppConstants.ivKey;
  final _key = encrypt.Key.fromUtf8(encryptionKey); // 32 chars
  final _iv = encrypt.IV.fromUtf8(ivKey); // 16 chars

  final encrypter =
      encrypt.Encrypter(encrypt.AES(_key, mode: encrypt.AESMode.cbc));
  final encrypted = encrypter.encrypt(password, iv: _iv);
  return encrypted.base64;
}
