// Automatic FlutterFlow imports
// Begin custom action code
// DO NOT REMOVE OR MODIFY THE CODE ABOVE!

import 'package:firebase_auth/firebase_auth.dart';

import '/backend/backend.dart';
import '/backend/schema/enums/enums.dart';
import '/flutter_flow/flutter_flow_util.dart';
import 'index.dart'; // Imports other custom actions

Future<String> createAdminUser(
  String email,
  String password,
  Role role,
  DateTime createdTime,
  String siteId, // Add this parameter
) async {
  String randomDocGen = 'aA123KhHH8923S';

  //Create the secondary app to create the users
  FirebaseApp app = await Firebase.initializeApp(
      name: randomDocGen, options: Firebase.app().options);
  try {
   //Create the user with the email & password provided
    UserCredential userCredential = await FirebaseAuth.instanceFor(app: app)
        .createUserWithEmailAndPassword(
            email: email, password: password);
    if (userCredential.user != null) {
      final uid = userCredential.user!.uid;
      String returnmsg = "Success";
      // USERS COLLECTION
      final usersRef = FirebaseFirestore.instance.collection('users');
            String encryptedPass = await encryptPassword(password);

      await usersRef.doc(uid).set({
        'email': email,
        'uid': uid,
        'role': role.name,
        'created_time': createdTime,
                'encryptedPassword': encryptedPass,

      });

      // Update the site document with the admin's UID
      final sitesRef = FirebaseFirestore.instance.collection('sites');
      await sitesRef.doc(siteId).update({
        'siteAdminId': uid,
      });

      return returnmsg;
    } else {
      return "Error while creating the UID";
    }
  } on FirebaseAuthException catch (e) {
    return e.message ?? "An error occurred";
  }
}
