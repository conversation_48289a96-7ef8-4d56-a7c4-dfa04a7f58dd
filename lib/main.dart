import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_web_plugins/url_strategy.dart';
import 'package:get/get.dart';
import 'package:unimetals_admin/pages/auth/login_screen/login_screen_widget.dart';
import 'package:unimetals_admin/pages/bookings/bookings_screen/bookings_screen_widget.dart';
import 'package:unimetals_admin/pages/customers/customers_screen/customers_screen_widget.dart';
import 'package:unimetals_admin/pages/home/<USER>/home_screen_widget.dart';
import 'package:unimetals_admin/pages/notifications/send_notification_page.dart';
import 'package:unimetals_admin/pages/pricing/pricing_details/pricing_details_widget.dart';
import 'package:unimetals_admin/pages/pricing/pricing_edit/pricing_edit_widget.dart';
import 'package:unimetals_admin/pages/pricing/pricing_management_screen/pricing_management_screen_widget.dart';
import 'package:unimetals_admin/pages/site_management/site_detail_screen/site_detail_screen_widget.dart';
import 'package:unimetals_admin/pages/site_management/site_management_screen/site_management_screen_widget.dart';

import 'auth/firebase_auth/firebase_user_provider.dart';
import 'backend/firebase/firebase_config.dart';
import 'controllers/app_state_controller.dart';
import 'flutter_flow/flutter_flow_util.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  usePathUrlStrategy();

  await initFirebase();

  // Initialize GetX controllers
  Get.put(AppStateController());

  runApp(const MyApp());
}

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  // This widget is the root of your application.
  @override
  State<MyApp> createState() => _MyAppState();

  static _MyAppState of(BuildContext context) => context.findAncestorStateOfType<_MyAppState>()!;
}

class MyAppScrollBehavior extends MaterialScrollBehavior {
  @override
  Set<PointerDeviceKind> get dragDevices => {
        PointerDeviceKind.touch,
        PointerDeviceKind.mouse,
      };
}

class _MyAppState extends State<MyApp> {
  ThemeMode _themeMode = ThemeMode.system;
  late Stream<BaseAuthUser> userStream;

  @override
  void initState() {
    super.initState();

    userStream = unimetalsAdminFirebaseUserStream()
      ..listen((user) {
        Get.find<AppStateController>().updateAuthState(user);
      });

    Future.delayed(
      const Duration(milliseconds: 1000),
      () => Get.find<AppStateController>().stopShowingSplashImage(),
    );
  }

  @override
  void dispose() {
    super.dispose();
  }

  void setThemeMode(ThemeMode mode) => safeSetState(() {
        _themeMode = mode;
      });

  @override
  Widget build(BuildContext context) {
    final appStateController = Get.find<AppStateController>();

    return GetMaterialApp(
      debugShowCheckedModeBanner: false,
      title: 'Unimetals Admin',
      initialRoute: '/',
      scrollBehavior: MyAppScrollBehavior(),
      localizationsDelegates: const [
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      supportedLocales: const [Locale('en', '')],
      theme: ThemeData(
        brightness: Brightness.light,
        useMaterial3: false,
      ),
      themeMode: _themeMode,
      defaultTransition: Transition.fadeIn,
      getPages: [
        GetPage(
          name: '/',
          page: () =>
              Obx(() => appStateController.loggedIn ? const HomeScreenWidget() : const LoginScreenWidget()),
        ),
        GetPage(
          name: LoginScreenWidget.routePath,
          page: () => const LoginScreenWidget(),
        ),
        GetPage(
          name: HomeScreenWidget.routePath,
          page: () => const HomeScreenWidget(),
        ),
        GetPage(
          name: SiteManagementScreenWidget.routePath,
          page: () => const SiteManagementScreenWidget(),
        ),
        // Add the Pricing Management route
        GetPage(
          name: PricingManagementScreenWidget.routePath,
          page: () => const PricingManagementScreenWidget(),
        ),
        GetPage(
          name: SiteDetailScreenWidget.routePath,
          page: () {
            final parameters = Get.parameters;
            final siteRef = parameters['siteRef'];
            return SiteDetailScreenWidget(siteRef: siteRef);
          },
        ),
        GetPage(
          name: BookingsScreenWidget.routePath,
          page: () => const BookingsScreenWidget(),
        ),
        GetPage(
          name: SendNotificationPage.routePath,
          page: () => const SendNotificationPage(),
        ),
        GetPage(
          name: CustomersScreenWidget.routePath,
          page: () => const CustomersScreenWidget(),
        ),
        GetPage(
          name: PricingManagementScreenWidget.routePath,
          page: () => const PricingManagementScreenWidget(),
        ),
        GetPage(
          name: PricingDetailsWidget.routePath,
          page: () => const PricingDetailsWidget(),
        ),
        GetPage(
          name: '/pricing-edit',
          page: () => const PricingEditWidget(),
        ),
      ],
    );
  }
}
