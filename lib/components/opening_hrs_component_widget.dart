import 'package:flutter/material.dart';

import '/backend/backend.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import 'opening_hrs_component_model.dart';

export 'opening_hrs_component_model.dart';

class OpeningHrsComponentWidget extends StatefulWidget {
  const OpeningHrsComponentWidget({
    super.key,
    required this.siteRef,
    required this.dayName,
  });

  final DocumentReference siteRef;
  final String dayName;

  @override
  State<OpeningHrsComponentWidget> createState() => _OpeningHrsComponentWidgetState();
}

class _OpeningHrsComponentWidgetState extends State<OpeningHrsComponentWidget> {
  late OpeningHrsComponentModel _model;

  String formatTime(String time24) {
    try {
      final parts = time24.split(':');
      int hours = int.parse(parts[0]);
      final minutes = parts[1];
      final period = hours >= 12 ? 'PM' : 'AM';

      // Convert to 12-hour format
      hours = hours > 12 ? hours - 12 : hours;
      // Handle midnight (00:00) case
      hours = hours == 0 ? 12 : hours;

      return '$hours:$minutes $period';
    } catch (e) {
      return time24;
    }
  }

  @override
  void setState(VoidCallback callback) {
    super.setState(callback);
    _model.onUpdate();
  }

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => OpeningHrsComponentModel());
    WidgetsBinding.instance.addPostFrameCallback((_) => safeSetState(() {}));
  }

  @override
  void dispose() {
    _model.maybeDispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<SitesRecord>(
      stream: SitesRecord.getDocument(widget.siteRef),
      builder: (context, snapshot) {
        if (!snapshot.hasData) {
          return Container(
            width: 105.0,
            height: 50.0, // Fixed height
            decoration: BoxDecoration(
              color: FlutterFlowTheme.of(context).primaryBackground,
              borderRadius: BorderRadius.circular(10.0),
              border: Border.all(
                color: FlutterFlowTheme.of(context).darkGreyColor,
                width: 1.0,
              ),
            ),
            child: const Center(
              child: SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                ),
              ),
            ),
          );
        }

        final site = snapshot.data!;
        final dayData = site.weeklyHours[widget.dayName.toLowerCase()];
        final isOpen = dayData?['isOpen'] ?? false;
        final openTime = formatTime(dayData?['openTime'] ?? '');
        final closeTime = formatTime(dayData?['closeTime'] ?? '');

        return Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 105.0,
              height: 50.0, // Fixed height
              decoration: BoxDecoration(
                color: FlutterFlowTheme.of(context).primaryBackground,
                borderRadius: BorderRadius.circular(10.0),
                border: Border.all(
                  color: isOpen ? FlutterFlowTheme.of(context).primary : FlutterFlowTheme.of(context).error,
                  width: 1.0,
                ),
              ),
              child: Padding(
                padding: const EdgeInsets.all(6.0),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center, // Center content vertically
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      widget.dayName.substring(0, 3),
                      style: FlutterFlowTheme.of(context).bodyMedium.override(
                            fontFamily: 'Metropolis',
                            fontSize: 14.0,
                            letterSpacing: 0.0,
                            fontWeight: FontWeight.w600,
                            useGoogleFonts: false,
                          ),
                    ),
                    const SizedBox(height: 4),
                    if (isOpen)
                      Text(
                        '$openTime - $closeTime',
                        textAlign: TextAlign.center,
                        style: FlutterFlowTheme.of(context).bodyMedium.override(
                              fontFamily: 'Open Sans',
                              fontSize: 10.0,
                              letterSpacing: 0.0,
                              fontWeight: FontWeight.normal,
                            ),
                      )
                    else
                      Text(
                        'Closed',
                        textAlign: TextAlign.center,
                        style: FlutterFlowTheme.of(context).bodyMedium.override(
                              fontFamily: 'Open Sans',
                              color: FlutterFlowTheme.of(context).error,
                              fontSize: 12.0,
                              letterSpacing: 0.0,
                              fontWeight: FontWeight.normal,
                            ),
                      ),
                  ],
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}
