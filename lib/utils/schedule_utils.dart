import 'dart:collection';

import 'package:flutter/material.dart';
import 'package:unimetals_admin/backend/schema/site_schedules_record.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

List<Map<String, dynamic>> generateTimeSlots(TimeOfDay openTime, TimeOfDay closeTime) {
  final slots = <Map<String, dynamic>>[];

  // Convert TimeOfDay to minutes since midnight for easier calculation
  int startMinutes = openTime.hour * 60 + openTime.minute;
  int endMinutes = closeTime.hour * 60 + closeTime.minute;

  // Start with first slot 30 minutes after opening time
  for (int currentMinutes = startMinutes + 30; currentMinutes <= endMinutes; currentMinutes += 30) {
    final hours = (currentMinutes ~/ 60).toString().padLeft(2, '0');
    final minutes = (currentMinutes % 60).toString().padLeft(2, '0');
    final timeString = '$hours:$minutes';

    slots.add({
      'time': timeString,
      'isDisabledByAdmin': false, // Set to false by default (meaning slot is available)
    });
  }

  return slots;
}

Map<String, Map<String, dynamic>> createDefaultWeeklySchedule(
  TimeOfDay openTime,
  TimeOfDay closeTime,
    {bool allClosed = false}

) {
  // Define weekdays in proper order
  final weekDays = [
    'monday',
    'tuesday',
    'wednesday',
    'thursday',
    'friday',
    'saturday',
    'sunday',
  ];

  // Use LinkedHashMap to maintain insertion order
  final schedule = <String, Map<String, dynamic>>{};

  // Add days in specific order
  for (final day in weekDays) {
    final daySchedule = DaySchedule(
      isOpen:
      allClosed?false:
       day != 'sunday', // Closed on Sundays by default
      openTime: openTime,
      closeTime: closeTime,
      timeSlots: generateTimeSlots(openTime, closeTime).map((map) => TimeSlot.fromMap(map)).toList(),
    );

    schedule[day] = daySchedule.toMap();
  }

  return schedule;
}
