<component name="libraryTable">
  <library name="Flutter Plugins" type="FlutterPluginsLibraryType">
    <CLASSES>
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/url_launcher_macos-3.2.1" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.0" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/firebase_performance_web-0.1.7+4" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/sqflite-2.3.3+1" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/google_sign_in-6.2.1" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/path_provider-2.1.4" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/shared_preferences_android-2.3.2" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/firebase_performance-0.10.0+10" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/sign_in_with_apple-6.1.2" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/firebase_core_web-2.18.1" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/url_launcher_android-6.3.10" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.2" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/path_provider_android-2.2.10" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/url_launcher_linux-3.2.0" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/url_launcher_web-2.3.3" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/package_info_plus-8.0.2" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/google_sign_in_android-6.1.30" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/firebase_auth_web-5.13.4" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/shared_preferences_web-2.4.2" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/firebase_auth-5.3.3" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/firebase_core-3.8.0" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/cloud_firestore_web-4.3.4" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/shared_preferences-2.3.2" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.1" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/google_sign_in_ios-5.7.7" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.1" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/cloud_firestore-5.5.0" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/url_launcher_windows-3.1.2" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/url_launcher-6.3.0" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/sign_in_with_apple_web-2.1.0" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.1" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/google_sign_in_web-0.12.4+2" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/google_api_headers-4.3.4" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/file_picker-10.1.9" />
    </CLASSES>
    <JAVADOC />
    <SOURCES />
  </library>
</component>