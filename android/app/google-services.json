{"project_info": {"project_number": "367727537813", "project_id": "unimetals-2e360", "storage_bucket": "unimetals-2e360.firebasestorage.app"}, "client": [{"client_info": {"mobilesdk_app_id": "1:367727537813:android:981f8635e72f89c017b59c", "android_client_info": {"package_name": "com.mycompany.unimetals"}}, "oauth_client": [{"client_id": "367727537813-o6ucb31mn715meumkvkdc34grq7k5qss.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.mycompany.unimetals", "certificate_hash": "9bbb3186b2189ef16d8f272bdab59362c395cbb4"}}, {"client_id": "367727537813-reap3fhf4j407aedvgu7fi4ji7et9sk5.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.mycompany.unimetals", "certificate_hash": "69a98d07910a60caea70934e881702d77273454a"}}, {"client_id": "367727537813-ls4c9gl87v5geht26dh0pf3sv1mqtdgr.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyDb7T-AXUAYvQSvjQOWmAOV7Lo92z69YBs"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "367727537813-ls4c9gl87v5geht26dh0pf3sv1mqtdgr.apps.googleusercontent.com", "client_type": 3}, {"client_id": "367727537813-sv78i442cn154b2u84nc3695j8m7hgea.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "com.mycompany.unimetals"}}]}}}], "configuration_version": "1"}