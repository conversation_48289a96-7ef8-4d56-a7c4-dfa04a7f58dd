name: unimetals_admin
description: A new Flutter project.

# The following line prevents the package from being accidentally published to
# pub.dev using `pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
version: 1.0.0+1

environment:
  sdk: ">=3.0.0 <4.0.0"

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  flutter_web_plugins:
    sdk: flutter
  auto_size_text: 3.0.0
  cached_network_image: 3.4.1
  cached_network_image_platform_interface: 4.1.1
  cached_network_image_web: 1.3.1
  cloud_firestore: 5.5.0
  cloud_firestore_platform_interface: 6.5.0
  cloud_firestore_web: 4.3.4
  collection: 1.19.0
  easy_debounce: 2.0.1
  encrypt:
  firebase_auth: 5.3.3
  firebase_auth_platform_interface: 7.4.9
  firebase_auth_web: 5.13.4
  firebase_core: 3.8.0
  firebase_core_platform_interface: 5.3.0
  firebase_core_web: 2.18.1
  firebase_performance: 0.10.0+10
  firebase_performance_platform_interface: 0.1.4+46
  firebase_performance_web: 0.1.7+4
  flutter_animate: 4.5.0
  flutter_cache_manager: 3.4.1
  flutter_google_places: 
    git:
      url: https://github.com/ShehanYjoz/flutter_google_places.git
  font_awesome_flutter: 10.7.0
  from_css_color: 2.0.0
  go_router: 12.1.3
  google_api_headers: 4.3.4
  google_fonts: 6.1.0
  google_sign_in: 6.2.1
  google_sign_in_android: 6.1.30
  google_sign_in_ios: 5.7.7
  google_sign_in_platform_interface: 2.4.5
  google_sign_in_web: 0.12.4+2
  intl: 0.19.0
  json_path: 0.7.2
  package_info_plus: 8.0.2
  package_info_plus_platform_interface: 3.0.1
  page_transition: 2.1.0
  path_provider: 2.1.4
  path_provider_android: 2.2.10
  path_provider_foundation: 2.4.0
  path_provider_linux: 2.2.1
  path_provider_platform_interface: 2.1.2
  path_provider_windows: 2.3.0
  plugin_platform_interface: 2.1.8
  provider: 6.1.2
  rxdart: 0.27.7
  shared_preferences: 2.3.2
  shared_preferences_android: 2.3.2
  shared_preferences_foundation: 2.5.2
  shared_preferences_linux: 2.4.1
  shared_preferences_platform_interface: 2.4.1
  shared_preferences_web: 2.4.2
  shared_preferences_windows: 2.4.1
  sign_in_with_apple: 6.1.2
  sign_in_with_apple_platform_interface: 1.1.0
  sign_in_with_apple_web: 2.1.0
  sqflite: 2.3.3+1
  sqflite_common: 2.5.4+3
  stream_transform: 2.1.0
  text_search: 1.0.1
  timeago: 3.6.1
  url_launcher: 6.3.0
  url_launcher_android: 6.3.10
  url_launcher_ios: 6.3.1
  url_launcher_linux: 3.2.0
  url_launcher_macos: 3.2.1
  url_launcher_platform_interface: 2.3.2
  url_launcher_web: 2.3.3
  url_launcher_windows: 3.1.2

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.0
  get: ^4.7.2
  file_picker: ^10.1.9

dependency_overrides:
  google_api_headers: 4.3.4
  http: 1.2.2
  package_info_plus: 8.0.2
  package_info_plus_platform_interface: 3.0.1
  uuid: ^4.0.0

dev_dependencies:
  flutter_lints: 4.0.0
  lints: 4.0.0
  flutter_test:
    sdk: flutter

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/fonts/
    - assets/images/
    - assets/videos/
    - assets/audios/
    - assets/rive_animations/
    - assets/pdfs/
    - assets/jsons/

  fonts:
    - family: 'Jost'
      fonts:
        - asset: assets/fonts/Jost-Light.ttf
          weight: 300
    - family: 'Metropolis'
      fonts:
        - asset: assets/fonts/Metropolis-Medium.otf
          weight: 500
        - asset: assets/fonts/Metropolis-Bold.otf
          weight: 700


  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware.

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages

