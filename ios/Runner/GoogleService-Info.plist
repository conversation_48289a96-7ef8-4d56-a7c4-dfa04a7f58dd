<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CLIENT_ID</key>
	<string>367727537813-sv78i442cn154b2u84nc3695j8m7hgea.apps.googleusercontent.com</string>
	<key>REVERSED_CLIENT_ID</key>
	<string>com.googleusercontent.apps.367727537813-sv78i442cn154b2u84nc3695j8m7hgea</string>
	<key>ANDROID_CLIENT_ID</key>
	<string>367727537813-o6ucb31mn715meumkvkdc34grq7k5qss.apps.googleusercontent.com</string>
	<key>API_KEY</key>
	<string>AIzaSyDPTDalC9jHTS7D3IrHxgAR8VA95aIY_vA</string>
	<key>GCM_SENDER_ID</key>
	<string>367727537813</string>
	<key>PLIST_VERSION</key>
	<string>1</string>
	<key>BUNDLE_ID</key>
	<string>com.mycompany.unimetals</string>
	<key>PROJECT_ID</key>
	<string>unimetals-2e360</string>
	<key>STORAGE_BUCKET</key>
	<string>unimetals-2e360.firebasestorage.app</string>
	<key>IS_ADS_ENABLED</key>
	<false></false>
	<key>IS_ANALYTICS_ENABLED</key>
	<false></false>
	<key>IS_APPINVITE_ENABLED</key>
	<true></true>
	<key>IS_GCM_ENABLED</key>
	<true></true>
	<key>IS_SIGNIN_ENABLED</key>
	<true></true>
	<key>GOOGLE_APP_ID</key>
	<string>1:367727537813:ios:1efd31f2fde4909b17b59c</string>
</dict>
</plist>