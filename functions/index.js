/**
 * Import function triggers from their respective submodules:
 *
 * const {onCall} = require("firebase-functions/v2/https");
 * const {onDocumentWritten} = require("firebase-functions/v2/firestore");
 *
 * See a full list of supported triggers at https://firebase.google.com/docs/functions
 */

// Create and deploy your first functions
// https://firebase.google.com/docs/functions/get-started

// exports.helloWorld = onRequest((request, response) => {
//   logger.info("Hello logs!", {structuredData: true});
//   response.send("Hello from Firebase!");
// });

const functions = require('firebase-functions');
const admin = require('firebase-admin');
const axios = require('axios');
const cors = require('cors')({ origin: true });

// Initialize Firebase Admin
admin.initializeApp();
const googleMapsKeyWeb = 'AIzaSyDqqSTOG6AwZhgmHz2ofTX19fv8i0Gq5mQ';

exports.proxyGoogleMapsHttp = functions.https.onRequest((request, response) => {
    return cors(request, response, async () => {
      try {
        let endpoint;
        let params = { ...request.query };
        
        // Determine which Places API endpoint to use
        if (request.path.includes('/place/details')) {
          endpoint = 'https://maps.googleapis.com/maps/api/place/details/json';
        } else if (request.path.includes('/place/autocomplete')) {
          endpoint = 'https://maps.googleapis.com/maps/api/place/autocomplete/json';
        } else {
          endpoint = 'https://maps.googleapis.com/maps/api/place/autocomplete/json';
        }
  
        // Add the API key
        params.key = googleMapsKeyWeb;
  
        console.log('Making request to:', endpoint);
        console.log('With params:', params);
  
        const result = await axios({
          method: 'get',
          url: endpoint,
          params: params
        });
  
        // Ensure the response matches the expected format
        const responseData = {
          status: result.data.status,
          error_message: result.data.error_message,
          predictions: result.data.predictions || [],
          result: result.data.result || null
        };
  
        response.set('Access-Control-Allow-Origin', '*');
        response.json(responseData);
  
      } catch (error) {
        console.error('Google Maps API Error:', error);
        
        response.status(error.response?.status || 500).json({
          status: 'ERROR',
          error_message: error.message,
          predictions: [],
          result: null
        });
      }
    });
  });
